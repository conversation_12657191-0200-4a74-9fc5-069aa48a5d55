<template>
  <div
    v-if="visible"
    class="custom-context-menu"
    :style="{ left: left + 'px', top: top + 'px' }"
  >
    <el-menu class="el-menu-vertical-demo" :collapse="true">
      <template v-for="(item, index) in filteredMenu" :key="index">
        <el-sub-menu v-if="item.children?.length > 0" :index="item.action">
          <template #title>
            <div>{{ item.label }}</div>
          </template>
          <el-menu-item
            v-for="(v, i) in item.children"
            :key="i"
            :index="v.action"
            @click="handleMenuClick(v.action)"
            >{{ v.label }}
          </el-menu-item>
        </el-sub-menu>
        <el-menu-item v-else :index="item.action">
          <div @click="handleMenuClick(item.action)">{{ item.label }}</div>
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<script>
export default {
  name: "ContextMenu",
  data() {
    return {
      visible: false,
      left: 0,
      top: 0,
      currentNode: null,
      menuData: [
        {
          label: "新建数据集",
          action: "addDataset",
          level: 2,
        },
        {
          label: "删除数据集",
          action: "removeDataset",
          level: 3,
        },
        { label: "编辑", action: "edit", level: 1 },
      ],
      projectId: "",
    };
  },
  computed: {
    filteredMenu() {
      return this.menuData.filter((item) => {
        // 添加额外的权限/状态判断逻辑
        return true;
      });
    },
  },
  methods: {
    show(event, node, projectId, menuData) {
      this.menuData = menuData;
      this.visible = true;
      this.left = event.clientX;
      this.top = event.clientY;
      this.currentNode = node;
      this.projectId = projectId;
      this.$nextTick(() => {
        this.checkPosition();
      });
    },

    hide() {
      this.visible = false;
      this.currentNode = null;
    },

    handleMenuClick(action) {
      this.$emit("menu-action", action, this.currentNode, this.projectId);
      this.hide();
    },

    checkPosition() {
      const menuElement = this.$el;
      if (!menuElement) return;

      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;
      const menuWidth = menuElement.offsetWidth;
      const menuHeight = menuElement.offsetHeight;

      if (this.left + menuWidth > windowWidth) {
        this.left = windowWidth - menuWidth;
      }
      if (this.top + menuHeight > windowHeight) {
        this.top = windowHeight - menuHeight;
      }
    },
  },

  mounted() {
    document.addEventListener("click", this.hide);
  },

  beforeUnmount() {
    document.removeEventListener("click", this.hide);
  },
};
</script>

<style scoped>
.custom-context-menu {
  position: fixed;
  background: #fff;
  border: 1px solid #ddd;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 2000;
  min-width: 120px;
}

.menu-item {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.menu-item:hover {
  background-color: #f5f7fa;
}
.el-menu--collapse {
  width: auto;
}
.el-menu-item {
  height: 40px;
  line-height: 40px;
}
:deep(.el-sub-menu__title) {
  height: 40px;
  line-height: 40px;
}
</style>
