<template>
  <div class="three-container">


    <!-- 控制面板 -->
    <div id="controls" :class="{ 'collapsed': isControlsCollapsed }">
      <!-- 收起/展开按钮 -->
      <div class="controls-toggle" @click="toggleControls" :title="isControlsCollapsed ? '展开控制面板' : '收起控制面板'">
        <span v-if="isControlsCollapsed" style="font-size: 20px;">▶</span>
        <span v-else style="font-size: 20px;">◀</span>
      </div>

      <div>
        <label for="viewMode">视图模式: </label>
        <select id="viewMode" v-model="viewMode">
          <option value="south">正南方</option>
          <option value="north">正北方</option>
        </select>
      </div>

      <!-- 添加模型间距控制 -->
      <!-- <div>
        <label for="spacing">模型间距: </label>
        <input type="range" id="spacing" v-model="modelSpacing" min="0" max="2000" step="10" />
        <span>{{ modelSpacing }}</span>
      </div> -->
      
      <!-- 添加高度系数控制 -->
      <div>
        <label for="heightScale">高度缩放: </label>
        <input type="range" id="heightScale" v-model="heightScale" min="1" max="10" step="1" />
        <span>{{ heightScale }}x</span>
      </div>
      
      <!-- 高度缩放快速预设 -->
      <div style="margin-top: 5px;">
        <button @click="heightScale = 1" style="margin-right: 5px; padding: 2px 8px; font-size: 12px; background-color: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer;">1x</button>
        <button @click="heightScale = 2" style="margin-right: 5px; padding: 2px 8px; font-size: 12px; background-color: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer;">2x</button>
        <button @click="heightScale = 5" style="margin-right: 5px; padding: 2px 8px; font-size: 12px; background-color: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer;">5x</button>
        <button @click="heightScale = 10" style="padding: 2px 8px; font-size: 12px; background-color: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer;">10x</button>
      </div>

      <!-- 地层模型切割控制 -->
      <div>
        <label>
          <input type="checkbox" v-model="clippingEnabled" @change="updateClipping" />
          启用地层切割
        </label>
      </div>
      <div v-if="clippingEnabled">
        <div>
          <label for="xMin">X最小值: </label>
          <input type="number" id="xMin" v-model.number="clippingBounds.xMin" @input="updateClipping" step="100" />
        </div>
        <div>
          <label for="xMax">X最大值: </label>
          <input type="number" id="xMax" v-model.number="clippingBounds.xMax" @input="updateClipping" step="100" />
        </div>
        <div>
          <label for="zMin">Z最小值: </label>
          <input type="number" id="zMin" v-model.number="clippingBounds.zMin" @input="updateClipping" step="100" />
        </div>
        <div>
          <label for="zMax">Z最大值: </label>
          <input type="number" id="zMax" v-model.number="clippingBounds.zMax" @input="updateClipping" step="100" />
        </div>
      </div>

      <!-- 摄像头定位控制 -->
      <div style="margin-top: 10px; border-top: 1px solid #666; padding-top: 10px;">
        <div style="font-weight: bold; margin-bottom: 5px; color: #fff; font-size: 14px;">摄像头定位:</div>
        <div>
          <label for="targetX">目标X: </label>
          <input type="number" id="targetX" v-model.number="cameraTarget.x" step="100" style="width: 80px;" />
        </div>
        <div>
          <label for="targetY">目标Y: </label>
          <input type="number" id="targetY" v-model.number="cameraTarget.y" step="100" style="width: 80px;" />
        </div>
        <div>
          <label for="targetZ">目标Z: </label>
          <input type="number" id="targetZ" v-model.number="cameraTarget.z" step="100" style="width: 80px;" />
        </div>
        <div>
          <label for="cameraDistance">相机距离: </label>
          <input type="number" id="cameraDistance" v-model.number="cameraDistance" min="100" max="10000" step="100" style="width: 80px;" />
        </div>
        <div style="margin-top: 5px;">
          <button @click="flyToTarget" style="padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; font-weight: bold;">定位到目标</button>
          <button @click="resetCamera" style="margin-left: 5px; padding: 5px 10px; background: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer; font-weight: bold;">重置视图</button>
        </div>
      </div>

    </div>

    <!-- 统计信息 -->
    <!-- <div id="stats" v-html="statsInfo"></div> -->

    <!-- 颜色图例 -->
    <div id="colorLegend" v-if="isLegendVisible" :style="{ background: colorLegendStyle }"></div>
    <div id="colorLabels" v-if="isLegendVisible">
      <div id="maxHeightLabel">{{ maxHeightLabel }}</div>
      <div id="minHeightLabel">{{ minHeightLabel }}</div>
    </div>

  </div>
</template>

<script>

import axios from "axios";
import { defineComponent, onMounted, onBeforeUnmount, ref, watch } from 'vue';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { Lut } from 'three/examples/jsm/math/Lut.js';
// 导入外部JS文件
import { Box3Grid, TOOLS, VarTubeGeometry, LineD3, AreaD3 } from './gr3D.js';



// 移除TypeScript接口定义，改用注释说明对象结构
/**
 * @typedef {Object} GridInfo
 * @property {number} cols - 列数
 * @property {number} rows - 行数
 * @property {number} xMin - X最小值
 * @property {number} xMax - X最大值
 * @property {number} yMin - Y最小值
 * @property {number} yMax - Y最大值
 * @property {number} nullValue - 空值
 * @property {number} rotation - 旋转角度
 * @property {number} xStep - X步长
 * @property {number} yStep - Y步长
 * @property {number[][]} data - 数据数组
 */

export default defineComponent({
  name: 'ThreeDShows',
  setup() {
    let camera, renderer, scene, controls,XCoordinates,YCoordinates;
    let AllGroup=new THREE.Group();
    const points = ref([]);
    const vWebApiUrl = window.location.protocol + "//" + window.location.host + "/api";
    const viewMode = ref('south'); // 默认正南方
    const statsInfo = ref('');
    const filePath = ref('');
    const currentMesh = ref(null);
    const lastGridInfo = ref(null);

    const terrainMeshes = ref([]); // 添加数组来存储所有地层模型
    // 添加模型间距控制变量
    const modelSpacing = ref(0); // 默认间距值，可以根据需要调整
    const currentModelIndex = ref(0); // 当前模型索引，用于计算偏移量
    // 添加高度系数控制变量
    const heightScale = ref(1.0);
    // 高度标签
    const maxHeightLabel = ref('最高: 0');
    const height80Label = ref('80%: 0');
    const height60Label = ref('60%: 0');
    const height40Label = ref('40%: 0');
    const height20Label = ref('20%: 0');
    const minHeightLabel = ref('最低: 0');

    // 添加导入模型相关的变量
    const importModelDialogVisible = ref(false);
    const isImportingModel = ref(false);
    const modelFileList = ref([]);
    const importModelForm = ref({
      modelFile: null
    });
    const importModelRules = {
      modelFile: [
        { required: true, message: '请选择模型文件', trigger: 'change' }
      ]
    };

    // 添加颜色图例相关的响应式变量
    const colorLegendStyle = ref('');
    const isLegendVisible = ref(false);

    // 添加地层模型切割相关变量
    const clippingEnabled = ref(false);
    const clippingBounds = ref({ xMin: 0, xMax: 0, zMin: 0, zMax: 0 });
    const clippingPlanes = ref([]);

    // 添加插值成像相关的响应式变量
    const interpolationMeshes = ref([]); // 存储插值成像网格

    // 添加摄像头定位相关的变量
    const cameraTarget = ref({ x: 0, y: 0, z: 0 });
    const cameraDistance = ref(1000);

    // 添加控制面板收起/展开状态
    const isControlsCollapsed = ref(false);

    class CustomCurve extends THREE.Curve {
      constructor(points) {
        super();
        this.points = points;
      }

      getPoint(t) {
        const index = Math.floor(t * (this.points.length - 1));
        const point1 = this.points[index];
        const point2 = this.points[Math.min(index + 1, this.points.length - 1)];

        const alpha = t * (this.points.length - 1) - index; // 计算插值因子
        return new THREE.Vector3(
          point1.x * (1 - alpha) + point2.x * alpha,
          point1.y * (1 - alpha) + point2.y * alpha,
          point1.z * (1 - alpha) + point2.z * alpha
        );
      }
    }

    const init = () => {
      const container = document.querySelector('.three-container');
      const [width, height] = [container.offsetWidth, container.offsetHeight];

      console.log('【调试】初始化3D场景');
      
      // 创建渲染器
      renderer = new THREE.WebGLRenderer({ antialias: true });
      renderer.setSize(width, height);
      renderer.setClearColor(0x000000); // 改为黑色背景
      renderer.localClippingEnabled = true; // 启用本地裁剪
      container.appendChild(renderer.domElement);

      // 创建场景
      scene = new THREE.Scene();
      scene.background = new THREE.Color(0x000000); // 改为黑色背景

      //新缩放
      scene.add(AllGroup);

      // 创建相机
      camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 10000);
      camera.position.set(0, 0, 20);
      camera.lookAt(0, 0, 0);

      // 添加轨道控制器
      controls = new OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.05;

      // 添加环境光和平行光
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
      scene.add(ambientLight);

      const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
      directionalLight.position.set(1, 1, 1);
      scene.add(directionalLight);

      // 窗口大小调整处理
      window.addEventListener('resize', onWindowResize);
      
      console.log('【调试】场景初始化完成');
    };

    const createCurve = (points, color, wellboreId, curveType, caliper) => {
      // 创建唯一标识符
      const curveId = `${wellboreId}_${curveType}`;
      console.log('创建时的唯一标识符号'+curveId)      
      var curveMesh = null;
      
        // 创建路径
        const curve = new CustomCurve(points);
        // 创建管道几何体
        const geometry = new THREE.TubeGeometry(
          curve,
          64,    // 分段数
          caliper,   // 增加半径，使曲线更粗
          8,     // 径向分段数
          false  // 是否闭合
        );

        // 创建材质
        const material = new THREE.MeshPhongMaterial({
          color: color,
          side: THREE.DoubleSide,
          transparent: true,
          opacity: 1.0,  // 增加不透明度
          shininess: 100,
          emissive: color,  // 添加自发光效果
          emissiveIntensity: 0.5  // 设置自发光强度
        });          
        // 创建网格
         curveMesh = new THREE.Mesh(geometry, material);
      



      // 添加自定义属性来标识这条曲线
      curveMesh.userData.curveId = curveId;
      // 新增：存储原始点、wellboreId、curveType
      curveMesh.userData.points = points.map(p => ({
        ...p,
        originalY: p.originalY || p.y // 保存原始Y值，用于缩放时还原
      }));
      curveMesh.userData.wellboreId = wellboreId;
      curveMesh.userData.curveType = curveType;
      // 添加显示模式和高度缩放系数
      curveMesh.userData.displayMode = 'normal'; 
      curveMesh.userData.heightScale = heightScale.value; // 记录当前高度缩放值
      curveMesh.userData.caliper = caliper; // 记录当前曲线粗细
      curveMesh.userData.originalColor = color; // 保存原始颜色信息

      scene.add(curveMesh);
      //新缩放
      //AllGroup.add(curveMesh);      
    };

    // 修改 updateCurve 方法
    const updateCurve = (newPoints, color, wellboreId, curveType, caliper) => {
      if(!caliper){
        caliper = 15;
      }
      createCurve(newPoints, color, wellboreId, curveType, caliper);
    };

    // 添加新的 removeCurve 方法
    const removeCurve = (wellboreId, curveType) => {
      const curveId = `${wellboreId}_${curveType}`;
      console.log('删除时的唯一标识符'+curveId)
      
      try {
        // 创建待删除对象的临时数组，避免在遍历过程中修改集合
        const objectsToRemove = [];
        
        // 先收集相关的白线（Line对象）
        const lineIdsToRemove = [`${curveId}_inline`, `${curveId}_outline`];
        scene.children.forEach(child => {
          if (child instanceof THREE.Line && 
              child.userData && 
              child.userData.lineId && 
              lineIdsToRemove.includes(child.userData.lineId)) {
            objectsToRemove.push(child);
          }
        });
        
        // 收集场景中所有相关的网格对象
        scene.traverse((object) => {
          // 检查是否是网格对象且具有匹配的curveId
          if (object instanceof THREE.Mesh && 
              object.userData && 
              object.userData.curveId === curveId) {
            objectsToRemove.push(object);
          }
        });
        
        // 收集所有相关的Line对象
        scene.traverse((object) => {
          if (object instanceof THREE.Line && 
              object.userData && 
              object.userData.lineId && 
              object.userData.lineId.includes(curveId)) {
            objectsToRemove.push(object);
          }
        });
        
        // 只有在确实找到了对象后才移除它们
        if (objectsToRemove.length > 0) {
          console.log(`找到 ${objectsToRemove.length} 个与曲线 ${curveId} 相关的对象`);
          
          // 安全地移除所有收集到的对象
          objectsToRemove.forEach(object => {
            try {
              // 从场景中移除对象
              scene.remove(object);
              
              // 释放几何体和材质资源
              if (object.geometry) object.geometry.dispose();
              
              if (object.material) {
                if (Array.isArray(object.material)) {
                  object.material.forEach(m => m.dispose());
                } else {
                  object.material.dispose();
                }
              }
            } catch (error) {
              console.error(`移除对象时出错 (${curveId}):`, error);
            }
          });
        } else {
          console.log(`未找到与曲线 ${curveId} 相关的对象`);
        }
      } catch (error) {
        console.error(`removeCurve全局错误 (${curveId}):`, error);
      }
    };

    // 添加直接创建2D曲线的函数
    const create2DCurve = (points, color, wellboreId, curveType, thickness, curveId) => {
      try {
        console.log('创建2D曲线:', curveId);
        
        // 验证点数据的有效性并安全创建曲线
        const validPoints = points.filter(p => p && typeof p === 'object' && 'x' in p && 'y' in p && 'z' in p);
        if (validPoints.length < 2) {
          console.error(`曲线 ${curveId} 的有效点数量不足`);
          return;
        }
        
        // 创建曲线
        const curve = new THREE.CatmullRomCurve3(
          validPoints.map(el => new THREE.Vector3(el.x, el.y, el.z))
        );
        
        // 设置参数
        var radiusArr = [];
        var toMin = 3;
        var toMax = 150;
        var dataMin = Infinity;
        var dataMax = -Infinity;
        
        // 遍历数据点，安全地获取modelData
        for (let i = 0; i < validPoints.length; i++) {
          if (validPoints[i].modelData !== undefined) {
            const value = validPoints[i].modelData;
            dataMin = Math.min(dataMin, value);
            dataMax = Math.max(dataMax, value);
          }
        }
        
        // 确保最大最小值有效
        if (!isFinite(dataMin) || !isFinite(dataMax)) {
          dataMin = 0;
          dataMax = 1;
        }
        if (dataMin === dataMax) {
          dataMax = dataMin + 1;
        }

        var valuesArr = [dataMin, dataMax];
        var mapValuesArr = [1*dataMin, 3*dataMax];
        
        // 添加直径数据
        for (var a = 0; a < validPoints.length; a++) {
          const modelData = validPoints[a].modelData !== undefined ? validPoints[a].modelData : 
            (curveType === '1' || curveType === '2' ? thickness * 5 : dataMin);
          radiusArr.push([a, modelData]);
        }
        
        // 颜色
        const LUT = new Lut('rainbow', 128);
        LUT.setMax(dataMax);
        LUT.setMin(dataMin);
        
        // 材质
        var _material = new THREE.LineDashedMaterial({
          color: '#ffffff',
          dashSize: 1,
          gapSize: 1
        });
        
        const scaledPoints = validPoints.map(p => new THREE.Vector3(p.x, p.y, p.z));
        const scaledCurve = new THREE.CatmullRomCurve3(scaledPoints);
        const curveLength = scaledCurve.getLength();
        
        if (curveLength <= 0) {
          console.error(`曲线 ${curveId} 的长度无效`);
          return;
        }
        
        // 区域图
        var AreaData = new AreaD3({
          line: scaledCurve,
          wellLength: curveLength,
          toMin,
          toMax,
          dataMin,
          dataMax,
          lut: LUT,
          arrRadius: radiusArr,
          angle: 0
        });
        
        // 创建线条
        done(AreaData.inlinePoints, _material, curveId + "_inline");
        
        // 创建几何体和网格
        var _areageo = new THREE.BufferGeometry();
        _areageo.setIndex(AreaData.areaIndexs);
        _areageo.setAttribute('position', new THREE.Float32BufferAttribute(AreaData.areaPoints, 3));
        _areageo.setAttribute('normal', new THREE.Float32BufferAttribute(AreaData.areaNormals, 3));
        _areageo.setAttribute('color', new THREE.Float32BufferAttribute(AreaData.areaColors, 3));

        var newMesh = new THREE.Mesh(_areageo, new THREE.MeshBasicMaterial({
          side: THREE.DoubleSide,
          vertexColors: true,
        }));
        
        // 添加自定义属性
        newMesh.userData = {
          curveId: curveId,
          points: validPoints.map(p => ({
            ...p,
            originalY: p.originalY || p.y
          })),
          wellboreId: wellboreId,
          curveType: curveType,
          displayMode: '2D',
          heightScale: heightScale.value,
          caliper: thickness,
          originalColor: color
        };
        
        scene.add(newMesh);
        console.log('2D曲线创建完成:', curveId);
        
      } catch (error) {
        console.error(`创建2D曲线时出错 (${curveId}):`, error);
      }
    };

    // 添加直接创建3D曲线的函数
    const create3DCurve = (points, color, wellboreId, curveType, thickness, curveId,minval,maxval,minCalibration,maxCalibration,minColor,maxColor) => {
      try {
        console.log('创建3D曲线:', curveId);
        
        // 验证点数据的有效性
        const validPoints = points.filter(p => p && typeof p === 'object' && 'x' in p && 'y' in p && 'z' in p);
        if (validPoints.length < 2) {
          console.error(`曲线 ${curveId} 的有效点数量不足`);
          return;
        }
        
        // 创建曲线
        const curve = new THREE.CatmullRomCurve3(
          validPoints.map(el => new THREE.Vector3(el.x, el.y, el.z))
        );
        
        let _mesh;
        
        // 对于轨迹数据，强制使用 TubeGeometry
        if (curveType === '0' || curveType === '1') {
          const tubeGeometry = new THREE.TubeGeometry(curve, 64, thickness, 8, false);
          const tubeMaterial = new THREE.MeshPhongMaterial({
            color: color,
            side: THREE.DoubleSide,
            transparent: true,
            opacity: 1.0,
            shininess: 100,
            emissive: color,
            emissiveIntensity: 0.2
          });
          _mesh = new THREE.Mesh(tubeGeometry, tubeMaterial);
        } else {
          // 对于其他类型的曲线，使用VarTubeGeometry
          const radiusArr = [];
          let dataMin = Infinity;
          let dataMax = -Infinity;
          let valMin=Infinity;
          let valMax=-Infinity
          
          // 计算数据范围
          for (let i = 0; i < validPoints.length; i++) {
            if (validPoints[i].modelData !== undefined) {
              const value = validPoints[i].modelData;
              dataMin = Math.min(dataMin, value);
              dataMax = Math.max(dataMax, value);
            }
          }
          
          if (!isFinite(dataMin) || !isFinite(dataMax)) {
            dataMin = 0;
            dataMax = 1;
          }
          if (dataMin === dataMax) {
            dataMax = dataMin + 1;
          }
          
          // 计算线性映射的 k 和 b
          var k = (maxCalibration - minCalibration) / (maxval - minval);
          var b = minCalibration - k * minval;

          // 添加直径数据，使用线性映射
          for (var a = 0; a < validPoints.length; a++) {
            const modelData = validPoints[a].modelData !== undefined ? validPoints[a].modelData : dataMin;            
            const mappedValue = k * modelData + b;
            valMin=Math.min(valMin, mappedValue);
            valMax=Math.max(valMax,mappedValue)
            radiusArr.push([a, mappedValue]);
          }
          
          // 创建自定义颜色映射
          let LUT;
          if (minColor && maxColor) {
            // 解析十六进制颜色
            const parseHexColor = (hex) => {
              // 支持带#和不带#的十六进制颜色格式
              const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
              return result ? {
                r: parseInt(result[1], 16) / 255,
                g: parseInt(result[2], 16) / 255,
                b: parseInt(result[3], 16) / 255
              } : null;
            };
            
            const minColorRGB = parseHexColor(minColor);
            const maxColorRGB = parseHexColor(maxColor);
            
            
            if (minColorRGB && maxColorRGB) {
              // 创建自定义颜色数组
              const colors = [];
              for (let i = 0; i < 128; i++) {
                const t = i / 127; // 0到1的插值因子
                const color = {
                  r: minColorRGB.r + (maxColorRGB.r - minColorRGB.r) * t,
                  g: minColorRGB.g + (maxColorRGB.g - minColorRGB.g) * t,
                  b: minColorRGB.b + (maxColorRGB.b - minColorRGB.b) * t
                };
                colors.push(color);
              }
              
              // 使用rainbow作为基础，然后手动设置颜色
              LUT = new Lut('rainbow', 128);
              // 手动设置颜色映射
              for (let i = 0; i < colors.length; i++) {
                const color = colors[i];
                LUT.lut[i] = new THREE.Color(color.r, color.g, color.b);
              }
            } else {
              // 如果颜色解析失败，使用默认的rainbow
              LUT = new Lut('rainbow', 128);
            }
          } else {
            // 使用默认的rainbow颜色映射
            LUT = new Lut('rainbow', 128);
          }
          
          // 设置数值范围
          if (valMin && valMax) {
            LUT.setMax(valMax);
            LUT.setMin(valMin);
          } else {
            LUT.setMax(dataMax);
            LUT.setMin(dataMin);
          }
                    
          var _geo = new VarTubeGeometry(curve, radiusArr, 20, [valMin, valMax], [valMin*1, valMax*3], LUT);
          //var _geo = new VarTubeGeometry(curve, radiusArr, 20, [dataMin, dataMax], [dataMin*1, dataMax*3], LUT);

          var _baseMaterial = new THREE.MeshPhongMaterial({
            side: THREE.DoubleSide,
            vertexColors: true,
          });
          _mesh = new THREE.Mesh(_geo, _baseMaterial);
        }
        
        // 添加必要的userData属性
        _mesh.userData = {
          curveId: curveId,
          points: validPoints.map(p => ({
            ...p,
            originalY: p.originalY || p.y
          })),
          wellboreId: wellboreId,
          curveType: curveType,
          displayMode: '3D',
          heightScale: heightScale.value,
          caliper: thickness,
          originalColor: color,
          // 添加3D模式所需的参数
          minVal: minval,
          maxVal: maxval,
          minCalibration: minCalibration,
          maxCalibration: maxCalibration,
          minColor: minColor,
          maxColor: maxColor
        };
        
        scene.add(_mesh);
        console.log('3D曲线创建完成:', curveId);
        
      } catch (error) {
        console.error(`创建3D曲线时出错 (${curveId}):`, error);
      }
    };

    const onWindowResize = () => {
      const container = document.querySelector('.three-container');
      const [width, height] = [container.offsetWidth, container.offsetHeight];
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };

    const animate = () => {
      requestAnimationFrame(animate);
      if (controls) controls.update();

      // 关键：每帧都调用，处理原始立方体网格
      const wireCube = scene.getObjectByName('WireCubeGrid');
      if (wireCube && wireCube.visible) {
        updateWireCubeHideNearest3Faces(wireCube, camera);
      }
      
      // 处理切割后的立方体网格
      const cuttingWireCube = scene.getObjectByName('CuttingWireCubeGrid');
      if (cuttingWireCube) {
        updateWireCubeHideNearest3Faces(cuttingWireCube, camera);
      }

      if (renderer && scene && camera) renderer.render(scene, camera);
    };

    const cleanup = () => {
      window.removeEventListener('resize', onWindowResize);
      if (renderer) {
        renderer.dispose();
      }
      if (scene) {
        // 清理所有地层模型
        terrainMeshes.value.forEach(mesh => {
          if (mesh) {
            scene.remove(mesh);
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) {
              if (Array.isArray(mesh.material)) {
                mesh.material.forEach(material => material.dispose());
              } else {
                mesh.material.dispose();
              }
            }
          }
        });
        terrainMeshes.value = []; // 清空数组
        
        // 清理所有插值成像模型
        interpolationMeshes.value.forEach(mesh => {
          if (mesh) {
            scene.remove(mesh);
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) {
              if (Array.isArray(mesh.material)) {
                mesh.material.forEach(material => material.dispose());
              } else {
                mesh.material.dispose();
              }
            }
          }
        });
        interpolationMeshes.value = []; // 清空数组
        
        // 清理场景中的所有对象，包括坐标系和切割立方体网格
        while(scene.children.length > 0) {
          const object = scene.children[0];
          if (object.geometry) object.geometry.dispose();
          if (object.material) {
            if (Array.isArray(object.material)) {
              object.material.forEach(material => material.dispose());
            } else {
              object.material.dispose();
            }
          }
          scene.remove(object);
        }
      }
    };

    const normalizePoints = (points) => {


      return points.map(point => ({
        x: point.dispE ,
        y: -point.tvd * heightScale.value,  // 使用负值是因为THREE.js中Y轴向上为正
        z: point.dispN,
        modelData: point.modelData,
        originalY: -point.tvd  // 保存原始Y值（未应用缩放的）
      }));
    };

    const getDesignCurve = async (wellboreId, curveType) => {
      try {
        const requestUrl = `${vWebApiUrl}/visual3D/project/GetDesignCurve?wellboreId=${wellboreId}&curveType=${curveType}`;
        const response = await axios.post(requestUrl);
        if (response.data?.success) {
          const points = normalizePoints(response.data.data.map(point => ({
            dispE: point.dispE,
            tvd: point.tvd,
            dispN: point.dispN
          })));

          console.log('轨迹曲线点数据:', points);          
          if (curveType === '0') {
            updateCurve(points, 0x00ff00, wellboreId, curveType,response.data.data[0].caliper);
          }
          else if (curveType === '1') {
            updateCurve(points, 0xff0000, wellboreId, curveType,response.data.data[0].caliper);
          }
        } else {
          throw new Error(response.data?.message || '获取设计曲线失败');
        }
      } catch (error) {
        console.error('获取设计曲线错误:', error);
        throw error;
      }
    };
    
    const getChannelData = async (wellboreId, channelId) => {
      try {
        const requestUrl = `${vWebApiUrl}/visual3D/project/GetChannelData?wellboreId=${wellboreId}&channelId=${channelId}`;
        const response = await axios.post(requestUrl);
        if (response.data?.success) {
          const points = normalizePoints(response.data.data.map(point => ({
            dispE: point.dispE,
            tvd: point.tvd,
            dispN: point.dispN,
            modelData: point.modelData
          })));          

          console.log('曲线点数据:', points);
          updateCurve(points, 0xff0000, channelId, 99);
        } else {
          throw new Error(response.data?.message || '获取数据集下的曲线错误');
        }
      }
      catch (error) {
        console.error('获取数据集下的曲线错误:', error);
        throw error;
      }
    }


    // 修改parseZMAPFile方法的类型声明
    const parseZMAPFile = (content, extension) => {
      const lines = content.split('\n');
      const cleanLines = lines.map(line => line.trim());

      let headerEndIndex = null;
      for (let i = 0; i < cleanLines.length; i++) {
        if (cleanLines[i] === '@') {
          headerEndIndex = i;
          break;
        }
      }
      if (headerEndIndex === null) {
        throw new Error("未找到 header 结束标记 '@'");
      }

      let gridInfoLine = '';
      let gridStepLine = '';
      let gridNullValueLine = '';
      for (let i = 0; i < headerEndIndex; i++) {
        if (cleanLines[i].startsWith('@')) {
          gridNullValueLine = cleanLines[i + 1];
          gridInfoLine = cleanLines[i + 2];
          gridStepLine = cleanLines[i + 3];
          break;
        }
      }
      if (!gridInfoLine) {
        throw new Error("未找到网格信息行");
      }
      if (!gridStepLine) {
        throw new Error("未找到网格间距信息行");
      }

      const gridNullValueInfoParts = gridNullValueLine.split(',').map(s => s.trim()).filter(s => s !== '');
      const gridInfoParts = gridInfoLine.split(',').map(s => s.trim()).filter(s => s !== '');
      const gridStepParts = gridStepLine.split(',').map(s => s.trim()).filter(s => s !== '');
      if (gridInfoParts.length < 6) {
        throw new Error("网格信息不足，请检查文件格式！");
      }

      const gridInfo = {
        cols: parseInt(gridInfoParts[0]),
        rows: parseInt(gridInfoParts[1]),
        xMin: parseFloat(gridInfoParts[4]),
        xMax: parseFloat(gridInfoParts[5]),
        yMin: parseFloat(gridInfoParts[2]),
        yMax: parseFloat(gridInfoParts[3]),
        minHeight: 0,
        nullValue: 0.1e31,
        rotation: 0,
        xStep: 0,
        yStep: 0,
        data: []
      };

      //这种是自动计算
      // gridInfo.xStep = (gridInfo.xMax - gridInfo.xMin) / (gridInfo.cols - 1);
      // gridInfo.yStep = (gridInfo.yMax - gridInfo.yMin) / (gridInfo.rows - 1);

      //这种是文件提取
      gridInfo.xStep = parseFloat(gridStepParts[1]);
      gridInfo.yStep = parseFloat(gridStepParts[2]);

      //缺省值
      gridInfo.nullValue = parseFloat(gridNullValueInfoParts[1] == "" ? gridNullValueInfoParts[2] : gridNullValueInfoParts[1]);      
      const dataValues = [];
      for (let i = headerEndIndex + 1; i < cleanLines.length; i++) {
        const line = cleanLines[i];
        if (line === '') continue;
        const parts = line.split(/\s+/).filter(part => part !== '');
        if(extension==='grd'){
         for (const part of parts) {
            try {
              const value = parseFloat(part);                                   
              dataValues.push(-value);                      
            } catch (e) {
              console.warn("无法转换数据:", part);
            }
          }
        }
        else{
          for (const part of parts) {
            try {
              const value = parseFloat(part);                                   
              dataValues.push(value);                      
            } catch (e) {
              console.warn("无法转换数据:", part);
            }
          }
        }

      }

      const expectedCount = gridInfo.rows * gridInfo.cols;
      const actualCount = dataValues.length;
      console.log(`预期数据数量: ${expectedCount}, 实际数据数量: ${actualCount}`);
      if (actualCount !== expectedCount) {
        throw new Error(`数据数量与预期不符，预期 ${expectedCount}，实际 ${actualCount}`);
      }

      for (let r = 0; r < gridInfo.rows; r++) {
        gridInfo.data[r] = [];
        for (let c = 0; c < gridInfo.cols; c++) {
          const index = r * gridInfo.cols + c;
          gridInfo.data[r][c] = dataValues[index];
        }
      }

      return gridInfo;
    };

    const parseCPS3File = (content) => {
      const lines = content.split('\n');
      const cleanLines = lines.map(line => line.trim());

      const gridNullValueLine = cleanLines[0];
      const gridInfoLine = cleanLines[2];
      const gridRowAndColInfoLine = cleanLines[3];
      const gridStepLine = cleanLines[4];

      if (!gridInfoLine) {
        throw new Error("未找到网格信息行");
      }
      if (!gridStepLine) {
        throw new Error("未找到网格间距信息行");
      }
      if (!gridRowAndColInfoLine) {
        throw new Error("未找到网格行列信息行");
      }
      if (!gridNullValueLine) {
        throw new Error("未找到网格缺省值信息行");
      }

      const gridNullValueInfoParts = gridNullValueLine.split(/\s+/);
      const gridInfoParts = gridInfoLine.split(/\s+/);
      const gridRowAndColInfoParts = gridRowAndColInfoLine.split(/\s+/);
      const gridStepParts = gridStepLine.split(/\s+/);

      const gridInfo = {
        cols: parseInt(gridRowAndColInfoParts[1]),
        rows: parseInt(gridRowAndColInfoParts[2]),
        xMin: parseFloat(gridInfoParts[1]),
        xMax: parseFloat(gridInfoParts[2]),
        yMin: parseFloat(gridInfoParts[3]),
        yMax: parseFloat(gridInfoParts[4]),
        minHeight: 0,
        nullValue: parseFloat(gridNullValueInfoParts[5]),
        rotation: 0,
        xStep: 0,
        yStep: 0,
        data: []
      };

      gridInfo.xStep = parseFloat(gridStepParts[1]);
      gridInfo.yStep = parseFloat(gridStepParts[2]);

      const dataValues = [];
      for (let i = 6; i < cleanLines.length; i++) {
        const line = cleanLines[i];
        if (line === '') continue;
        const parts = line.split(/\s+/).filter(part => part !== '');
        for (const part of parts) {
          try {
            const value = parseFloat(part);
            dataValues.push(-value);
          } catch (e) {
            console.warn("无法转换数据:", part);
          }
        }
      }

      const expectedCount = gridInfo.rows * gridInfo.cols;
      const actualCount = dataValues.length;
      console.log(`预期数据数量: ${expectedCount}, 实际数据数量: ${actualCount}`);
      if (actualCount !== expectedCount) {
        throw new Error(`数据数量与预期不符，预期 ${expectedCount}，实际 ${actualCount}`);
      }

      for (let r = 0; r < gridInfo.rows; r++) {
        gridInfo.data[r] = [];
        for (let c = 0; c < gridInfo.cols; c++) {
          const index = r * gridInfo.cols + c;
          gridInfo.data[r][c] = dataValues[index];
        }
      }

      return gridInfo;
    };

    const createTerrainMesh = (gridInfo) => {
      // 计算地形尺寸
      // const width = gridInfo.xMax - gridInfo.xMin;
      // const height = gridInfo.yMax - gridInfo.yMin;
      const height = gridInfo.xMax - gridInfo.xMin;
      const width = gridInfo.yMax - gridInfo.yMin;
      console.log('地形尺寸:', { width, height });
      console.log('原始坐标范围:', {
        xMin: gridInfo.xMin,
        xMax: gridInfo.xMax,
        yMin: gridInfo.yMin,
        yMax: gridInfo.yMax
      });

      // 先使用普通的PlaneGeometry创建基础网格，然后我们会修改坐标
      const geometry = new THREE.PlaneGeometry(
        width,
        height,
        gridInfo.cols - 1,
        gridInfo.rows - 1
      );

      // 处理顶点数据
      const positions = geometry.attributes.position.array;
      let minHeight = Infinity;
      let maxHeight = -Infinity;

      // 计算高度范围
      for (let r = 0; r < gridInfo.rows; r++) {
        for (let c = 0; c < gridInfo.cols; c++) {
          const value = gridInfo.data[r][c];
          if (value !== null && !isNaN(value) && Math.abs(value) < gridInfo.nullValue) {
            minHeight = Math.min(minHeight, value);
            maxHeight = Math.max(maxHeight, value);
          }
        }
      }

      console.log('高度范围:', { minHeight, maxHeight });

      // 确保有一定的高度差，避免平面导致着色问题
      if (Math.abs(maxHeight - minHeight) < 0.1) {
        maxHeight = minHeight + 1.0;
        console.log('高度差太小，调整为:', { minHeight, maxHeight });
      }

      // 更新统计信息
      statsInfo.value = `
        网格大小: ${gridInfo.rows} × ${gridInfo.cols}<br>
        X范围: ${gridInfo.xMin.toFixed(2)} - ${gridInfo.xMax.toFixed(2)}<br>
        Y范围: ${gridInfo.yMin.toFixed(2)} - ${gridInfo.yMax.toFixed(2)}<br>
        高度范围: ${minHeight.toFixed(2)} - ${maxHeight.toFixed(2)}<br>
        网格间距: ${gridInfo.xStep} × ${gridInfo.yStep}
        `;

      // 不再使用缩放，直接使用原始高度值
      // 设置useOriginalHeight标志，这样其他函数可以知道我们使用的是原始高度
      const useOriginalHeight = true;

      // 收集一些高度值样本用于调试
      const heightSamples = [];

      // 创建顶点 - 转换为使用真实地理坐标
      // PlaneGeometry默认是中心位于原点，范围是-width/2到width/2，-height/2到height/2
      // 我们需要将其转换为使用原始地理坐标
      const validVertices = new Set(); // 用于存储有效顶点的索引

      for (let i = 0, j = 0; i < positions.length; i += 3, j++) {
        const col = j % gridInfo.cols;
        const row = Math.floor(j / gridInfo.cols);

        // 计算真实地理坐标 - 注意这里使用交换后的height和width
        const x = gridInfo.xMin + (col / (gridInfo.cols - 1)) * height;
        const y = gridInfo.yMin + (row / (gridInfo.rows - 1)) * width;

        // 计算高度值 - 直接使用原始高度值，不再缩放
        const invertedRow = gridInfo.rows - 1 - row;
        const value = gridInfo.data[invertedRow][col];
        let z = minHeight; // 默认使用最小高度

        if (value !== null && !isNaN(value) && Math.abs(value) < gridInfo.nullValue) {
          //z = value; // 直接使用原始高度值
          // 增加高度差异系数          
          z = value * heightScale.value; // 将高度值放大
          validVertices.add(j); // 记录有效顶点

          // 收集前10个有效高度值样本
          if (heightSamples.length < 10) {
            heightSamples.push({
              row, col,
              originalValue: value,
              usedValue: z
            });
          }
        }

        // 更新顶点位置 - 使用实际地理坐标和原始高度
        positions[i] = x;     // X对应原始X
        positions[i + 1] = z; // Y对应原始高度值
        positions[i + 2] = y; // Z对应原始Y
      }


      // 创建三角形索引数组
      const indexArray = [];
      for (let row = 0; row < gridInfo.rows - 1; row++) {
        for (let col = 0; col < gridInfo.cols - 1; col++) {
          const a = row * gridInfo.cols + col;
          const b = a + 1;
          const c = a + gridInfo.cols;
          const d = c + 1;

          // 只有当相关顶点都有效时才创建三角形
          if (validVertices.has(a) && validVertices.has(b) && validVertices.has(c)) {
            indexArray.push(a, b, c);
          }
          if (validVertices.has(b) && validVertices.has(c) && validVertices.has(d)) {
            indexArray.push(b, d, c);
          }
        }
      }

      // 更新几何体的索引
      geometry.setIndex(indexArray);

      // 通知Three.js顶点位置已更新
      geometry.attributes.position.needsUpdate = true;
      geometry.computeVertexNormals();

      // 设置等高线参数 - 确保有合理的等高线间隔
      const contourInterval = Math.max((maxHeight - minHeight) / 20, 0.1);
      const baseContourWidth = Math.max(contourInterval * 0.1, 0.05);


      // 创建着色器材质
      const material = new THREE.ShaderMaterial({
        uniforms: {
          contourInterval: { value: contourInterval },
          contourWidth: { value: baseContourWidth },
          minHeight: { value: minHeight },
          maxHeight: { value: maxHeight }
        },
        // 顶点着色器：计算高度值并传递给片元着色器
        vertexShader: `
                varying float vHeight;
                varying vec4 vPosition;
                uniform float minHeight;
                uniform float maxHeight;
                
                void main() {
                    // 这里使用position.y作为高度值，现在是原始高度
                    vHeight = position.y;
                    vPosition = modelViewMatrix * vec4(position, 1.0);
                    gl_Position = projectionMatrix * vPosition;
                }
            `,
        // 片元着色器：根据高度值计算颜色和等高线
        fragmentShader: `
                uniform float contourInterval;
                uniform float contourWidth;
                uniform float minHeight;
                uniform float maxHeight;
                varying float vHeight;
                varying vec4 vPosition;
                
                void main() {
                    // 计算标准化高度
                    float normalizedHeight = (vHeight - minHeight) / (maxHeight - minHeight);
                    
                    // 确保normalizedHeight在0到1之间
                    normalizedHeight = clamp(normalizedHeight, 0.0, 1.0);
                    
                    // 计算等高线
                    float modHeight = mod(vHeight, contourInterval);
                    float distanceToContour = min(modHeight, contourInterval - modHeight);
                    float contourFactor = smoothstep(0.0, contourWidth, distanceToContour);
                    
                    // 根据高度计算颜色 - 使用更简单的方式，确保能生成颜色
                    vec3 color = vec3(0.0);
                    
                    // 简单的颜色渐变
                    color.r = normalizedHeight;
                    color.g = 1.0 - normalizedHeight;
                    color.b = normalizedHeight * 0.5;
                    
                    // 混合等高线和地形颜色
                    vec3 finalColor = mix(vec3(0.0, 0.0, 0.0), color, contourFactor);
                    
                    // 确保不会出现全黑的情况
                    finalColor = max(finalColor, vec3(0.1));
                    
                    gl_FragColor = vec4(finalColor, 1.0);
                }
            `,
        side: THREE.DoubleSide
      });

      // 创建备选的简单材质 - 使用MeshLambertMaterial
      const lambertMaterial = new THREE.MeshLambertMaterial({
        vertexColors: true,
        side: THREE.DoubleSide
      });

      // 为简单材质添加顶点颜色
      if (!geometry.attributes.color) {
        const colors = [];

        for (let i = 0, j = 0; i < positions.length; i += 3, j++) {
          const col = j % gridInfo.cols;
          const row = Math.floor(j / gridInfo.cols);
          const invertedRow = gridInfo.rows - 1 - row;
          const value = gridInfo.data[invertedRow][col];

          // 默认颜色
          let color = { r: 0.5, g: 0.5, b: 0.5 };

          if (value !== null && !isNaN(value) && Math.abs(value) < gridInfo.nullValue) {
            // 计算标准化高度
            const normalizedHeight = (value - minHeight) / (maxHeight - minHeight);

            // 根据高度设置颜色 - 修改避免纯黑色
            if (normalizedHeight < 0.2) {
              // 从深蓝色开始，而不是黑色
              color = { r: 0, g: 0, b: 0.5 + normalizedHeight * 2.5 };
            } else if (normalizedHeight < 0.4) {
              color = { r: 0, g: (normalizedHeight - 0.2) * 5, b: 1 - (normalizedHeight - 0.2) * 5 };
            } else if (normalizedHeight < 0.6) {
              color = { r: (normalizedHeight - 0.4) * 5, g: 1, b: 0 };
            } else if (normalizedHeight < 0.8) {
              color = { r: 1, g: 1 - (normalizedHeight - 0.6) * 5, b: 0 };
            } else {
              // 修改最高点颜色，使用深红色而不是白色
              //color = { r: 1, g: 0.2 + (normalizedHeight - 0.8) * 4, b: 0.2 + (normalizedHeight - 0.8) * 4 };
              color = { r: 0.8, g: 0.1, b: 0.1 };
            }
          }

          // 添加颜色
          colors.push(color.r, color.g, color.b);
        }

        geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
      }

      // 创建网格 - 直接使用Lambert材质
      const mesh = new THREE.Mesh(
        geometry,
        lambertMaterial
      );

      mesh.userData = {
        baseContourWidth: baseContourWidth,
        originalXMin: gridInfo.xMin,
        originalXMax: gridInfo.xMax,
        originalYMin: gridInfo.yMin,
        originalYMax: gridInfo.yMax,
        useRealCoordinates: true,
        useOriginalHeight: useOriginalHeight,
        minHeight: minHeight,
        maxHeight: maxHeight,
        originalScale: 1.0
      };

      // 如果切割功能已启用，应用切割平面到新创建的模型
      if (clippingEnabled.value && clippingPlanes.value.length > 0) {
        mesh.material.clippingPlanes = clippingPlanes.value;
        mesh.material.clipShadows = true;
        mesh.material.needsUpdate = true;
      }

      return mesh;
    };

    const getCornerCoordinates = (mesh, gridInfo) => {
      // 从模型几何体中直接获取顶点信息
      const geometry = mesh.geometry;
      const positions = geometry.attributes.position.array;

      // 检查是否使用原始高度值
      const useOriginalHeight = mesh.userData.useOriginalHeight || false;

      // 获取高度范围
      const minHeight = mesh.userData.minHeight || 0;
      const maxHeight = mesh.userData.maxHeight || 0;

      // 初始化边界值
      let minX = Infinity, maxX = -Infinity;
      let minY = Infinity, maxY = -Infinity;
      let minZ = Infinity, maxZ = -Infinity;

      // 寻找最接近四个角的顶点
      let leftBottomVertex = { x: 0, y: 0, z: 0 };
      let rightBottomVertex = { x: 0, y: 0, z: 0 };
      let leftTopVertex = { x: 0, y: 0, z: 0 };
      let rightTopVertex = { x: 0, y: 0, z: 0 };

      let minDistLB = Infinity, minDistRB = Infinity, minDistLT = Infinity, minDistRT = Infinity;

      // 遍历所有顶点，找到最接近四个角的顶点
      for (let i = 0; i < positions.length; i += 3) {
        const x = positions[i];
        const z = positions[i + 1];  // 高度值 - 现在是原始高度值
        const y = positions[i + 2];

        // 计算到四个角的距离
        const distLB = Math.sqrt(Math.pow(x - gridInfo.xMin, 2) + Math.pow(y - gridInfo.yMin, 2));
        const distRB = Math.sqrt(Math.pow(x - gridInfo.xMax, 2) + Math.pow(y - gridInfo.yMin, 2));
        const distLT = Math.sqrt(Math.pow(x - gridInfo.xMin, 2) + Math.pow(y - gridInfo.yMax, 2));
        const distRT = Math.sqrt(Math.pow(x - gridInfo.xMax, 2) + Math.pow(y - gridInfo.yMax, 2));

        // 更新最接近左下角的顶点
        if (distLB < minDistLB) {
          minDistLB = distLB;
          leftBottomVertex = { x, y, z };
        }

        // 更新最接近右下角的顶点
        if (distRB < minDistRB) {
          minDistRB = distRB;
          rightBottomVertex = { x, y, z };
        }

        // 更新最接近左上角的顶点
        if (distLT < minDistLT) {
          minDistLT = distLT;
          leftTopVertex = { x, y, z };
        }

        // 更新最接近右上角的顶点
        if (distRT < minDistRT) {
          minDistRT = distRT;
          rightTopVertex = { x, y, z };
        }

        // 记录边界值
        minX = Math.min(minX, x);
        maxX = Math.max(maxX, x);
        minY = Math.min(minY, y);
        maxY = Math.max(maxY, y);
        minZ = Math.min(minZ, z);
        maxZ = Math.max(maxZ, z);
      }

      console.log('最接近四个角的顶点:', {
        leftBottom: leftBottomVertex,
        rightBottom: rightBottomVertex,
        leftTop: leftTopVertex,
        rightTop: rightTopVertex
      });

      // 从模型计算得到的3D坐标（已经是原始高程）
      const modelCoordinates = {
        leftBottom: { x: leftBottomVertex.x, y: leftBottomVertex.y, z: leftBottomVertex.z },
        rightBottom: { x: rightBottomVertex.x, y: rightBottomVertex.y, z: rightBottomVertex.z },
        leftTop: { x: leftTopVertex.x, y: leftTopVertex.y, z: leftTopVertex.z },
        rightTop: { x: rightTopVertex.x, y: rightTopVertex.y, z: rightTopVertex.z }
      };

      // 从模型的userData获取原始地理坐标
      const originalCoordinates = {
        leftBottom: { x: gridInfo.xMin, y: gridInfo.yMin },
        rightBottom: { x: gridInfo.xMax, y: gridInfo.yMin },
        leftTop: { x: gridInfo.xMin, y: gridInfo.yMax },
        rightTop: { x: gridInfo.xMax, y: gridInfo.yMax }
      };

      return {
        modelCoordinates,
        originalCoordinates,
        heightRange: { min: minHeight, max: maxHeight }
      };
    };

    const processFileData = (content, extension) => {
      try {
        console.log('【调试】处理文件数据，当前filePath =', filePath.value);
        
        let gridInfo = null;
        if (extension === "zmap" || extension === "grd") {
          gridInfo = parseZMAPFile(content, extension);
        } else if (extension === "") {
          gridInfo = parseCPS3File(content);
        } else {
          alert("文件格式不支持");
          return;
        }

        // 创建新的地形网格并添加到场景
        const mesh = createTerrainMesh(gridInfo);
        currentMesh.value = mesh;
        
        // 为每个新模型添加垂直偏移
        const verticalOffset = currentModelIndex.value * modelSpacing.value;        
        mesh.position.y = verticalOffset; // 在Y轴方向添加偏移
        
        // 重要：确保设置正确的ID
        mesh.userData.verticalOffset = verticalOffset; // 记录偏移量
        mesh.userData.modelIndex = currentModelIndex.value; // 记录模型索引
        mesh.userData.id = filePath.value; // 记录模型ID，用于后续识别
        
        console.log('【调试】创建的模型ID =', mesh.userData.id);

        // 增加当前模型索引，为下一个模型准备
        currentModelIndex.value++;
        terrainMeshes.value.push(mesh); // 将新创建的网格添加到数组中
        scene.add(mesh);
        
        //新缩放
        //AllGroup.add(mesh);

        console.log('【调试】模型已添加到场景，当前terrainMeshes数量:', terrainMeshes.value.length);

        // 分析模型坐标和文件坐标的差异
        const coordinatesInfo = getCornerCoordinates(mesh, gridInfo);

        // 计算场景中心点 - 使用原始地理坐标的中心
        const centerX = (gridInfo.xMin + gridInfo.xMax) / 2;
        const centerY = (gridInfo.yMin + gridInfo.yMax) / 2;
        const centerZ = (mesh.userData.minHeight + mesh.userData.maxHeight) / 2;  // 使用原始高度的中间值


        // 计算模型尺寸 - 与createTerrainMesh保持一致
        const width = gridInfo.yMax - gridInfo.yMin;
        const height = gridInfo.xMax - gridInfo.xMin;
        const heightRange = mesh.userData.maxHeight - mesh.userData.minHeight;
        const maxDim = Math.max(width, height, heightRange);


        // 根据视图模式设置相机位置
        if (viewMode.value === 'south') {
          // 正南方：相机在Y轴负方向，朝向场景中心
          camera.position.set(centerX, centerZ, gridInfo.yMin - maxDim * 2.5);
          camera.up.set(0, 1, 0);
        } else if (viewMode.value === 'north') {
          // 正北方：相机在Y轴正方向，朝向场景中心
          camera.position.set(centerX, centerZ, gridInfo.yMax + maxDim * 2.5);
          camera.up.set(0, 1, 0);
        }

        // 设置相机看向场景中心
        camera.lookAt(centerX, centerZ, centerY);
        camera.near = 0.1;
        camera.far = maxDim * 20; // 增加远平面距离以适应原始高程范围
        camera.updateProjectionMatrix();

        // 设置轨道控制器的目标为场景中心
        controls.target.set(centerX, centerZ, centerY);
        controls.update();

        // 更新高度标签
        let minHeight = Infinity;
        let maxHeight = -Infinity;

        for (let r = 0; r < gridInfo.rows; r++) {
          for (let c = 0; c < gridInfo.cols; c++) {
            const value = gridInfo.data[r][c];
            if (value !== null && !isNaN(value) && Math.abs(value) < gridInfo.nullValue) {
                minHeight = Math.min(minHeight, value);
                maxHeight = Math.max(maxHeight, value);
              
            }
          }
        }

        gridInfo.minHeight = minHeight;
        lastGridInfo.value = gridInfo;
        const heightDiff = maxHeight - minHeight;
        maxHeightLabel.value = `最高: ${maxHeight.toFixed(1)}`;
        height80Label.value = `80%: ${(minHeight + heightDiff * 0.8).toFixed(1)}`;
        height60Label.value = `60%: ${(minHeight + heightDiff * 0.6).toFixed(1)}`;
        height40Label.value = `40%: ${(minHeight + heightDiff * 0.4).toFixed(1)}`;
        height20Label.value = `20%: ${(minHeight + heightDiff * 0.2).toFixed(1)}`;
        minHeightLabel.value = `最低: ${minHeight.toFixed(1)}`;

        // 生成动态颜色图例
        generateColorLegend(minHeight, maxHeight);

        // 只创建一个立方体网格
        if (!scene.getObjectByName('WireCubeGrid')) {
          const wireCube = createWireCubeGridFromGridInfo({
            ...gridInfo,
            minHeight: gridInfo.minHeight * heightScale.value // 保证缩放生效
          });
          scene.add(wireCube);
        }

        // 初始化切割边界值为模型的实际范围
        if (clippingBounds.value.xMin === 0 && clippingBounds.value.xMax === 0) {
          clippingBounds.value.xMin = gridInfo.xMin;
          clippingBounds.value.xMax = gridInfo.xMax;
          clippingBounds.value.zMin = gridInfo.yMin;
          clippingBounds.value.zMax = gridInfo.yMax;
        }

        // 设置轨道控制器的目标为场景中心
        controls.target.set(centerX, centerZ, centerY);
        controls.update();

        // 初始化摄像头定位目标为场景中心
        cameraTarget.value.x = centerX;
        cameraTarget.value.y = centerZ;
        cameraTarget.value.z = centerY;
        cameraDistance.value = maxDim * 2;

      } catch (error) {
        console.error('处理文件数据失败:', error);
        alert('处理文件数据失败: ' + error.message);
      }
    };

    const getFileExtension = (filename) => {
      const match = filename.match(/\.([^.]+)$/);
      return match ? match[1] : '';
    };

    const downloadAndProcessFile = async (path) => {
      try {
        // 防止重复加载
        if (terrainMeshes.value.some(mesh => mesh && mesh.userData && mesh.userData.id === path)) {
          console.log('【调试】防止重复：模型ID已存在于terrainMeshes中，跳过加载', path);
          return;
        }
        
        // 确保设置filePath值为当前模型的id，用于后续识别
        filePath.value = path;
        console.log('【调试】设置filePath.value =', filePath.value);
        
        path = '/api/oil/GeologicModel/DownloadFile?id=' + path;
        const response = await fetch(path);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 从 Content-Disposition 头获取文件名
        const contentDisposition = response.headers.get('Content-Disposition');
        let filename = '';
        if (contentDisposition) {
          const matches = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(contentDisposition);
          if (matches != null && matches[1]) {
            filename = matches[1].replace(/['"]/g, '');
          }
        }

        const content = await response.text();
        const extension = filename ? getFileExtension(filename) : getFileExtension(path);
        
        // 再次检查是否已存在此ID的模型（可能在异步加载期间已被添加）
        if (terrainMeshes.value.some(mesh => mesh && mesh.userData && mesh.userData.id === filePath.value)) {
          console.log('【调试】文件已下载，但模型已存在，跳过处理');
          return;
        }
        
        processFileData(content, extension);
      } catch (error) {
        console.error('下载或处理文件失败:', error);
        alert('下载或处理文件失败: ' + error.message);
      }
    };
    // 添加一个方法来重新调整所有模型的间距
    const adjustModelSpacing = (newSpacing) => {
      terrainMeshes.value.forEach((mesh, index) => {
        if (mesh) {
          const verticalOffset = index * newSpacing;
          // 检查是否是网格组
          if (mesh instanceof THREE.Group) {
            // 如果是网格组，调整整个组的位置
            mesh.position.y = verticalOffset;
                } else {
            // 如果是地形模型，调整模型位置
            mesh.position.y = verticalOffset;
            mesh.userData.verticalOffset = verticalOffset;
          }
        }
      });
    };

    // 监听模型间距变化
    watch(modelSpacing, (newSpacing) => {
      adjustModelSpacing(newSpacing);
    });

    // 添加对高度系数变化的监听
    watch(heightScale, (newScale, oldScale) => {
      if (!oldScale || newScale === oldScale) return;
      console.log(`高度缩放从 ${oldScale} 变更为 ${newScale}`);
      
      // 计算缩放比例
      const scaleFactor = newScale / oldScale;
      
      // 收集所有需要处理的对象信息，而不是直接操作它们
      const curvesToProcess = [];
      
      // 首先收集需要处理的曲线信息
      // try {
      //   scene.traverse((object) => {
      //     try {
      //       if (object instanceof THREE.Mesh && 
      //           object.userData && 
      //           object.userData.curveId) {
              
      //         const points = object.userData.points;
      //         if (points && Array.isArray(points) && points.length >= 2) {
      //           // 创建一个对象的副本，避免直接修改scene中的对象
      //           curvesToProcess.push({
      //             displayMode: object.userData.displayMode || '3D',
      //             curveId: object.userData.curveId,
      //             wellboreId: object.userData.wellboreId,
      //             curveType: object.userData.curveType,
      //             points: [...points], // 创建点数组的副本
      //             heightScale: object.userData.heightScale || oldScale,
      //             caliper: object.userData.caliper || 10,
      //             originalColor: object.userData.originalColor
      //           });
      //         }
      //       }
      //     } catch (error) {
      //       console.error('收集曲线信息时出错:', error);
      //     }
      //   });
        
      //   console.log(`找到 ${curvesToProcess.length} 条曲线需要处理`);
      // } catch (error) {
      //   console.error('遍历场景对象时出错:', error);
      // }
      
      // 使用setTimeout确保操作是异步的，避免界面阻塞
      if (curvesToProcess.length > 0) {
        setTimeout(() => {
          try {
            // 先移除所有旧曲线
            curvesToProcess.forEach(curve => {
              try {
                // 彻底清理相关的所有对象
                const curveId = curve.curveId;
                const lineIdsToRemove = [`${curveId}_inline`, `${curveId}_outline`];
                
                // 收集所有要删除的对象
                const objectsToRemove = [];
                
                scene.traverse((object) => {
                  // 清理网格对象
                  if (object instanceof THREE.Mesh && 
                      object.userData && 
                      object.userData.curveId === curveId) {
                    objectsToRemove.push(object);
                  }
                  
                  
                  // 清理线条对象
                  if (object instanceof THREE.Line && 
                      object.userData && 
                      object.userData.lineId && 
                      lineIdsToRemove.includes(object.userData.lineId)) {
                    objectsToRemove.push(object);
                  }
                });
                
                // 移除所有收集到的对象
                objectsToRemove.forEach(obj => {
                  scene.remove(obj);
                  if (obj.geometry) obj.geometry.dispose();
                  if (obj.material) {
                    if (Array.isArray(obj.material)) {
                      obj.material.forEach(m => m.dispose());
                    } else {
                      obj.material.dispose();
                    }
                  }
                });
                
              } catch (error) {
                console.error(`移除曲线时出错 (${curve.curveId}):`, error);
              }
            });
            
            // 等待清理完成后再重新创建
            setTimeout(() => {
              try {
                console.log(`开始重新创建 ${curvesToProcess.length} 条曲线`);
                
                // 重新创建所有曲线，直接使用setCurveSetting而不是先createCurve
                curvesToProcess.forEach((curve, index) => {
                  try {
                    console.log(`处理第 ${index + 1} 条曲线: ${curve.curveId}, 模式: ${curve.displayMode}, 颜色: ${curve.originalColor}`);
                    
                    // 统一处理点的Y坐标更新
                    const updatedPoints = curve.points.map(p => {
                      // 确保有originalY值，如果没有则从当前y值推导
                      const originalY = p.originalY !== undefined ? p.originalY : (p.y / (curve.heightScale || oldScale));
                      
                      return {
                        x: p.x,
                        y: originalY * newScale, // 使用原始Y值乘以新缩放
                        z: p.z,
                        modelData: p.modelData,
                        originalY: originalY // 保持原始Y值不变
                      };
                    });
                    
                    // 使用保存的原始颜色
                    const color = curve.originalColor || 0xff0000;
                    
                    console.log(`创建曲线 ${curve.curveId}, 点数: ${updatedPoints.length}, 颜色: ${color.toString(16)}`);
                    
                    // 根据显示模式直接创建对应的曲线，避免重复创建
                    if (curve.displayMode === '2D') {
                      // 直接创建2D模式的曲线
                      create2DCurve(updatedPoints, color, curve.wellboreId, curve.curveType, curve.caliper, curve.curveId);
                    } else if (curve.displayMode === '3D') {
                      // 直接创建3D模式的曲线
                      create3DCurve(updatedPoints, color, curve.wellboreId, curve.curveType, curve.caliper, curve.curveId);
                    } else {
                      // 创建默认模式的曲线
                      createCurve(updatedPoints, color, curve.wellboreId, curve.curveType, curve.caliper);
                    }
                    
                  } catch (error) {
                    console.error(`重新创建曲线 ${curve.curveId} 时出错:`, error);
                  }
                });
                
                console.log('所有曲线重新创建完成');
              } catch (error) {
                console.error('重新创建曲线过程中出错:', error);
              }
            }, 100); // 等待100毫秒确保移除操作完成
          } catch (error) {
            console.error('处理曲线缩放时出错:', error);
          }
        }, 0);
      }

      //新缩放
      //AllGroup.scale.set(1,5,1)

      // 更新所有地层模型
      try {
        terrainMeshes.value.forEach(mesh => {
          try {
            if (mesh && mesh.geometry) {
              const positions = mesh.geometry.attributes.position.array;
              
              // 只缩放Y坐标（高度）
              for (let i = 1; i < positions.length; i += 3) {
                positions[i] *= scaleFactor;
              }
              
              // 更新几何体
              mesh.geometry.attributes.position.needsUpdate = true;
              mesh.geometry.computeVertexNormals();
              mesh.geometry.computeBoundingBox();
              mesh.geometry.computeBoundingSphere();
              
              // 更新userData中的高度信息
              if (mesh.userData) {
                mesh.userData.minHeight *= scaleFactor;
                mesh.userData.maxHeight *= scaleFactor;
              }
            }
          } catch (error) {
            console.error('更新地层模型时出错:', error);
          }
        });
      } catch (error) {
        console.error('更新地层模型过程中出错:', error);
      }
      


      // 更新所有插值成像模型
      try {
        interpolationMeshes.value.forEach(mesh => {
          try {
            if (mesh && mesh.geometry) {
              const positions = mesh.geometry.attributes.position.array;
              
              // 只缩放Y坐标（高度/深度）
              for (let i = 1; i < positions.length; i += 3) {
                positions[i] *= scaleFactor;
              }
              
              // 更新几何体
              mesh.geometry.attributes.position.needsUpdate = true;
              mesh.geometry.computeVertexNormals();
              mesh.geometry.computeBoundingBox();
              mesh.geometry.computeBoundingSphere();
            }
          } catch (error) {
            console.error('更新插值成像模型时出错:', error);
          }
        });
      } catch (error) {
        console.error('更新插值成像模型过程中出错:', error);
      }
      
      // 如果有lastGridInfo，更新立方体网格
      if (lastGridInfo.value) {
        try {
          // 重要：不要修改lastGridInfo的原始数据          
          // 彻底移除所有WireCubeGrid和CuttingWireCubeGrid相关对象
          const removeWireCubes = () => {
            const objectsToRemove = [];
            scene.children.forEach(obj => {
              if (
                (obj.name && (obj.name.includes('WireCubeGrid') || obj.name.includes('CuttingWireCubeGrid')))
                || (obj.userData && (obj.userData.isWireCubeGrid || obj.userData.isCuttingGrid))
              ) {
                objectsToRemove.push(obj);
              }
            });
            objectsToRemove.forEach(obj => {
              scene.remove(obj);
              obj.traverse(child => {
                if (child.geometry) child.geometry.dispose();
                if (child.material) {
                  if (Array.isArray(child.material)) {
                    child.material.forEach(m => m.dispose());
                  } else {
                    child.material.dispose();
                  }
                }
              });
            });
          };
          removeWireCubes();

          // 只生成当前需要的网格，始终用lastGridInfo的原始minHeight乘以当前缩放倍数
          if (clippingEnabled.value) {
            // 生成切割网格
            const cuttingGridInfo = {
              ...lastGridInfo.value,
              xMin: clippingBounds.value.xMin,
              xMax: clippingBounds.value.xMax,
              yMin: clippingBounds.value.zMin,
              yMax: clippingBounds.value.zMax,
              minHeight: lastGridInfo.value.minHeight * newScale // 使用newScale而不是scaleFactor
            };
            const cuttingWireCube = createWireCubeGridFromGridInfo(cuttingGridInfo);
            cuttingWireCube.name = 'CuttingWireCubeGrid';
            cuttingWireCube.userData.isCuttingGrid = true; // 添加标记
            scene.add(cuttingWireCube);
          } else {
            // 生成普通网格
            const newWireCube = createWireCubeGridFromGridInfo({
              ...lastGridInfo.value,
              minHeight: lastGridInfo.value.minHeight * newScale // 使用newScale而不是scaleFactor
            });
            newWireCube.name = 'WireCubeGrid';
            newWireCube.userData.isWireCubeGrid = true;
            scene.add(newWireCube);
          }
        } catch (error) {
          console.error('更新网格时出错:', error);
        }
      }
      
      // 更新相机位置和控制器
      try {
        if (camera && controls) {
          const cameraPosition = camera.position.clone();
          const controlsTarget = controls.target.clone();
          
          // 缩放Y坐标
          cameraPosition.y *= scaleFactor;
          controlsTarget.y *= scaleFactor;
          
          camera.position.copy(cameraPosition);
          controls.target.copy(controlsTarget);
          controls.update();
        }
      } catch (error) {
        console.error('更新相机和控制器时出错:', error);
      }
    });

    // 监听视图模式变化
    watch(viewMode, () => {
      if (!currentMesh.value) return;

      // 获取网格的userData中存储的原始坐标范围
      const xMin = currentMesh.value.userData.originalXMin;
      const xMax = currentMesh.value.userData.originalXMax;
      const yMin = currentMesh.value.userData.originalYMin;
      const yMax = currentMesh.value.userData.originalYMax;
      const minHeight = currentMesh.value.userData.minHeight;
      const maxHeight = currentMesh.value.userData.maxHeight;

      // 计算中心点和尺寸
      const centerX = (xMin + xMax) / 2;
      const centerY = (yMin + yMax) / 2;
      const centerZ = (minHeight + maxHeight) / 2;

      // 与createTerrainMesh保持一致的尺寸计算
      const width = yMax - yMin;
      const height = xMax - xMin;
      const heightRange = maxHeight - minHeight;
      const maxDim = Math.max(width, height, heightRange);

      // 根据视图模式调整相机位置
      if (viewMode.value === 'south') {
        // 正南方：相机在Y轴负方向，朝向场景中心
        camera.position.set(centerX, centerZ, yMin - maxDim * 2.5);
        camera.up.set(0, 1, 0);
      } else if (viewMode.value === 'north') {
        // 正北方：相机在Y轴正方向，朝向场景中心
        camera.position.set(centerX, centerZ, yMax + maxDim * 2.5);
        camera.up.set(0, 1, 0);
      }

      // 设置相机看向场景中心
      camera.lookAt(centerX, centerZ, centerY);
      camera.near = 0.1;
      camera.far = maxDim * 20;
      camera.updateProjectionMatrix();

      // 设置轨道控制器的目标为场景中心
      controls.target.set(centerX, centerZ, centerY);
      controls.update();
    });

    // 监听文件路径变化
    watch(filePath, (newPath) => {
      if (newPath) {
        downloadAndProcessFile(newPath);
      }
    });



    const handleNodeCheck = (id, curveType, isChecked, moduleType, wellboreId,datasetId) => {
      console.log('【调试】handleNodeCheck 被调用:', { id, curveType, isChecked, moduleType, wellboreId,datasetId });
      
      if (moduleType === 6) {
        // 处理轨迹节点
        if (isChecked) {
          getDesignCurve(id, curveType);
        } else {
          removeCurve(id, curveType);
        }
      } 
      else if (moduleType === 10) {
        console.log('【调试】处理插值成像节点');
        // 处理插值成像节点
        if (isChecked) {
          console.log('【调试】添加插值成像');
          getInterpolationImage(id,wellboreId,datasetId);
        } else {
          removeInterpolationImage(id);
        }
      }
      else if (moduleType === 9) {
        // 处理模型节点
        if (isChecked) {
          console.log('【调试】添加地层模型, ID:', id);
          
          // 检查是否已存在相同ID的模型，避免重复添加
          const existingModels = terrainMeshes.value.filter(mesh => 
            mesh && mesh.userData && mesh.userData.id === id
          );
          
          if (existingModels.length > 0) {
            console.log(`【调试】已存在${existingModels.length}个相同ID的模型，不重复添加`);
            terrainMeshes.value.forEach((mesh, index) => {
              if (mesh && mesh.userData && mesh.userData.id === id) {
                mesh.visible=true;
              }
            });
            return; // 已存在模型，不再添加
          }
          
          // 设置filePath，确保模型ID被正确设置
          filePath.value = id;
          // 一次性加载模型
          downloadAndProcessFile(id);
        } else {
        
          
          // 找出所有相关模型
          const modelsToRemove = [];
          
          // 在terrainMeshes中查找
          terrainMeshes.value.forEach((mesh, index) => {
            if (mesh && mesh.userData && mesh.userData.id === id) {
              modelsToRemove.push(mesh);
            }
          });
          console.log("移除地层模型节点");
          console.log(modelsToRemove);
          // 在场景中查找
          scene.children.forEach((child, index) => {
            if (child instanceof THREE.Mesh && child.userData && child.userData.id === id) {
              if (!modelsToRemove.includes(child)) {
                modelsToRemove.push(child);
              }
            }
          });
          
          
          // 移除所有找到的模型
          if (modelsToRemove.length > 0) {
            modelsToRemove.forEach(mesh => {              
              scene.remove(mesh);
              //AllGroup.remove(mesh);
              mesh.visible=false;
              // 释放资源
              if (mesh.geometry) {
                mesh.geometry.dispose();
              }
              
              if (mesh.material) {
                if (Array.isArray(mesh.material)) {
                  mesh.material.forEach(m => m.dispose());
                } else {
                  mesh.material.dispose();
                }
              }
            });
            
            // 更新terrainMeshes数组
            const originalLength = terrainMeshes.value.length;
            terrainMeshes.value = terrainMeshes.value.filter(mesh => !modelsToRemove.includes(mesh));
            console.log(`【调试】terrainMeshes长度变化: ${originalLength} -> ${terrainMeshes.value.length}`);
            
            // 重新调整剩余模型的索引和位置
            terrainMeshes.value.forEach((mesh, index) => {
              if (mesh) {
                mesh.userData.modelIndex = index;
                const verticalOffset = index * modelSpacing.value;
                mesh.position.y = verticalOffset;
                mesh.userData.verticalOffset = verticalOffset;
              }
            });
            
            // 更新当前模型索引
            currentModelIndex.value = terrainMeshes.value.length;
            console.log('【调试】模型已成功移除');

            // 只在最后一个地层模型被移除后，才移除立方体网格
            if (terrainMeshes.value.length === 0) {
              const oldWireCube = scene.getObjectByName('WireCubeGrid');
              if (oldWireCube) scene.remove(oldWireCube);
              
              // 同时移除切割立方体网格
              const cuttingWireCube = scene.getObjectByName('CuttingWireCubeGrid');
              if (cuttingWireCube) {
                scene.remove(cuttingWireCube);
                // 释放资源
                cuttingWireCube.traverse((child) => {
                  if (child.geometry) child.geometry.dispose();
                  if (child.material) {
                    if (Array.isArray(child.material)) {
                      child.material.forEach(m => m.dispose());
                    } else {
                      child.material.dispose();
                    }
                  }
                });
              }
              
              // 清除颜色图例
              clearColorLegend();
            }
          } 
          else {
            console.warn('【调试】未找到ID为', id, '的模型');
            // 强制清理所有场景对象
            const allMeshes = scene.children.filter(child => child instanceof THREE.Mesh);
            console.log('【调试】场景中还有', allMeshes.length, '个网格对象');
            
            if (allMeshes.length > 0) {
              console.log('【调试】执行强制清理');
              allMeshes.forEach(mesh => {
                console.log('【调试】强制清理网格:', mesh.userData?.id || '未知ID');
                scene.remove(mesh);
                if (mesh.geometry) mesh.geometry.dispose();
                if (mesh.material) {
                  if (Array.isArray(mesh.material)) {
                    mesh.material.forEach(m => m.dispose());
                  } else {
                    mesh.material.dispose();
                  }
                }
              });
              
              // 清空terrainMeshes
              terrainMeshes.value = [];
              currentModelIndex.value = 0;
              console.log('【调试】强制清理完成');
              // 清除颜色图例
              clearColorLegend();
            }
          }
        }
      }
      else if(moduleType === 7) {
          // 处理曲线节点
          if (isChecked) {
            getChannelData(wellboreId, id);
          } else {
            console.log(id+'-----'+curveType);
            // 移除曲线
            removeCurve(id, curveType);
            
            // 额外清理：确保3D模式下的管道模型也被移除
            const curveId = `${id}_${curveType}`;
            scene.traverse((object) => {
              console.log('检查对象:', object.userData);
              if (object instanceof THREE.Mesh && 
                  object.userData && 
                  object.userData.curveId === curveId) {
                console.log('移除管道模型:', curveId);
                scene.remove(object);
                if (object.geometry) object.geometry.dispose();
                if (object.material) {
                  if (Array.isArray(object.material)) {
                    object.material.forEach(m => m.dispose());
                  } else {
                    object.material.dispose();
                  }
                }
              }
            });
        }
      };
    }

    // 生成立方体网格（线框，六面有网格，内部无线）
    function createWireCubeGridFromGridInfo(gridInfo, height = 5300, segments = 6) {
      // 确保与createTerrainMesh函数使用相同的坐标系统
      const xMin = gridInfo.xMin;
      const xMax = gridInfo.xMax;
      const zMin = gridInfo.yMin;
      const zMax = gridInfo.yMax;
      const yMin = gridInfo.minHeight;
      const yMax = 0;
      const group = new THREE.Group();
      
      // 为组添加原始网格信息的引用
      group.userData.gridInfo = {
        xMin: xMin,
        xMax: xMax,
        zMin: zMin,
        zMax: zMax,
        yMin: yMin,
        yMax: yMax,
        isGrid: true // 标记为网格对象
      };

      // 六个面的法线和中心点
      const faceNormals = [
        new THREE.Vector3(1, 0, 0),   // 右
        new THREE.Vector3(-1, 0, 0),  // 左
        new THREE.Vector3(0, 1, 0),   // 上
        new THREE.Vector3(0, -1, 0),  // 下
        new THREE.Vector3(0, 0, 1),   // 前
        new THREE.Vector3(0, 0, -1),  // 后
      ];
      const faceCenters = [
        new THREE.Vector3(xMax, (yMin + yMax) / 2, (zMin + zMax) / 2), // 右
        new THREE.Vector3(xMin, (yMin + yMax) / 2, (zMin + zMax) / 2), // 左
        new THREE.Vector3((xMin + xMax) / 2, yMax, (zMin + zMax) / 2), // 上
        new THREE.Vector3((xMin + xMax) / 2, yMin, (zMin + zMax) / 2), // 下
        new THREE.Vector3((xMin + xMax) / 2, (yMin + yMax) / 2, zMax), // 前
        new THREE.Vector3((xMin + xMax) / 2, (yMin + yMax) / 2, zMin), // 后
      ];
      // 六个面的线和文本分组
      const faceLines = [[], [], [], [], [], []];
      const faceLabels = [[], [], [], [], [], []];

      // 辅助函数：生成一个面上的网格线，并返回该面所有线对象
      function addGridOnPlane(x1, y1, z1, x2, y2, z2, x3, y3, z3, x4, y4, z4, faceIdx) {
        const lines = [];
        for (let i = 0; i <= segments; i++) {
          const t = i / segments;
          // 横向线
          const start = new THREE.Vector3(
            x1 + (x4 - x1) * t,
            y1 + (y4 - y1) * t,
            z1 + (z4 - z1) * t
          );
          const end = new THREE.Vector3(
            x2 + (x3 - x2) * t,
            y2 + (y3 - y2) * t,
            z2 + (z3 - z2) * t
          );
          const line1 = new THREE.Line(
            new THREE.BufferGeometry().setFromPoints([start, end]),
            new THREE.LineBasicMaterial({ 
              color: 0xcccccc
              // 移除clippingPlanes设置，让网格线可以被切割
            })
          );
          
          // 添加标识
          line1.userData.isGridLine = true;
          line1.userData.faceIdx = faceIdx;
          line1.userData.lineType = 'horizontal';
          line1.userData.t = t;
          
          group.add(line1);
          lines.push(line1);
          
          // 纵向线
          const start2 = new THREE.Vector3(
            x1 + (x2 - x1) * t,
            y1 + (y2 - y1) * t,
            z1 + (z2 - z1) * t
          );
          const end2 = new THREE.Vector3(
            x4 + (x3 - x4) * t,
            y4 + (y3 - y4) * t,
            z4 + (z3 - z4) * t
          );
          const line2 = new THREE.Line(
            new THREE.BufferGeometry().setFromPoints([start2, end2]),
            new THREE.LineBasicMaterial({ 
              color: 0xcccccc
              // 移除clippingPlanes设置，让网格线可以被切割
            })
          );
          
          // 添加标识
          line2.userData.isGridLine = true;
          line2.userData.faceIdx = faceIdx;
          line2.userData.lineType = 'vertical';
          line2.userData.t = t;
          
          group.add(line2);
          lines.push(line2);
        }
        faceLines[faceIdx] = lines;
      }

      // 文本分组辅助函数
      function addNumberLabelsOnEdge(start, end, segments, axis = 'x', faceIdx) {
        const labels = [];
        for (let i = 0; i <= segments; i++) {
          // 新增：顶面和底面不绘制角点文本
          if ((faceIdx === 2 || faceIdx === 3) && (i === 0 || i === segments)) {
            continue;
          }
          const t = i / segments;
          const pos = new THREE.Vector3(
            start.x + (end.x - start.x) * t,
            start.y + (end.y - start.y) * t,
            start.z + (end.z - start.z) * t
          );
          let labelValue = 0;
          if (axis === 'x') labelValue = Math.round(pos.x);
          if (axis === 'y') labelValue = Math.round(pos.y);
          if (axis === 'z') labelValue = Math.round(pos.z);

          // 角点分开显示
          let offset = new THREE.Vector3(0, 0, 0);
          const offsetDistance = 300; // 增加原有偏移，从300改为500
          const offsetDistanceExtra = 200; // 增加额外偏移量，从200改为400

          if (i === 0 || i === segments) {
            if (axis === 'x') offset.x = (i === 0 ? -1 : 1) * offsetDistance;
            if (axis === 'y') offset.y = (i === 0 ? -1 : 1) * offsetDistance;
            if (axis === 'z') offset.z = (i === 0 ? -1 : 1) * offsetDistance;
          }

          // 为Y轴（垂直方向）的所有标签添加额外偏移
          if (axis === 'y') {
            // 根据面的方向添加额外偏移
            if (faceIdx === 0) { // 右面
              offset.x += 50; // 向右偏移
            } else if (faceIdx === 1) { // 左面
              offset.x -= 200; // 向左偏移
            }
            // 为所有Y轴标签添加向上偏移，避免与网格线交点重叠
            // offset.y += 100; // 向上偏移            
          }

          // 针对底面（faceIdx===3），让文本再往下偏移
          if (faceIdx === 3) {
            offset.y += offsetDistanceExtra; // 增加偏移倍数，从1倍改为3倍
            offset.z += offsetDistanceExtra
            // 为底面添加水平方向的平行偏移
            const horizontalOffset = 200; // 增加平行偏移距离，从600改为800
            
            // 根据轴向添加不同的平行偏移
            if (axis === 'x') {
              // X轴标签向Z轴负方向偏移
              offset.z -= horizontalOffset;
            } else if (axis === 'z') {
              // Z轴标签向X轴负方向偏移
              offset.x -= horizontalOffset;
            }
          }

          //为所有在底部高度(yMin)位置的标签添加额外向下偏移，避免被地层模型遮挡
          if (Math.abs(pos.y - yMin) < 10) { // 如果标签位置接近底部
            offset.y += 300; // 向下偏移800单位，确保在地层模型下方显示
          }

          const label = createTextSprite(labelValue.toString());
          label.position.copy(pos.clone().add(offset));
          
          // 添加标识
          label.userData.isGridLabel = true;
          label.userData.faceIdx = faceIdx;
          label.userData.axis = axis;
          label.userData.t = t;
          label.userData.value = labelValue;
          
          // 为Y轴标签添加特殊标记，便于智能隐藏时识别
          if (axis === 'y') {
            label.userData.isYAxisLabel = true;
          }
          
          group.add(label);
          labels.push(label);
          faceLabels[faceIdx].push(label);
        }
        
        return labels;
      }

      // 六个面
      // 右面（0）
      addGridOnPlane(xMax, yMin, zMin, xMax, yMin, zMax, xMax, yMax, zMax, xMax, yMax, zMin, 0);
      // 左面（1）
      addGridOnPlane(xMin, yMin, zMin, xMin, yMin, zMax, xMin, yMax, zMax, xMin, yMax, zMin, 1);
      // 上面（2）
      addGridOnPlane(xMin, yMax, zMin, xMax, yMax, zMin, xMax, yMax, zMax, xMin, yMax, zMax, 2);
      // 下面（3）
      addGridOnPlane(xMin, yMin, zMin, xMax, yMin, zMin, xMax, yMin, zMax, xMin, yMin, zMax, 3);
      // 前面（4）
      addGridOnPlane(xMin, yMin, zMax, xMax, yMin, zMax, xMax, yMax, zMax, xMin, yMax, zMax, 4);
      // 后面（5）
      addGridOnPlane(xMin, yMin, zMin, xMax, yMin, zMin, xMax, yMax, zMin, xMin, yMax, zMin, 5);

      // 右面（0） - 负责右后和右前两条垂直边
      addNumberLabelsOnEdge(
        new THREE.Vector3(xMax, yMin, zMin), new THREE.Vector3(xMax, yMin, zMax), segments, 'z', 0);
      addNumberLabelsOnEdge(
        new THREE.Vector3(xMax, yMin, zMax), new THREE.Vector3(xMax, yMax, zMax), segments, 'y', 0);
      addNumberLabelsOnEdge(
        new THREE.Vector3(xMax, yMax, zMax), new THREE.Vector3(xMax, yMax, zMin), segments, 'z', 0);
      addNumberLabelsOnEdge(
        new THREE.Vector3(xMax, yMax, zMin), new THREE.Vector3(xMax, yMin, zMin), segments, 'y', 0);
      // 左面（1） - 负责左后和左前两条垂直边
      addNumberLabelsOnEdge(
        new THREE.Vector3(xMin, yMin, zMin), new THREE.Vector3(xMin, yMin, zMax), segments, 'z', 1);
      addNumberLabelsOnEdge(
        new THREE.Vector3(xMin, yMin, zMax), new THREE.Vector3(xMin, yMax, zMax), segments, 'y', 1);
      addNumberLabelsOnEdge(
        new THREE.Vector3(xMin, yMax, zMax), new THREE.Vector3(xMin, yMax, zMin), segments, 'z', 1);
      addNumberLabelsOnEdge(
        new THREE.Vector3(xMin, yMax, zMin), new THREE.Vector3(xMin, yMin, zMin), segments, 'y', 1);
      // 上面（2）
      addNumberLabelsOnEdge(
        new THREE.Vector3(xMin, yMax, zMin), new THREE.Vector3(xMax, yMax, zMin), segments, 'x', 2);
      addNumberLabelsOnEdge(
        new THREE.Vector3(xMax, yMax, zMin), new THREE.Vector3(xMax, yMax, zMax), segments, 'z', 2);
      addNumberLabelsOnEdge(
        new THREE.Vector3(xMax, yMax, zMax), new THREE.Vector3(xMin, yMax, zMax), segments, 'x', 2);
      addNumberLabelsOnEdge(
        new THREE.Vector3(xMin, yMax, zMax), new THREE.Vector3(xMin, yMax, zMin), segments, 'z', 2);

      addNumberLabelsOnEdge(
        new THREE.Vector3(xMax, yMax, zMax), new THREE.Vector3(xMin, yMax, zMax), segments, 'x', 4);
      
      addNumberLabelsOnEdge(
        new THREE.Vector3(xMax, yMax, zMin), new THREE.Vector3(xMin, yMax, zMin), segments, 'x', 5);


      group.name = 'WireCubeGrid';
      group.userData.faceCenters = faceCenters;
      group.userData.faceLines = faceLines;
      group.userData.faceLabels = faceLabels;
      group.userData.isWireCubeGrid = true; // 添加标记
      group.userData.gridBounds = {
        xMin: xMin,
        xMax: xMax,
        yMin: yMin,
        yMax: yMax,
        zMin: zMin,
        zMax: zMax
      };
      
      return group;
    }

    // 动态隐藏最近的3个面
    function updateWireCubeHideNearest3Faces(group, camera) {
      if (!group || !group.userData || !group.userData.faceCenters || !group.userData.faceLines) return;
      const faceCenters = group.userData.faceCenters;
      const faceLines = group.userData.faceLines;
      const faceLabels = group.userData.faceLabels;
      const cameraPosition = new THREE.Vector3();
      camera.getWorldPosition(cameraPosition);
      // 计算每个面中心到相机的距离
      const distances = faceCenters.map(center => cameraPosition.distanceTo(center));
      // 找到距离最近的3个面的下标
      const sorted = distances
        .map((d, i) => ({ d, i }))
        .sort((a, b) => a.d - b.d)
        .slice(0, 3)
        .map(obj => obj.i);
      
      // 确定Y轴面的显示优先级：右面(0) > 左面(1)
      const yAxisFaces = [0, 1]; // 现在只有右面和左面有Y轴标签
      const visibleYAxisFaces = yAxisFaces.filter(faceIdx => !sorted.includes(faceIdx));
      
      // 决定显示哪些Y轴面的标签
      let yAxisFacesToShow = [];
      
      if (visibleYAxisFaces.length === 0) {
        // 所有Y轴面都被隐藏，强制显示距离最远的一个
        const farthestYAxisFace = yAxisFaces
          .map(faceIdx => ({ faceIdx, distance: distances[faceIdx] }))
          .sort((a, b) => b.distance - a.distance)[0].faceIdx;
        yAxisFacesToShow = [farthestYAxisFace];
      } else {
        // 有Y轴面可见，使用优先级规则：右面(0) > 左面(1)
        const hasRight = visibleYAxisFaces.includes(0);  // 右面
        const hasLeft = visibleYAxisFaces.includes(1);   // 左面
        
        // 优先显示右面，如果右面不可见才显示左面
        if (hasRight) {
          yAxisFacesToShow.push(0);
        } else if (hasLeft) {
          yAxisFacesToShow.push(1);
        }
      }
      
      // 隐藏最近的3个面，显示其余
      for (let i = 0; i < 6; i++) {
        const visible = !sorted.includes(i);
        if (faceLines[i]) {
          faceLines[i].forEach(line => { line.visible = visible; });
        }
        if (faceLabels && faceLabels[i]) {
          faceLabels[i].forEach(label => {
            if (label.userData.isYAxisLabel) {
              // Y轴标签使用智能显示逻辑
              label.visible = yAxisFacesToShow.includes(i);
            } else {
              // 非Y轴标签按正常逻辑隐藏
              label.visible = visible;
            }
          });
        }
      }
    }

    function createTextSprite(text, color = "#FFFFFF", fontSize = 45) {  // 增加默认字体大小
      const canvas = document.createElement('canvas');
      canvas.width = 512;
      canvas.height = 256;
      const ctx = canvas.getContext('2d');
      ctx.font = `bold ${fontSize}px Arial`;
      ctx.fillStyle = color;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.fillText(text, canvas.width / 2, canvas.height / 2);

      const texture = new THREE.CanvasTexture(canvas);
      const material = new THREE.SpriteMaterial({ 
        map: texture, 
        transparent: true,
        clippingPlanes: [] // 明确设置为空数组，确保不受裁剪平面影响
      });
      const sprite = new THREE.Sprite(material);
      sprite.scale.set(1800, 900, 1); // 增加缩放比例
      return sprite;
    }

    // 新增：只调整指定ID曲线粗细和显示方式的方法
    const setCurveSetting = (nodeId, thickness, displayMode, min, max, color, minCalibration, maxCalibration, minColor, maxColor) => {      
      console.log('setCurveSetting', nodeId, thickness, displayMode, color, minCalibration, maxCalibration, minColor, maxColor);      
      
      try {
        // 彻底清理与nodeId相关的所有对象
        const objectsToRemove = [];
        const lineIdsToRemove = [`${nodeId}_inline`, `${nodeId}_outline`];
        
        // 收集所有相关的线条对象
        scene.children.forEach(child => {
          if (child instanceof THREE.Line && 
              child.userData && 
              child.userData.lineId && 
              lineIdsToRemove.includes(child.userData.lineId)) {
            objectsToRemove.push(child);
          }
        });
        
        // 收集所有相关的网格对象
        scene.traverse((object) => {
          if (object instanceof THREE.Mesh && 
              object.userData && 
              object.userData.curveId === nodeId) {
            objectsToRemove.push(object);
          }
        });
        
        // 收集所有其他相关的线条对象
        scene.traverse((object) => {
          if (object instanceof THREE.Line && 
              object.userData && 
              object.userData.lineId && 
              object.userData.lineId.includes(nodeId)) {
            if (!objectsToRemove.includes(object)) {
              objectsToRemove.push(object);
            }
          }
        });
        
        console.log(`找到 ${objectsToRemove.length} 个与曲线 ${nodeId} 相关的对象需要清理`);
        
        // 获取原始数据（从第一个找到的对象中获取）
        let targetObject = null;
        for (const obj of objectsToRemove) {
          if (obj instanceof THREE.Mesh && obj.userData && obj.userData.curveId === nodeId) {
            targetObject = obj;
            break;
          }
        }
        
        // 如果未找到目标对象，直接返回
        if (!targetObject) {
          console.log(`未找到ID为 ${nodeId} 的曲线对象`);
          return;
        }
        
        // 获取原始点数据并检查有效性
        const points = targetObject.userData.points;
        if (!points || !Array.isArray(points) || points.length < 2) {
          console.error(`曲线 ${nodeId} 的点数据无效`);
          return;
        }
        
        const wellboreId = targetObject.userData.wellboreId;
        const curveType = targetObject.userData.curveType;
        
        // 彻底移除所有相关对象
        objectsToRemove.forEach(obj => {
          try {
            scene.remove(obj);
            if (obj.geometry) obj.geometry.dispose();
            if (obj.material) {
              if (Array.isArray(obj.material)) {
                obj.material.forEach(m => m.dispose());
              } else {
                obj.material.dispose();
              }
            }
          } catch (error) {
            console.error(`清理对象时出错:`, error);
          }
        });
        
        console.log(`已清理 ${objectsToRemove.length} 个对象，开始创建新的 ${displayMode} 模式曲线`);
        
        // 验证点数据的有效性并安全创建曲线
        const validPoints = points.filter(p => p && typeof p === 'object' && 'x' in p && 'y' in p && 'z' in p);
        if (validPoints.length < 2) {
          console.error(`曲线 ${nodeId} 的有效点数量不足`);
          return;
        }
        
        // 根据显示模式直接调用对应的创建函数
        if (displayMode === '2D') {
          create2DCurve(validPoints, color, wellboreId, curveType, thickness, nodeId);
        } else if (displayMode === '3D') {
          create3DCurve(validPoints, color, wellboreId, curveType, thickness, nodeId,min,max,minCalibration,maxCalibration,minColor,maxColor);
        } else {
          // 创建默认模式的曲线
          createCurve(validPoints, color, wellboreId, curveType, thickness);
        }
        
        console.log(`${displayMode} 模式曲线创建完成: ${nodeId}`);
        
      } catch (error) {
        console.error(`setCurveSetting全局错误 (${nodeId}):`, error);
      }
    };

    function done(point, m, lineId) {
      const geo = new THREE.BufferGeometry();
      
      geo.setFromPoints(point);
      const mesh = new THREE.Line(geo, m);
      mesh.computeLineDistances();
      
      // 为线条添加标识，便于后续找到并删除
      mesh.userData.lineId = lineId;
      
      scene.add(mesh);
    }

    // 动态生成颜色图例
    const generateColorLegend = (minHeight, maxHeight) => {
      console.log('生成颜色图例，高度范围:', { minHeight, maxHeight });
      
      // 如果没有有效的高度范围，隐藏图例
      if (minHeight === undefined || maxHeight === undefined || minHeight === maxHeight) {
        isLegendVisible.value = false;
        return;
      }
      
      // 直接计算最低点和最高点的颜色（与地层模型中使用的完全相同的逻辑）
      const getColorForNormalizedHeight = (normalizedHeight) => {
        let color = { r: 0.5, g: 0.5, b: 0.5 }; // 默认颜色
        
        if (normalizedHeight < 0.2) {
          // 从深蓝色开始，而不是黑色
          color = { r: 0, g: 0, b: 0.5 + normalizedHeight * 2.5 };
        } else if (normalizedHeight < 0.4) {
          color = { r: 0, g: (normalizedHeight - 0.2) * 5, b: 1 - (normalizedHeight - 0.2) * 5 };
        } else if (normalizedHeight < 0.6) {
          color = { r: (normalizedHeight - 0.4) * 5, g: 1, b: 0 };
        } else if (normalizedHeight < 0.8) {
          color = { r: 1, g: 1 - (normalizedHeight - 0.6) * 5, b: 0 };
        } else {
          // 修改最高点颜色，使用深红色而不是白色
          //color = { r: 1, g: 0.2 + (normalizedHeight - 0.8) * 4, b: 0.2 + (normalizedHeight - 0.8) * 4 };
          color = { r: 0.8, g: 0.1, b: 0.1 };
        }
        
        return color;
      };
      
      // 获取最低点颜色（normalizedHeight = 0）
      const minColor = getColorForNormalizedHeight(0);
      const minRGB = {
        r: Math.round(minColor.r * 255),
        g: Math.round(minColor.g * 255),
        b: Math.round(minColor.b * 255)
      };
      
      // 获取最高点颜色（normalizedHeight = 1）
      const maxColor = getColorForNormalizedHeight(1);
      const maxRGB = {
        r: Math.round(maxColor.r * 255),
        g: Math.round(maxColor.g * 255),
        b: Math.round(maxColor.b * 255)
      };
      
      console.log('最低点颜色:', minColor, '-> RGB:', minRGB);
      console.log('最高点颜色:', maxColor, '-> RGB:', maxRGB);
      
      // 获取中间过渡颜色点，创建五段式渐变
      const color25 = getColorForNormalizedHeight(0.25);
      const rgb25 = {
        r: Math.round(color25.r * 255),
        g: Math.round(color25.g * 255),
        b: Math.round(color25.b * 255)
      };
      
      const color50 = getColorForNormalizedHeight(0.5);
      const rgb50 = {
        r: Math.round(color50.r * 255),
        g: Math.round(color50.g * 255),
        b: Math.round(color50.b * 255)
      };
      
      const color75 = getColorForNormalizedHeight(0.75);
      const rgb75 = {
        r: Math.round(color75.r * 255),
        g: Math.round(color75.g * 255),
        b: Math.round(color75.b * 255)
      };
      
      // 创建五段式渐变：从最低点到最高点，包含中间过渡色
      const gradient = `linear-gradient(to top, 
        rgb(${minRGB.r}, ${minRGB.g}, ${minRGB.b}) 0%, 
        rgb(${rgb25.r}, ${rgb25.g}, ${rgb25.b}) 25%, 
        rgb(${rgb50.r}, ${rgb50.g}, ${rgb50.b}) 50%, 
        rgb(${rgb75.r}, ${rgb75.g}, ${rgb75.b}) 75%, 
        rgb(${maxRGB.r}, ${maxRGB.g}, ${maxRGB.b}) 100%)`;
      
      colorLegendStyle.value = gradient;
      isLegendVisible.value = true;
      
      console.log('生成的CSS渐变:', gradient);
      console.log('色标已生成：从最低点颜色到最高点颜色的直接渐变');
    };

    // 清除颜色图例
    const clearColorLegend = () => {
      isLegendVisible.value = false;
      colorLegendStyle.value = '';
    };

    // 更新切割平面的方法
    const updateClipping = () => {
      if (!clippingEnabled.value) {
        // 禁用切割
        clippingPlanes.value = [];
        // 更新所有地层模型材质
        terrainMeshes.value.forEach(mesh => {
          if (mesh && mesh.material) {
            mesh.material.clippingPlanes = [];
            mesh.material.needsUpdate = true;
          }
        });
        
        // 查找并移除所有切割网格对象
        const objectsToRemove = [];
        
        // 使用更严格的查找逻辑，避免遗漏
        scene.traverse((object) => {
          // 检查名称包含CuttingWireCubeGrid的对象
          if (object.name && object.name.includes('CuttingWireCubeGrid')) {
            objectsToRemove.push(object);
          }
          
          // 检查userData中标记为切割网格的对象
          if (object.userData && object.userData.isCuttingGrid) {
            objectsToRemove.push(object);
          }
        });
        
        // 移除收集到的所有对象
        objectsToRemove.forEach(obj => {
          try {
            console.log(`禁用切割: 移除切割网格对象: ${obj.name || '未命名'}`);
            scene.remove(obj);
            
            // 释放资源
            if (obj.geometry) obj.geometry.dispose();
            
            if (obj.material) {
              if (Array.isArray(obj.material)) {
                obj.material.forEach(m => m.dispose());
              } else {
                obj.material.dispose();
              }
            }
            
            // 递归释放子对象的资源
            obj.traverse((child) => {
              if (child.geometry) child.geometry.dispose();
              if (child.material) {
                if (Array.isArray(child.material)) {
                  child.material.forEach(m => m.dispose());
                } else {
                  child.material.dispose();
                }
              }
            });
          } catch (error) {
            console.error('移除切割网格对象时出错:', error);
          }
        });
        
        // 显示原始立方体网格
        const originalWireCube = scene.getObjectByName('WireCubeGrid');
        if (originalWireCube) {
          originalWireCube.visible = true;
        } else if (lastGridInfo.value) {
          // 如果原始网格不存在，重新创建一个
          const newWireCube = createWireCubeGridFromGridInfo({
            ...lastGridInfo.value,
            minHeight: lastGridInfo.value.minHeight * heightScale.value // 应用当前缩放
          });
          newWireCube.name = 'WireCubeGrid';
          newWireCube.userData.isWireCubeGrid = true;
          scene.add(newWireCube);
        }
        
        return;
      }

      // 创建切割平面
      const planes = [];
      
      // X轴切割平面
      if (clippingBounds.value.xMin !== undefined && clippingBounds.value.xMin !== null) {
        planes.push(new THREE.Plane(new THREE.Vector3(1, 0, 0), -clippingBounds.value.xMin));
      }
      if (clippingBounds.value.xMax !== undefined && clippingBounds.value.xMax !== null) {
        planes.push(new THREE.Plane(new THREE.Vector3(-1, 0, 0), clippingBounds.value.xMax));
      }
      
      // Z轴切割平面
      if (clippingBounds.value.zMin !== undefined && clippingBounds.value.zMin !== null) {
        planes.push(new THREE.Plane(new THREE.Vector3(0, 0, 1), -clippingBounds.value.zMin));
      }
      if (clippingBounds.value.zMax !== undefined && clippingBounds.value.zMax !== null) {
        planes.push(new THREE.Plane(new THREE.Vector3(0, 0, -1), clippingBounds.value.zMax));
      }

      clippingPlanes.value = planes;
      
      // 只应用到地层模型材质
      terrainMeshes.value.forEach(mesh => {
        if (mesh && mesh.material) {
          mesh.material.clippingPlanes = planes;
          mesh.material.clipShadows = true;
          mesh.material.needsUpdate = true;
        }
      });
      
      // 隐藏原始立方体网格
      const originalWireCube = scene.getObjectByName('WireCubeGrid');
      if (originalWireCube) {
        originalWireCube.visible = false;
      }
      
      // 查找并移除所有切割网格对象
        const objectsToRemove = [];
      
      // 使用更严格的查找逻辑，避免遗漏
            scene.traverse((object) => {
        // 检查名称包含CuttingWireCubeGrid的对象
        if (object.name && object.name.includes('CuttingWireCubeGrid')) {
          objectsToRemove.push(object);
        }
        
        // 检查userData中标记为切割网格的对象
        if (object.userData && object.userData.isCuttingGrid) {
                objectsToRemove.push(object);
              }
            });
      
      // 移除收集到的所有对象
      objectsToRemove.forEach(obj => {
        try {
          console.log(`更新切割: 移除切割网格对象: ${obj.name || '未命名'}`);
          scene.remove(obj);
          
          // 释放资源
          if (obj.geometry) obj.geometry.dispose();
          
          if (obj.material) {
            if (Array.isArray(obj.material)) {
              obj.material.forEach(m => m.dispose());
            } else {
              obj.material.dispose();
            }
          }
          
          // 递归释放子对象的资源
          obj.traverse((child) => {
            if (child.geometry) child.geometry.dispose();
            if (child.material) {
              if (Array.isArray(child.material)) {
                child.material.forEach(m => m.dispose());
              } else {
                child.material.dispose();
              }
            }
          });
        } catch (error) {
          console.error('移除切割网格对象时出错:', error);
        }
      });
      
      // 根据切割范围创建新的立方体网格
      if (lastGridInfo.value) {
        try {
          const cuttingGridInfo = {
            ...lastGridInfo.value,
            xMin: clippingBounds.value.xMin,
            xMax: clippingBounds.value.xMax,
            yMin: clippingBounds.value.zMin, // 注意坐标系转换
            yMax: clippingBounds.value.zMax,
            // 使用原始高度与当前缩放的乘积
            minHeight: lastGridInfo.value.minHeight * heightScale.value
          };
          
          const cuttingWireCube = createWireCubeGridFromGridInfo(cuttingGridInfo);
          cuttingWireCube.name = 'CuttingWireCubeGrid'; // 使用不同的名称
          cuttingWireCube.userData.isCuttingGrid = true; // 添加标记
          scene.add(cuttingWireCube);
        } catch (error) {
          console.error('创建切割网格时出错:', error);
        }
      }
    };

    // 获取插值成像数据并构建圆柱体
    const getInterpolationImage = async (nodeId,wellboreId,datasetId) => {
      try {
        const requestUrl = `${vWebApiUrl}/visual3D/project/GetInterpolationImageData?wellboreId=${wellboreId}&datasetId=${datasetId}`;
        const response = await axios.post(requestUrl);
        if (response.data?.success) {
          XCoordinates=response.data.data.xCoordinates;
          YCoordinates=response.data.data.yCoordinates;          
          const data = response.data.data.data;
          console.log('插值成像数据:', data);
          const invalidValue = "-999.250";
          const formattedData = data.map(item => {
            const depth = item["depth(m)"];
            
            // 验证depth的有效性
            if (depth === undefined || depth === null || isNaN(parseFloat(depth))) {
              return null;
            }
            
            const grValues = [];

            // 1. 提取所有 "gr_interpolated_XX(api)" 格式的键
            const grKeys = Object.keys(item)
              .filter(key => key.startsWith("gr_interpolated_") && key.endsWith("(api)"))
              // 2. 对这些键进行排序
              .sort((a, b) => {
                const numA = parseInt(a.split("_")[2].split("(")[0]);
                const numB = parseInt(b.split("_")[2].split("(")[0]);
                return numA - numB; // 按数字大小升序排列
              });

            // 3. 按照排序后的键的顺序，提取对应的值
            grKeys.forEach(key => {
              const value = item[key];
              if (value !== invalidValue && value !== undefined && value !== null && !isNaN(parseFloat(value))) { // 验证值的有效性
                grValues.push(parseFloat(value)); // 确保是数字类型
              }
            });

            if (grValues.length > 0) {
              // 确保depth是字符串类型，并且格式化数值
              const depthStr = String(depth).padEnd(14);
              const valuesStr = grValues.map(v => v.toFixed(3)).join("     ");
              return `${depthStr}${valuesStr}`;
            }
            return null;
          }).filter(row => row !== null);
          
          if (formattedData.length === 0) {
            console.error('没有有效的插值成像数据');
            return;
          }
          
          const rawData = formattedData.join("\n");
          console.log('rawdata:', rawData);
          createInterpolationCylinder(rawData, nodeId,wellboreId,datasetId);
        } else {
          throw new Error(response.data?.message || '获取插值成像数据失败');
        }
      } catch (error) {
        console.error('获取插值成像数据错误:', error);
        // 如果API调用失败，使用示例数据进行测试


        //createInterpolationCylinder(sampleData, nodeId);
      }
    };

    // 解析插值成像数据并创建圆柱体
    const createInterpolationCylinder = (data, nodeId,wellboreId,datasetId) => {
      try {
        // 解析数据
        const lines = data.split('\n').filter(line => line.trim() !== '');
        const parsedData = [];
        let minValue = Infinity;
        let maxValue = -Infinity;

        lines.forEach(line => {
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 2) { // 至少需要深度和一个值
            // 修复：去除深度值中的逗号并验证
            const depthStr = parts[0].replace(/,/g, '');
            const depth = parseFloat(depthStr);
            
            // 验证深度值
            if (isNaN(depth) || !isFinite(depth)) {
              console.warn(`跳过无效深度值: ${depthStr}`);
              return;
            }
            
            const values = [];
            // 从第二个元素开始提取值（跳过深度）
            for (let i = 1; i < parts.length; i++) {
              const value = parseFloat(parts[i]);
              // 验证每个值
              if (!isNaN(value) && isFinite(value)) {
                values.push(value);
                minValue = Math.min(minValue, value);
                maxValue = Math.max(maxValue, value);
              }
            }
            
            // 只有当有有效值时才添加到parsedData
            if (values.length > 0) {
              parsedData.push({ depth, values });
            }
          }
        });

        console.log('解析的数据:', parsedData);
        console.log('数值范围:', { minValue, maxValue });

        if (parsedData.length === 0) {
          console.error('没有有效的插值成像数据');
          return;
        }

        // 验证最大最小值
        if (!isFinite(minValue) || !isFinite(maxValue) || minValue === maxValue) {
          console.error('数值范围无效:', { minValue, maxValue });
          return;
        }

        // 创建颜色查找表
        const lut = new Lut('rainbow', 128);
        lut.setMin(minValue);
        lut.setMax(maxValue);
        
        console.log('LUT设置 - 最小值:', minValue, '最大值:', maxValue, '范围:', maxValue - minValue);

        // 圆柱体参数
        const radius = 50; // 圆柱体半径
        const segments = 64; // 提升平滑度 // 圆周分段数
        const layerHeight = 10; // 每层的高度

        // 创建几何体
        const geometry = new THREE.BufferGeometry();
        const positions = [];
        const colors = [];
        const indices = [];

        let vertexIndex = 0;

        // --- 优化参数 ---
        const numSmoothPointsPerCircle = segments; // 使用当前的segments值(64)
        const numOriginalPointsPerCircle = Math.max(16, parsedData[0].values.length); // 动态确定原始数据点数
        // ---

        // 为每一层创建平滑插值圆环
        parsedData.forEach((layer, layerIndex) => {
          // 验证深度值和高度缩放
          if (!isFinite(layer.depth) || !isFinite(heightScale.value)) {
            console.warn(`跳过无效层数据: depth=${layer.depth}, heightScale=${heightScale.value}`);
            return;
          }
          
          const y = -Math.abs(layer.depth * heightScale.value);
          
          // 验证y坐标
          if (!isFinite(y)) {
            console.warn(`跳过无效Y坐标: ${y}`);
            return;
          }
          
          // 确保values数组有足够的数据，如果不足16个，则重复填充
          let adjustedValues = [...layer.values];
          while (adjustedValues.length < 16) {
            adjustedValues = adjustedValues.concat(layer.values);
          }
          adjustedValues = adjustedValues.slice(0, 16); // 只取前16个
          
          // 提取原始16个半径值并计算动态半径
          const originalRadii = adjustedValues.map(value => {
            // const normalizedValue = (value - minValue) / (maxValue - minValue || 1);
            // return radius + normalizedValue * 50; // 增加动态半径调整幅度，从30改为50
            return radius; // 固定半径
          });
          
          // 为循环插值添加第一个值到末尾
          const originalRadiiCyclic = [...originalRadii, originalRadii[0]];
          const originalValuesCyclic = [...adjustedValues, adjustedValues[0]];
          
          // 生成平滑圆周上的点
          for (let j = 0; j < numSmoothPointsPerCircle; j++) {
            const angleRatio = j / numSmoothPointsPerCircle;
            const angle = angleRatio * 2 * Math.PI;
            
            // 验证角度
            if (!isFinite(angle)) {
              console.warn(`跳过无效角度: ${angle}`);
              continue;
            }
            
            // 计算对应于原始16个点的浮点索引
            const originalFloatIndex = angleRatio * 16; // 固定使用16个点
            const index1 = Math.floor(originalFloatIndex);
            const index2 = index1 + 1;
            
            // 获取插值所需的半径值和原始值
            const radius1 = originalRadiiCyclic[index1];
            const radius2 = originalRadiiCyclic[index2];
            const value1 = originalValuesCyclic[index1];
            const value2 = originalValuesCyclic[index2];
            const fraction = originalFloatIndex - index1;
            
            // 验证插值参数
            if (!isFinite(radius1) || !isFinite(radius2) || !isFinite(value1) || !isFinite(value2) || !isFinite(fraction)) {
              console.warn(`跳过无效插值参数`);
              continue;
            }
            
            // 线性插值计算平滑半径和值
            const interpolatedRadius = (1 - fraction) * radius1 + fraction * radius2;
            const interpolatedValue = (1 - fraction) * value1 + fraction * value2;
            
            // 验证插值结果
            if (!isFinite(interpolatedRadius) || !isFinite(interpolatedValue)) {
              console.warn(`跳过无效插值结果`);
              continue;
            }
            
            // 计算3D坐标（添加偏移）
            const x = Math.cos(angle) * interpolatedRadius + XCoordinates;
            const z = Math.sin(angle) * interpolatedRadius + YCoordinates;
            
            // 验证最终坐标
            if (!isFinite(x) || !isFinite(y) || !isFinite(z)) {
              console.warn(`跳过无效坐标: x=${x}, y=${y}, z=${z}`);
              continue;
            }
            
            positions.push(x, y, z);
            
            // 使用插值后的值计算颜色
            const color = lut.getColor(interpolatedValue);
            if (color && isFinite(color.r) && isFinite(color.g) && isFinite(color.b)) {
              colors.push(color.r, color.g, color.b);
            } else {
              // 使用默认颜色
              colors.push(0.5, 0.5, 0.5);
            }
          }
        });

        // 创建面索引（优化版）
        const actualVerticesPerLayer = positions.length / (3 * parsedData.length);
        console.log('每层实际顶点数:', actualVerticesPerLayer, '总顶点数:', positions.length / 3);
        
        if (actualVerticesPerLayer < 3 || !isFinite(actualVerticesPerLayer)) {
          console.error('顶点数不足以创建面');
          return;
        }
        
        for (let i = 0; i < parsedData.length - 1; i++) {
          for (let j = 0; j < actualVerticesPerLayer; j++) {
            const current = i * actualVerticesPerLayer + j;
            const next = i * actualVerticesPerLayer + (j + 1) % actualVerticesPerLayer;
            const currentNext = (i + 1) * actualVerticesPerLayer + j;
            const nextNext = (i + 1) * actualVerticesPerLayer + (j + 1) % actualVerticesPerLayer;
            
            // 验证索引的有效性
            const maxIndex = positions.length / 3 - 1;
            if (current <= maxIndex && next <= maxIndex && currentNext <= maxIndex && nextNext <= maxIndex) {
              // 创建两个三角形
              indices.push(current, currentNext, next);
              indices.push(next, currentNext, nextNext);
            }
          }
        }

        console.log('创建的面数:', indices.length / 3, '顶点数:', positions.length / 3);

        // 验证数据完整性
        if (positions.length === 0 || colors.length === 0 || indices.length === 0) {
          console.error('几何体数据不完整:', {
            positions: positions.length,
            colors: colors.length,
            indices: indices.length
          });
          return;
        }

        // 验证颜色数组长度与位置数组长度匹配
        if (colors.length !== positions.length) {
          console.error('颜色数组长度与位置数组长度不匹配:', {
            positions: positions.length,
            colors: colors.length
          });
          return;
        }

        // 设置几何体属性
        geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
        geometry.setIndex(indices);
        
        // 安全地计算法线
        try {
          geometry.computeVertexNormals();
        } catch (error) {
          console.error('计算法线时出错:', error);
          // 使用默认法线
          const normals = new Array(positions.length).fill(0);
          for (let i = 1; i < normals.length; i += 3) {
            normals[i] = 1; // Y轴向上的法线
          }
          geometry.setAttribute('normal', new THREE.Float32BufferAttribute(normals, 3));
        }

        // 创建材质
        // const material = new THREE.MeshLambertMaterial({
        //   vertexColors: true,
        //   side: THREE.DoubleSide,
        //   transparent: true,
        //   opacity: 0.8
        // });
        const material = new THREE.MeshLambertMaterial({
          side: THREE.DoubleSide,
          vertexColors: true,
          // metalness: 0.1,     // Lambert材质不支持metalness
          // transparent: true,  // 移除透明度以避免颜色混合问题
        });
        

        // 创建网格
        const mesh = new THREE.Mesh(geometry, material);
        
        // 设置网格的用户数据
        mesh.userData = {
          nodeId: nodeId,
          type: 'interpolationImage',
          minValue: minValue,
          maxValue: maxValue,
          wellboreId:wellboreId,
          datasetId:datasetId
        };

        // 添加增强光照系统
        if (!scene.userData.enhancedLighting) {
          // 增强环境光
          const existingAmbientLight = scene.children.find(child => child.type === 'AmbientLight');
          if (existingAmbientLight) {
            existingAmbientLight.intensity = 0.7;
          } else {
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.7);
            scene.add(ambientLight);
          }
          
          // 添加主要方向光
          const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0);
          directionalLight1.position.set(10000, 5000, 10000);
          directionalLight1.castShadow = true;
          scene.add(directionalLight1);
          
          // 添加辅助方向光
          const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.6);
          directionalLight2.position.set(-10000, 3000, -10000);
          scene.add(directionalLight2);
          
          // 添加点光源增强细节
          const pointLight = new THREE.PointLight(0xffffff, 0.5, 50000);
          pointLight.position.set(XCoordinates, 0, YCoordinates);
          scene.add(pointLight);
          
          scene.userData.enhancedLighting = true;
          console.log('增强光照系统已启用');
        }

        // 添加到场景和存储数组
        scene.add(mesh);
        interpolationMeshes.value.push(mesh);

        console.log('优化插值成像圆柱体已创建 - 平滑度:', numSmoothPointsPerCircle, '段，LUT精度:', 512);

      } catch (error) {
        console.log('创建插值成像圆柱体失败:', error);
      }
    };

    // 移除插值成像模型
    const removeInterpolationImage = (nodeId) => {
      const meshesToRemove = [];
      
      // 在interpolationMeshes中查找
      interpolationMeshes.value.forEach((mesh, index) => {
        if (mesh && mesh.userData && mesh.userData.nodeId === nodeId) {
          meshesToRemove.push(mesh);
        }
      });
      
      // 在场景中查找
      scene.children.forEach((child) => {
        if (child instanceof THREE.Mesh && 
            child.userData && 
            child.userData.nodeId === nodeId && 
            child.userData.type === 'interpolationImage') {
          if (!meshesToRemove.includes(child)) {
            meshesToRemove.push(child);
          }
        }
      });
      
      // 移除所有找到的模型
      if (meshesToRemove.length > 0) {
        meshesToRemove.forEach(mesh => {
          scene.remove(mesh);
          
          // 释放资源
          if (mesh.geometry) {
            mesh.geometry.dispose();
          }
          
          if (mesh.material) {
            if (Array.isArray(mesh.material)) {
              mesh.material.forEach(m => m.dispose());
            } else {
              mesh.material.dispose();
            }
          }
        });
        
        // 更新interpolationMeshes数组
        interpolationMeshes.value = interpolationMeshes.value.filter(mesh => !meshesToRemove.includes(mesh));
        console.log('插值成像模型已移除');
      } else {
        console.warn('未找到ID为', nodeId, '的插值成像模型');
      }
    };

    // 摄像头定位到指定坐标
    const flyToTarget = () => {
      if (!camera || !controls) {
        console.warn('相机或控制器未初始化');
        return;
      }

      const targetPos = new THREE.Vector3(
        cameraTarget.value.x,
        cameraTarget.value.y,
        cameraTarget.value.z
      );

      console.log('摄像头定位到:', targetPos);

      // 平滑动画到目标位置
      const startPos = camera.position.clone();
      const startTarget = controls.target.clone();
      const distance = cameraDistance.value;

      // 根据当前视图模式计算相机位置
      let cameraPos;
      if (viewMode.value === 'south') {
        // 正南方：相机在目标点的南方（Z轴负方向）
        cameraPos = new THREE.Vector3(targetPos.x, targetPos.y, targetPos.z - distance);
      } else if (viewMode.value === 'north') {
        // 正北方：相机在目标点的北方（Z轴正方向）
        cameraPos = new THREE.Vector3(targetPos.x, targetPos.y, targetPos.z + distance);
      } else {
        // 默认位置：相机在目标点的斜上方
        cameraPos = new THREE.Vector3(
          targetPos.x + distance * 0.5,
          targetPos.y + distance * 0.5,
          targetPos.z + distance * 0.5
        );
      }

      // 使用动画平滑移动
      const duration = 1000; // 动画时长1秒
      const startTime = Date.now();

      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // 使用缓动函数
        const easeProgress = 1 - Math.pow(1 - progress, 3);

        // 插值计算当前位置
        const currentCameraPos = startPos.clone().lerp(cameraPos, easeProgress);
        const currentTarget = startTarget.clone().lerp(targetPos, easeProgress);

        // 更新相机和控制器
        camera.position.copy(currentCameraPos);
        controls.target.copy(currentTarget);
        controls.update();

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          console.log('摄像头定位完成');
        }
      };

      animate();
    };

    // 重置相机视图
    const resetCamera = () => {
      if (!camera || !controls) {
        console.warn('相机或控制器未初始化');
        return;
      }

      if (!lastGridInfo.value) {
        console.warn('没有地层模型信息，无法重置视图');
        return;
      }

      const gridInfo = lastGridInfo.value;

      // 计算场景中心点
      const centerX = (gridInfo.xMin + gridInfo.xMax) / 2;
      const centerY = (gridInfo.yMin + gridInfo.yMax) / 2;
      const centerZ = (gridInfo.minHeight * heightScale.value + 0) / 2;  // 使用当前缩放后的高度

      // 计算模型尺寸
      const width = gridInfo.yMax - gridInfo.yMin;
      const height = gridInfo.xMax - gridInfo.xMin;
      const heightRange = Math.abs(gridInfo.minHeight * heightScale.value);
      const maxDim = Math.max(width, height, heightRange);

      console.log('重置相机视图到场景中心:', { centerX, centerY, centerZ });

      // 更新目标坐标输入框
      cameraTarget.value.x = centerX;
      cameraTarget.value.y = centerZ;
      cameraTarget.value.z = centerY;
      cameraDistance.value = maxDim * 2;

      // 根据视图模式设置相机位置
      let resetCameraPos;
      if (viewMode.value === 'south') {
        resetCameraPos = new THREE.Vector3(centerX, centerZ, gridInfo.yMin - maxDim * 2.5);
      } else if (viewMode.value === 'north') {
        resetCameraPos = new THREE.Vector3(centerX, centerZ, gridInfo.yMax + maxDim * 2.5);
      } else {
        resetCameraPos = new THREE.Vector3(centerX, centerZ + maxDim, centerY + maxDim);
      }

      const resetTarget = new THREE.Vector3(centerX, centerZ, centerY);

      // 平滑动画重置
      const startPos = camera.position.clone();
      const startTarget = controls.target.clone();
      const duration = 1000;
      const startTime = Date.now();

      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easeProgress = 1 - Math.pow(1 - progress, 3);

        const currentCameraPos = startPos.clone().lerp(resetCameraPos, easeProgress);
        const currentTarget = startTarget.clone().lerp(resetTarget, easeProgress);

        camera.position.copy(currentCameraPos);
        controls.target.copy(currentTarget);
        controls.update();

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          console.log('相机视图重置完成');
        }
      };

      animate();
    };

    // 切换控制面板收起/展开状态
    const toggleControls = () => {
      console.log('toggleControls 被调用，当前状态:', isControlsCollapsed.value);
      isControlsCollapsed.value = !isControlsCollapsed.value;
      console.log('切换后状态:', isControlsCollapsed.value);
    };

    // 保存场景到本地存储
    const saveScene = (keyName) => {
      try {
        // 收集场景数据
        const sceneData = {
          // 相机位置和目标
          camera: {
            position: camera.position.clone(),
            target: controls.target.clone()
          },
          // 控制参数
          viewMode: viewMode.value,
          heightScale: heightScale.value,
          modelSpacing: modelSpacing.value,
          // 切割参数
          clippingEnabled: clippingEnabled.value,
          clippingBounds: { ...clippingBounds.value },
          // 摄像头定位参数
          cameraTarget: { ...cameraTarget.value },
          cameraDistance: cameraDistance.value,
          // 地层模型数据
          terrainModels: terrainMeshes.value.map(mesh => ({
            id: mesh.userData.id,
            verticalOffset: mesh.userData.verticalOffset,
            modelIndex: mesh.userData.modelIndex
          })),
          // 插值成像数据
          interpolationModels: interpolationMeshes.value.map(mesh => ({
            nodeId: mesh.userData.nodeId,
            type: mesh.userData.type,
            minValue: mesh.userData.minValue,
            maxValue: mesh.userData.maxValue,
            wellboreId:mesh.userData.wellboreId,
            datasetId:mesh.userData.datasetId
          })),
          // 曲线数据（遍历场景中的所有曲线对象）
          curves: []
        };

        // 收集场景中的曲线数据
        scene.traverse((object) => {
          if (object instanceof THREE.Mesh && object.userData && object.userData.curveId) {
            const curveData = {
              curveId: object.userData.curveId,
              points: object.userData.points,
              wellboreId: object.userData.wellboreId,
              curveType: object.userData.curveType,
              displayMode: object.userData.displayMode,
              heightScale: object.userData.heightScale,
              caliper: object.userData.caliper,
              originalColor: object.userData.originalColor
            };
            
            // 如果是3D模式，添加额外的参数
            if (object.userData.displayMode === '3D') {
              // 从userData中获取3D模式的设置参数
              curveData.minVal = object.userData.minVal;
              curveData.maxVal = object.userData.maxVal;
              curveData.minCalibration = object.userData.minCalibration;
              curveData.maxCalibration = object.userData.maxCalibration;
              curveData.minColor = object.userData.minColor;
              curveData.maxColor = object.userData.maxColor;
              
              console.log('保存3D曲线参数:', {
                curveId: curveData.curveId,
                minVal: curveData.minVal,
                maxVal: curveData.maxVal,
                minCalibration: curveData.minCalibration,
                maxCalibration: curveData.maxCalibration,
                minColor: curveData.minColor,
                maxColor: curveData.maxColor
              });
            }
            
            sceneData.curves.push(curveData);
          }
        });

        // 保存到本地存储
        localStorage.setItem('threeSceneData'+keyName, JSON.stringify(sceneData));
        console.log('场景已保存到本地存储');
        //alert('场景保存成功！');
      } catch (error) {
        console.error('保存场景时出错:', error);
        //alert('保存场景失败: ' + error.message);
      }
    };

    // 从本地存储加载场景
    const loadScene = async () => {
      try {
        // 从本地存储读取数据
        const hashParams = new URLSearchParams(window.location.hash.split('?')[1]);
        const appId = hashParams.get("appId");
        const id = hashParams.get("id");
        if(!id){
          console.log('没有找到场景id');
          return;
        }
        const savedDataStr = localStorage.getItem('threeSceneData'+id);
        if (!savedDataStr) {
          console.log('没有找到保存的场景数据');
          return;
        }

        const savedData = JSON.parse(savedDataStr);
        console.log('加载的场景数据:', savedData);

        // 恢复控制参数
        viewMode.value = savedData.viewMode;
        heightScale.value = savedData.heightScale;
        modelSpacing.value = savedData.modelSpacing;

        // 恢复切割参数
        clippingEnabled.value = savedData.clippingEnabled;
        clippingBounds.value = { ...savedData.clippingBounds };

        // 恢复摄像头定位参数
        cameraTarget.value = { ...savedData.cameraTarget };
        cameraDistance.value = savedData.cameraDistance;

        // 清理当前场景
        // 移除所有地层模型
        terrainMeshes.value.forEach(mesh => {
          if (mesh) {
            scene.remove(mesh);
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) {
              if (Array.isArray(mesh.material)) {
                mesh.material.forEach(m => m.dispose());
              } else {
                mesh.material.dispose();
              }
            }
          }
        });
        terrainMeshes.value = [];

        // 移除所有插值成像模型
        interpolationMeshes.value.forEach(mesh => {
          if (mesh) {
            scene.remove(mesh);
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) {
              if (Array.isArray(mesh.material)) {
                mesh.material.forEach(m => m.dispose());
              } else {
                mesh.material.dispose();
              }
            }
          }
        });
        interpolationMeshes.value = [];

        // 移除所有曲线
        const objectsToRemove = [];
        scene.traverse((object) => {
          if (object instanceof THREE.Mesh && object.userData && object.userData.curveId) {
            objectsToRemove.push(object);
          }
          if (object instanceof THREE.Line && object.userData && object.userData.lineId) {
            objectsToRemove.push(object);
          }
        });
        objectsToRemove.forEach(obj => {
          scene.remove(obj);
          if (obj.geometry) obj.geometry.dispose();
          if (obj.material) {
            if (Array.isArray(obj.material)) {
              obj.material.forEach(m => m.dispose());
            } else {
              obj.material.dispose();
            }
          }
        });

        // 重新加载地层模型
        for (const modelData of savedData.terrainModels) {
          // 设置filePath触发模型加载
          filePath.value = modelData.id;
          await downloadAndProcessFile(modelData.id);
        }

        // 重新创建插值成像模型
        // 由于插值成像数据比较复杂，需要重新调用API获取数据
        for (const imageData of savedData.interpolationModels) {
          // 这里需要重新获取数据并创建模型
          // 注意：这需要后端API支持根据nodeId重新获取数据
          // 由于插值成像数据存储在interpolationMeshes中，我们需要重新获取数据来重建模型
           getInterpolationImage(imageData.nodeId, imageData.wellboreId, imageData.datasetId);
        }

        // 重新创建曲线
        for (const curveData of savedData.curves) {
          const points = curveData.points.map(p => ({
            x: p.x,
            y: p.y,
            z: p.z,
            modelData: p.modelData,
            originalY: p.originalY
          }));

          // 根据显示模式创建对应的曲线
          if (curveData.displayMode === '2D') {
            create2DCurve(
              points,
              curveData.originalColor,
              curveData.wellboreId,
              curveData.curveType,
              curveData.caliper,
              curveData.curveId
            );
          } else if (curveData.displayMode === '3D') {
            console.log('恢复3D曲线:', {
              curveId: curveData.curveId,
              minVal: curveData.minVal,
              maxVal: curveData.maxVal,
              minCalibration: curveData.minCalibration,
              maxCalibration: curveData.maxCalibration,
              minColor: curveData.minColor,
              maxColor: curveData.maxColor
            });
            
            create3DCurve(
              points,
              curveData.originalColor,
              curveData.wellboreId,
              curveData.curveType,
              curveData.caliper,
              curveData.curveId,
              curveData.minVal,
              curveData.maxVal,
              curveData.minCalibration,
              curveData.maxCalibration,
              curveData.minColor,
              curveData.maxColor
            );
          } else {
            createCurve(
              points,
              curveData.originalColor,
              curveData.wellboreId,
              curveData.curveType,
              curveData.caliper
            );
          }
        }

        // 恢复相机位置和目标
        if (savedData.camera && savedData.camera.position && savedData.camera.target) {
          camera.position.copy(savedData.camera.position);
          controls.target.copy(savedData.camera.target);
          controls.update();
        }

        // 更新切割平面
        updateClipping();

        console.log('场景加载完成');
        //alert('场景加载成功！');
      } catch (error) {
        console.log('加载场景时出错:', error);
        //alert('加载场景失败: ' + error.message);
      }
    };

    onMounted(() => {
      init();
      animate();
      loadScene();
    });

    onBeforeUnmount(() => {
      cleanup();
    });



    


    return {
      getDesignCurve,
      getChannelData,
      removeCurve,
      updateCurve,
      viewMode,
      statsInfo,
      filePath,
      maxHeightLabel,
      height80Label,
      height60Label,
      height40Label,
      height20Label,
      minHeightLabel,
      downloadAndProcessFile,
      handleNodeCheck,
      modelSpacing, // 导出模型间距变量
      adjustModelSpacing, // 导出调整间距方法
      heightScale,
      setCurveSetting, // 导出方法
      colorLegendStyle,
      isLegendVisible,
      generateColorLegend,
      clearColorLegend,
      clippingEnabled,
      clippingBounds,
      clippingPlanes,
      updateClipping,
      getInterpolationImage, // 导出插值成像方法
      removeInterpolationImage, // 导出移除插值成像方法
      interpolationMeshes, // 导出插值成像网格数组
      cameraTarget,
      cameraDistance,
      flyToTarget, // 导出摄像头定位方法
      resetCamera, // 导出重置相机方法
      saveScene, // 导出保存场景方法
      loadScene, // 导出加载场景方法
      isControlsCollapsed, // 导出控制面板收起状态
      toggleControls // 导出切换控制面板方法

    };
  }
});
</script>

<style scoped>
.three-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

#fileInput {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 5px;
  z-index: 10;
  color: white;
}

#controls {
  position: absolute;
  top: 60px;
  right: 10px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 5px;
  z-index: 10;
  transition: transform 0.3s ease;
  transform: translateX(0);
  color: white;
}

#controls.collapsed {
  transform: translateX(calc(100% - 30px));
  overflow: visible;
}

/* 确保收起状态下切换按钮仍然可以正常点击 */
#controls.collapsed .controls-toggle {
  pointer-events: auto;
  opacity: 1;
  transform: translateY(-50%);
}

#controls.collapsed > div:not(.controls-toggle) {
  opacity: 0;
  pointer-events: none;
}

/* 确保切换按钮始终可以点击 */
#controls.collapsed .controls-toggle {
  pointer-events: auto;
  opacity: 1;
}

/* 确保切换按钮在收起状态下更加明显 */
#controls.collapsed .controls-toggle {
  background-color: rgba(0, 0, 0, 1);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.controls-toggle {
  position: absolute;
  left: -30px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 60px;
  background-color: rgba(0, 0, 0, 0.9);
  border-radius: 5px 0 0 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  color: #4CAF50;
  transition: all 0.2s ease;
  z-index: 999;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  pointer-events: auto;
  user-select: none;
}

.controls-toggle:hover {
  background-color: rgba(0, 0, 0, 1);
  color: #fff;
  border-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
  transform: translateY(-50%) scale(1.05);
}

#controls div {
  margin: 5px 0;
  transition: opacity 0.3s ease;
}

#controls span {
  color: #fff;
  font-weight: 500;
}

#controls label {
  display: inline-block;
  width: 100px;
  font-size: 12px;
  color: #fff;
  font-weight: 500;
}

#controls input[type="number"] {
  width: 80px;
  padding: 2px;
  font-size: 12px;
  background-color: rgba(255, 255, 255, 0.9);
  color: #000;
  border: 1px solid #666;
  border-radius: 3px;
}

#controls input[type="checkbox"] {
  margin-right: 5px;
}

#stats {
  position: absolute;
  color: white;
  background-color: rgba(0, 0, 0, 0.8);
  padding: 10px;
  border-radius: 5px;
  z-index: 10;
}

#colorLegend {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 300px;
  border-radius: 5px;
  border: 1px solid white;
  z-index: 10;
}

#colorLabels {
  position: absolute;
  left: 45px;
  top: 50%;
  transform: translateY(-50%);
  height: 300px;
  color: white;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: rgba(0, 0, 0, 0.8);
  padding: 5px;
  border-radius: 5px;
  z-index: 10;
}

.upload-demo {
  width: 100%;
}

.el-upload-dragger {
  width: 100%;
}

.el-upload__tip {
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}
</style>
