(function(I){typeof define=="function"&&define.amd?define(I):I()})(function(){"use strict";var Ht=Object.defineProperty;var Bt=(I,B,A)=>B in I?Ht(I,B,{enumerable:!0,configurable:!0,writable:!0,value:A}):I[B]=A;var d=(I,B,A)=>Bt(I,typeof B!="symbol"?B+"":B,A);var I=typeof document<"u"?document.currentScript:null;function B(_){return _&&_.__esModule&&Object.prototype.hasOwnProperty.call(_,"default")?_.default:_}var A={exports:{}},Y=typeof Reflect=="object"?Reflect:null,xe=Y&&typeof Y.apply=="function"?Y.apply:function(s,e,t){return Function.prototype.apply.call(s,e,t)},ne;Y&&typeof Y.ownKeys=="function"?ne=Y.ownKeys:Object.getOwnPropertySymbols?ne=function(s){return Object.getOwnPropertyNames(s).concat(Object.getOwnPropertySymbols(s))}:ne=function(s){return Object.getOwnPropertyNames(s)};function Qe(_){console&&console.warn&&console.warn(_)}var De=Number.isNaN||function(s){return s!==s};function x(){x.init.call(this)}A.exports=x,A.exports.once=it,x.EventEmitter=x,x.prototype._events=void 0,x.prototype._eventsCount=0,x.prototype._maxListeners=void 0;var Re=10;function ae(_){if(typeof _!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof _)}Object.defineProperty(x,"defaultMaxListeners",{enumerable:!0,get:function(){return Re},set:function(_){if(typeof _!="number"||_<0||De(_))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+_+".");Re=_}}),x.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},x.prototype.setMaxListeners=function(s){if(typeof s!="number"||s<0||De(s))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+s+".");return this._maxListeners=s,this};function je(_){return _._maxListeners===void 0?x.defaultMaxListeners:_._maxListeners}x.prototype.getMaxListeners=function(){return je(this)},x.prototype.emit=function(s){for(var e=[],t=1;t<arguments.length;t++)e.push(arguments[t]);var i=s==="error",r=this._events;if(r!==void 0)i=i&&r.error===void 0;else if(!i)return!1;if(i){var n;if(e.length>0&&(n=e[0]),n instanceof Error)throw n;var a=new Error("Unhandled error."+(n?" ("+n.message+")":""));throw a.context=n,a}var l=r[s];if(l===void 0)return!1;if(typeof l=="function")xe(l,this,e);else for(var h=l.length,o=Ve(l,h),t=0;t<h;++t)xe(o[t],this,e);return!0};function ke(_,s,e,t){var i,r,n;if(ae(e),r=_._events,r===void 0?(r=_._events=Object.create(null),_._eventsCount=0):(r.newListener!==void 0&&(_.emit("newListener",s,e.listener?e.listener:e),r=_._events),n=r[s]),n===void 0)n=r[s]=e,++_._eventsCount;else if(typeof n=="function"?n=r[s]=t?[e,n]:[n,e]:t?n.unshift(e):n.push(e),i=je(_),i>0&&n.length>i&&!n.warned){n.warned=!0;var a=new Error("Possible EventEmitter memory leak detected. "+n.length+" "+String(s)+" listeners added. Use emitter.setMaxListeners() to increase limit");a.name="MaxListenersExceededWarning",a.emitter=_,a.type=s,a.count=n.length,Qe(a)}return _}x.prototype.addListener=function(s,e){return ke(this,s,e,!1)},x.prototype.on=x.prototype.addListener,x.prototype.prependListener=function(s,e){return ke(this,s,e,!0)};function Ze(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function Le(_,s,e){var t={fired:!1,wrapFn:void 0,target:_,type:s,listener:e},i=Ze.bind(t);return i.listener=e,t.wrapFn=i,i}x.prototype.once=function(s,e){return ae(e),this.on(s,Le(this,s,e)),this},x.prototype.prependOnceListener=function(s,e){return ae(e),this.prependListener(s,Le(this,s,e)),this},x.prototype.removeListener=function(s,e){var t,i,r,n,a;if(ae(e),i=this._events,i===void 0)return this;if(t=i[s],t===void 0)return this;if(t===e||t.listener===e)--this._eventsCount===0?this._events=Object.create(null):(delete i[s],i.removeListener&&this.emit("removeListener",s,t.listener||e));else if(typeof t!="function"){for(r=-1,n=t.length-1;n>=0;n--)if(t[n]===e||t[n].listener===e){a=t[n].listener,r=n;break}if(r<0)return this;r===0?t.shift():et(t,r),t.length===1&&(i[s]=t[0]),i.removeListener!==void 0&&this.emit("removeListener",s,a||e)}return this},x.prototype.off=x.prototype.removeListener,x.prototype.removeAllListeners=function(s){var e,t,i;if(t=this._events,t===void 0)return this;if(t.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):t[s]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete t[s]),this;if(arguments.length===0){var r=Object.keys(t),n;for(i=0;i<r.length;++i)n=r[i],n!=="removeListener"&&this.removeAllListeners(n);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(e=t[s],typeof e=="function")this.removeListener(s,e);else if(e!==void 0)for(i=e.length-1;i>=0;i--)this.removeListener(s,e[i]);return this};function Ie(_,s,e){var t=_._events;if(t===void 0)return[];var i=t[s];return i===void 0?[]:typeof i=="function"?e?[i.listener||i]:[i]:e?tt(i):Ve(i,i.length)}x.prototype.listeners=function(s){return Ie(this,s,!0)},x.prototype.rawListeners=function(s){return Ie(this,s,!1)},x.listenerCount=function(_,s){return typeof _.listenerCount=="function"?_.listenerCount(s):Fe.call(_,s)},x.prototype.listenerCount=Fe;function Fe(_){var s=this._events;if(s!==void 0){var e=s[_];if(typeof e=="function")return 1;if(e!==void 0)return e.length}return 0}x.prototype.eventNames=function(){return this._eventsCount>0?ne(this._events):[]};function Ve(_,s){for(var e=new Array(s),t=0;t<s;++t)e[t]=_[t];return e}function et(_,s){for(;s+1<_.length;s++)_[s]=_[s+1];_.pop()}function tt(_){for(var s=new Array(_.length),e=0;e<s.length;++e)s[e]=_[e].listener||_[e];return s}function it(_,s){return new Promise(function(e,t){function i(n){_.removeListener(s,r),t(n)}function r(){typeof _.removeListener=="function"&&_.removeListener("error",i),e([].slice.call(arguments))}He(_,s,r,{once:!0}),s!=="error"&&st(_,i,{once:!0})})}function st(_,s,e){typeof _.on=="function"&&He(_,"error",s,e)}function He(_,s,e,t){if(typeof _.on=="function")t.once?_.once(s,e):_.on(s,e);else if(typeof _.addEventListener=="function")_.addEventListener(s,function i(r){t.once&&_.removeEventListener(s,i),e(r)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof _)}var rt=A.exports;const nt=B(rt);class y{constructor(s,e,t=null,i=null){d(this,"x",0);d(this,"y",0);d(this,"z",0);d(this,"v",0);this.x=s??0,this.y=e??0,this.z=i??0,this.v=t??0}translate(s,e){return new y(this.x+s,this.y+e)}sameAs(s){return s.x==this.x&&s.y==this.y&&s.v==this.v&&s.z==this.z}}class C{constructor(s,e,t,i){d(this,"x");d(this,"y");d(this,"w");d(this,"h");this.x=s??0,this.y=e??0,this.w=t??0,this.h=i??0}getLocation(){return new y(this.x,this.y)}sameAs(s){return s.x==this.x&&s.y==this.y&&s.w==this.w&&s.h==this.h}translate(s,e){return new C(this.x+s,this.y+e,this.w,this.h)}clone(){return new C(this.x,this.y,this.w,this.h)}setSize(s){this.x=s.x,this.y=s.y,this.w=s.w,this.h=s.h}containPoint(s){return s.x>this.x&&s.x<this.x+this.w&&s.y>this.y&&s.y<this.y+this.h}intersectsWithLine(s,e,t,i){let r=this.x+this.w,n=this.y+this.h,a=this.x,l=this.y,h=e-i,o=t-s,u=s*i-t*e;if(h*a+o*l+u>=0&&h*r+o*n+u<=0||h*a+o*l+u<=0&&h*r+o*n+u>=0||h*a+o*n+u>=0&&h*r+o*l+u<=0||h*a+o*n+u<=0&&h*r+o*l+u>=0){if(a>r){let c=a;a=r,r=c}if(l<n){let c=l;l=n,n=c}return!(s<a&&t<a||s>r&&t>r||e>l&&i>l||e<n&&i<n)}else return!1}compareLarger(s){return s.w>this.w&&s.h>=this.h||s.h>this.h&&s.w>=this.w?s:this}intersect(s){if(this.intersectsWith(s)){let e=this.x+this.w,t=this.y+this.h;this.x=Math.max(this.x,s.x),this.y=Math.max(this.y,s.y),this.w=Math.min(e,s.x+s.w)-this.x,this.h=Math.min(t,s.y+s.h)-this.y}else this.x=le.x,this.y=le.y,this.w=le.w,this.h=le.h}inflate(s,e){this.x-=s,this.y-=e,this.w+=2*s,this.h+=2*e}pointOnFrame(s){return this.pointOnLeftFrame(s)||this.pointOnRightFrame(s)||this.pointOnTopFrame(s)||this.pointOnBottomFrame(s)}pointOnLeftFrame(s){return Math.abs(s.x-this.x)<2}pointOnRightFrame(s){return Math.abs(s.x-(this.x+this.w))<2}pointAtBoundLeft(s){return Math.abs(s.x-this.x)<5}pointAtBoundRight(s){return Math.abs(s.x-(this.x+this.w))<5}pointAtBoundTop(s){return Math.abs(s.y-this.y)<5}pointAtBoundBottom(s){return Math.abs(s.y-(this.y+this.h))<5}pointOnTopFrame(s){return Math.abs(s.y-this.y)<2}pointOnBottomFrame(s){return Math.abs(s.y-(this.y+this.h))<2}intersectsWith(s){if(!s)throw new Error('Parameter "rect" cannot be null or undefined');const e=this.x+this.w,t=this.y+this.h,i=s.x+s.w,r=s.y+s.h;return s.x<e&&i>this.x&&s.y<t&&r>this.y}}const le=new C(0,0,0,0);class k{constructor(s={}){d(this,"family");d(this,"height");d(this,"style");d(this,"foreColor");d(this,"maxWidth");d(this,"textAlign");d(this,"textBaseline");d(this,"direction");this.family=s.family??"Helvetica",this.height=s.height??12,this.style=s.style??"",this.foreColor=s.foreColor??"#000000",this.maxWidth=s.maxWidth??9999,this.textAlign=s.textAlign??"start",this.textBaseline=s.textBaseline??"alphabetic",this.direction=s.direction??"inherit"}get font(){return(this.style==""?"":this.style+" ")+this.height.toString()+"px "+this.family}}const at="Generic";class ue{constructor(s={}){d(this,"backgroundColor");d(this,"grayBGColor");d(this,"stBGColor");d(this,"lineColor");d(this,"gridColor");d(this,"lightGridColor");d(this,"majorGridColor");d(this,"borderColor");d(this,"lightBorderColor");d(this,"hitColor");d(this,"indicatorColor");d(this,"dragPointColor");d(this,"scrollbarColor");d(this,"scrollbarBGColor");d(this,"scrollbarOverColor");d(this,"scrollbarDownColor");d(this,"stratuPointColor");d(this,"predictionLineColor");d(this,"gsIndicatorLineColor");d(this,"alBgColor");d(this,"intpNodeColor");d(this,"headerFont");d(this,"headerBoldFont");d(this,"scaleFont");d(this,"smallFont");d(this,"normalFont");d(this,"indicatorFont");d(this,"boldFont");d(this,"midFont");d(this,"boldMidFont");let e=Object.assign({backgroundColor:"#ffffff",grayBGColor:"#bebdbd",stBGColor:"#fffff7",lineColor:"rgba(0,0,0,0.5)",lightBorderColor:"#cccccc",borderColor:"#1a1a1a",gridColor:"rgba(0,0,0,0.5)",lightGridColor:"#e6e6df",intpNodeColor:"#A5A5A5",majorGridColor:"#404040",indicatorColor:"rgba(127,127,0,.5)",hitColor:"#ff0000",dragPointColor:"#bfbfbf",scrollbarColor:"#C1C1C1",scrollbarBGColor:"#F1F1F1",scrollbarOverColor:"#A8A8A8",scrollbarDownColor:"#787878",headerFont:new k({foreColor:"#ffffff",height:16}),headerBoldFont:new k({style:"bold",height:12}),smallFont:new k({height:10}),midFont:new k({height:12}),boldMidFont:new k({height:12,style:"bold"}),scaleFont:new k({style:"bold",height:14}),normalFont:new k({height:16}),indicatorFont:new k({height:16}),boldFont:new k({height:16,style:"bold"}),stratuPointColor:"#ff00ff",predictionLineColor:"#00D700",alBgColor:"#E4E8EC",gsIndicatorLineColor:"#81AEE7"},s);Object.assign(this,e)}get Name(){return at}update(s){Object.assign(this,s)}}class V{constructor(s=null,e=null){d(this,"objTypeName","FreeObject");d(this,"defaultLayerNo",10);s!=null&&(this.objTypeName=s),e!=null&&(this.defaultLayerNo=e)}}class lt extends nt.EventEmitter{constructor(e){super();d(this,"opts");d(this,"dpr");d(this,"canvas");d(this,"ctx");d(this,"offscreenCanvasElement");d(this,"offScreenCanvas");d(this,"offScreenCtx");d(this,"floatCanvasElement");d(this,"ctxFloat");d(this,"topCanvasElement");d(this,"ctxTop");d(this,"width");d(this,"height");d(this,"stageWidth");d(this,"stageHeight");d(this,"refreshRect");d(this,"refreshRects");d(this,"requireRedraw",!1);d(this,"defaultContextMenu");d(this,"contextMenus",[]);d(this,"webCanvasViews");d(this,"visualComponents");d(this,"selectedVisualComponents",[]);d(this,"logDataLoader");d(this,"canvasTheme",new ue);d(this,"lastMouseDownPos");d(this,"lastMouseMovePos");d(this,"lastMouseDownClientPos");d(this,"lastMouseMoveClientPos");d(this,"objTypeInfos");d(this,"orientation","horizontal");d(this,"frame",()=>{if(this.requireRedraw)try{const e=[...this.refreshRects];this.clearRefreshRects(),this.redraw(this.ctx,e)}catch(e){console.error("Error during frame rendering:",e)}finally{this.requireRedraw=!1}window.requestAnimationFrame(this.frame)});this.opts=e,this.dpr=window.devicePixelRatio>1?1.2:window.devicePixelRatio||1,this.canvas=document.createElement("canvas"),this.canvas.style.position="absolute",this.canvas.oncontextmenu=t=>{t.preventDefault()},this.ctx=this.canvas.getContext("2d"),this.defaultContextMenu=document.getElementById("defaultContextMenu"),this.contextMenus.push(this.defaultContextMenu),this.appContainer.appendChild(this.canvas),this.webCanvasViews=new Array,this.visualComponents=new Array,this.refreshRect=new C,this.refreshRects=new Array,this.loadCanvasCtx(),this.resize(),window.addEventListener("resize",this.debounce(()=>this.resize(),100)),this.resizeObserver=new ResizeObserver(this.debounce(()=>this.resize(),100)),this.resizeObserver.observe(this.appContainer),this.frame()}get plotMeasureType(){return null}set plotMeasureType(e){}loadCanvasCtx(){this.floatCanvasElement=document.createElement("canvas"),this.floatCanvasElement.style.position="absolute",this.appContainer.appendChild(this.floatCanvasElement),this.ctxFloat=this.floatCanvasElement.getContext("2d"),this.topCanvasElement=document.createElement("canvas"),this.topCanvasElement.style.position="absolute",this.appContainer.appendChild(this.topCanvasElement),this.ctxTop=this.topCanvasElement.getContext("2d")}debounce(e,t){let i=null;return()=>{i!==null&&clearTimeout(i),i=window.setTimeout(()=>{e()},t)}}resize(){this.width=this.appContainer.offsetWidth,this.height=this.appContainer.offsetHeight,this.stageWidth=Math.floor(this.width*this.dpr),this.stageHeight=Math.floor(this.height*this.dpr),this.canvas.width=this.floatCanvasElement.width=this.stageWidth,this.canvas.height=this.floatCanvasElement.height=this.stageHeight,this.canvas.style.width=this.floatCanvasElement.style.width=`${this.width}px`,this.canvas.style.height=this.floatCanvasElement.style.height=`${this.height}px`,this.topCanvasElement.width=this.stageWidth,this.topCanvasElement.height=this.stageHeight,this.topCanvasElement.style.width=`${this.width}px`,this.topCanvasElement.style.height=`${this.height}px`,this.refreshRect&&(this.refreshRect.w=this.stageWidth,this.refreshRect.h=this.stageHeight),this.layout(),this.invalidate()}layout(){if(this.webCanvasViews&&this.webCanvasViews.length>0)for(let e=0;e<this.webCanvasViews.length;e++)this.webCanvasViews[e].autoLayoutContents();if(this.visualComponents&&this.visualComponents.length>0)for(let e=0;e<this.visualComponents.length;e++)this.visualComponents[e].autoLayoutContents()}invalidate(e){try{this.refreshRect=e?new C(e.x,e.y,e.w,e.h):new C(0,0,this.stageWidth,this.stageHeight)}catch{this.refreshRect=new C(0,0,this.stageWidth,this.stageHeight)}finally{this.addRefreshRects(this.refreshRect),this.requireRedraw=!0}}addRefreshRects(e){if(this.clearRefreshRects(),!this.refreshRects.some(t=>t.sameAs(e))){let t=this.refreshRects.find(i=>i.intersectsWith(e));if(t){let i=e.compareLarger(t);t.setSize(i)}else this.refreshRects.push(e)}}redraw(e,t){if(this.redrawBackGround(e,t),this.webCanvasViews&&this.webCanvasViews.length>0)for(let i=0;i<this.webCanvasViews.length;i++)this.webCanvasViews[i].redraw(e,t);if(this.visualComponents&&this.visualComponents.length>0){this.clearComponentRect(t,e);for(let i=0;i<this.visualComponents.length;i++){let r=t.find(n=>this.visualComponents[i].needRedraw(n)==!0);r!=null&&this.visualComponents[i].redraw(r)}}}clearComponentRect(e,t){for(let i=0;i<this.visualComponents.length;i++){let r=this.visualComponents[i].drawRect;e.find(n=>r.intersectsWith(n))!=null&&t.clearRect(r.x,r.y,r.w,r.h)}}redrawBackGround(e,t){t.find(i=>i.w==this.stageWidth&&i.h==this.stageHeight)!=null&&(e.fillStyle=this.canvasTheme.backgroundColor,e.fillRect(0,0,this.stageWidth,this.stageHeight))}clearRefreshRects(){this.refreshRects=[]}loadObjTypeInfos(e){if(e!=null)for(const t of e){let i=this.findObjectTypeInfo(t.objTypeName);i==null?this.objTypeInfos.push(new V(t.objTypeName,t.defaultLayerNo)):i.defaultLayerNo=t.defaultLayerNo}}findObjectTypeInfo(e){return this.objTypeInfos.find(t=>t.objTypeName==e)}isComponentSelected(e){return this.selectedVisualComponents.indexOf(e)>=0}SelectComponent(e,t){if(e==null){this.selectedVisualComponents.length>0&&(this.selectedVisualComponents=[],this.invalidateAllComponent());return}if(t){if(this.selectedVisualComponents.length==0){this.selectedVisualComponents.push(e),this.InvalidateComponent(e);return}if(this.IsComponentPrimarilySelected(e))return;{this.InvalidateComponent(this.GetPrimarilySelectedComponent());let i=this.selectedVisualComponents.indexOf(e);i>=0&&this.selectedVisualComponents.splice(i,1),this.selectedVisualComponents.push(e),this.InvalidateComponent(e)}}else if(this.isComponentSelected(e))if(this.IsComponentPrimarilySelected(e))this.selectedVisualComponents.splice(this.selectedVisualComponents.length-1,1);else{let i=this.selectedVisualComponents.indexOf(e);this.selectedVisualComponents.splice(i,1)}else return}invalidateAllComponent(){if(this.visualComponents.length>0)for(let e=0;e<this.visualComponents.length;e++)this.invalidate(this.visualComponents[e].drawRect)}InvalidateComponent(e){let t=e.drawRect;this.invalidate(t)}GetPrimarilySelectedComponent(){return this.selectedVisualComponents.length==0?null:this.selectedVisualComponents[this.selectedVisualComponents.length-1]}IsComponentPrimarilySelected(e){return this.selectedVisualComponents.length>0&&this.selectedVisualComponents.indexOf(e)==this.selectedVisualComponents.length-1}setEvent(e){let t={mousedown:a=>{a.target==this.topCanvasElement&&e.currentTool.OnMouseDown(this,a)},click:a=>{},dblclick:a=>{a.target==this.topCanvasElement&&e.currentTool.OnMouseDoubleClick(this,a)},mousemove:a=>{requestAnimationFrame(()=>{e.currentTool.OnMouseMove(this,a)})},mouseup:a=>{e.currentTool.OnMouseUp(this,a)},mouseout:a=>{e.currentTool.OnMouseOut(this,a)},mouseenter:a=>{e.currentTool.OnMouseEnter(this,a)}},i=a=>{e.currentTool.OnKeyDown(this,a),a.preventDefault()},r=a=>{e.currentTool.OnKeyUp(this,a),a.preventDefault()},n=a=>{this.stageRect.containPoint(this.lastMouseMovePos)&&requestAnimationFrame(()=>{e.currentTool.OnMouseWheel(this,a)})};this.floatCanvasElement.oncontextmenu=a=>{a.preventDefault()},this.topCanvasElement.oncontextmenu=a=>{a.preventDefault(),e.currentTool.OnContextMenu(this,a)};for(let a in t)window.addEventListener(a,t[a]);this.appContainer.addEventListener("wheel",n,{passive:!0}),this.appContainer.addEventListener("keydown",i),this.appContainer.addEventListener("keyup",r)}get appContainer(){return this.opts.appContainer}handleOrientation(e,t){return this.orientation==="horizontal"?e():t()}}class ot{constructor(s){d(this,"currentWorkMode",0);d(this,"isDirty",!1);d(this,"webCanvas",null);d(this,"tools",[]);d(this,"defaultTool",null);d(this,"_CurrentTool",null);this._CurrentTool=null,this.webCanvas=s}get currentTool(){return this._CurrentTool}set currentTool(s){if(this._CurrentTool!=s&&this._CurrentTool!=null){if(!this._CurrentTool.onDeselecting(this.webCanvas))return;this._CurrentTool.onDeselected(this.webCanvas)}this._CurrentTool=s,s!=null&&s.onSelected(this.webCanvas)}registerTool(s){this.tools.indexOf(s)>=0||(this.tools.push(s),s.parent=this)}setCurrentTool(s){for(let e of this.tools)if(e.name===s){this.currentTool=e;break}return this._CurrentTool}findTool(s){for(let e of this.tools)if(e.name===s)return e;return null}}class ht{constructor(s){d(this,"webCanvas");d(this,"ctxTop");d(this,"show_");d(this,"px_");d(this,"py_");d(this,"_requireRedraw",!1);d(this,"plotRect_");d(this,"AllBodysRect_");d(this,"theme_");this.webCanvas=s,this.ctxTop=s.ctxTop,this.requireRedraw,window.requestAnimationFrame(this.frame.bind(this))}frame(){this.requireRedraw&&(this.requireRedraw=!1)}hide(){this.show_=!1,this.requireRedraw}show(s){this.show_=!0,this.px_=s.x,this.py_=s.y,this.requireRedraw}redraw_(){let s=this.plotRect_,e=this.AllBodysRect_,t=this.ctxTop;t.clearRect(0,0,s.w,s.h),t.font=this.theme_.indicatorFont.font;let i;if(this.show_){let r=!1,n=this.webCanvas,a=n.virtual_pixel_offsetY_,l=n.vPlotView_.vPlot_.measure_,h=n.vPlotView_.vPlot_.objs_.map(o=>o);for(let o=0;o<h.length;o++){let u=h[o],c=u.bodyRect_,f=u.objs_.filter(g=>g.type_=="curve");if(f)for(let g=0;g<f.length;g++){let m=f[g],w=n.loader_.findLogPrsDataContainer(m.objId_,n);if(w){let v=w.fetch_nearest__((a+this.py_-e.y)/l.dprm_i_);if(v.ok&&(i=v.index*l.dprm_i_+e.y-a,v.index>=n.vPlotView_.vPlot_.topIndex&&v.index<n.vPlotView_.vPlot_.bottomIndex)){r==!1&&(t.strokeStyle=this.theme_.indicatorColor,t.lineWidth=1,t.beginPath(),t.moveTo(0,i),t.lineTo(e.w,i),t.stroke(),r=!0);let b=m.curve_opt_.v_to_grid_x_(v.value)+c.x;t.strokeStyle=m.lineTrait.foreColor,t.lineWidth=1,t.beginPath(),t.moveTo(b,i);let M=i+10+g*20;t.lineTo(b,M),t.stroke();let O=parseFloat(v.value.toFixed(3)).toString();t.strokeStyle="rgba(255,255,255,.875)",t.lineWidth=8,t.strokeText(O,b,M+16),t.fillStyle=m.lineTrait.foreColor,t.fillText(O,b,M+16)}}}}}}get requireRedraw(){return this._requireRedraw=!0,this._requireRedraw}set requireRedraw(s){this._requireRedraw=s}}class dt{constructor(){}notify(s){}}var D=(_=>(_[_.none=0]="none",_[_.solid=1]="solid",_[_.dash=2]="dash",_[_.dot=3]="dot",_))(D||{});class N{constructor(s={}){d(this,"width");d(this,"foreColor");d(this,"lineType");d(this,"lineCap");d(this,"lineJoin");d(this,"dashStyle",[20,10]);d(this,"dotStyle",[5,5]);let e=Object.assign({width:1,foreColor:"#000000",lineType:1,lineCap:"butt",lineJoin:"round",textBaseline:"alphabetic",direction:"inherit"},s);Object.assign(this,e)}}const oe="FreeObject";class J extends dt{constructor(e){super();d(this,"_webCanvas");d(this,"_cacheCanvas");d(this,"_layer");d(this,"_textTrait",new k);d(this,"_lineTrait",new N);d(this,"_drawRect",new C);d(this,"_isSelected",!1);d(this,"_isDraggingPos",!1);d(this,"_isDraggingBound",!1);d(this,"_invalid",!0);d(this,"_onMouseDown",!1);d(this,"_onMouseHover",!1);d(this,"_isVisible",!0);d(this,"_isDisable",!1);d(this,"_hitTestProtected",!1);d(this,"_objType");d(this,"_handlePointRadius",5);d(this,"_parentVisualObject");d(this,"_subVisualObjects",[]);d(this,"_cacheVisualObjects",[]);this._webCanvas=e,this._layer=this._webCanvas.objTypeInfos[0].defaultLayerNo}get webCanvas(){return this._webCanvas}set webCanvas(e){this._webCanvas=e}get cacheCanvas(){return this._cacheCanvas}set cacheCanvas(e){this._cacheCanvas=e}get layer(){return this._layer}set layer(e){this._layer=e}get textTrait(){return this._textTrait}set textTrait(e){this._textTrait=e}get lineTrait(){return this._lineTrait}set lineTrait(e){this._lineTrait=e}get drawRect(){return this._drawRect}set drawRect(e){this._drawRect=e}get isSelected(){return this._isSelected}set isSelected(e){this._isSelected=e}get isDraggingPos(){return this._isDraggingPos}set isDraggingPos(e){this._isDraggingPos=e}get isDraggingBound(){return this._isDraggingBound}set isDraggingBound(e){this._isDraggingBound=e}get invalid(){return this._invalid}set invalid(e){this._invalid=e}get onMouseDown(){return this._onMouseDown}set onMouseDown(e){this._onMouseDown=e}get onMouseHover(){return this._onMouseHover}set onMouseHover(e){this._onMouseHover=e}get isVisible(){return this._isVisible}set isVisible(e){this._isVisible=e}get isDisable(){return this._isDisable}set isDisable(e){this._isDisable=e}get hitTestProtected(){return this._hitTestProtected}set hitTestProtected(e){this._hitTestProtected=e}get objType(){return this._objType}set objType(e){this._objType=e}get handlePointRadius(){return this._handlePointRadius}set handlePointRadius(e){this._handlePointRadius=e}get parentVisualObject(){return this._parentVisualObject}set parentVisualObject(e){this._parentVisualObject=e}get subVisualObjects(){return this._subVisualObjects}set subVisualObjects(e){this._subVisualObjects=e}get cacheVisualObjects(){return this._cacheVisualObjects}set cacheVisualObjects(e){this._cacheVisualObjects=e}updateLayer(e){this._layer=e}updateDefaultLayer(){if(this._subVisualObjects!=null)for(const e of this._subVisualObjects)e.updateDefaultLayer()}get visible(){return this._isVisible&&!this._isDisable}set visible(e){this._isVisible=e}get refreshRect(){return this._drawRect}autoLayoutContents(e){if(this._subVisualObjects!=null)for(let t=0;t<this._subVisualObjects.length;t++)this._subVisualObjects[t].autoLayoutContents(e)}redraw(e,t,i,r){if(this._subVisualObjects!=null)for(let n=0;n<this._subVisualObjects.length;n++){let a=this._subVisualObjects[n];a.visible&&a.needRedraw(e,i)&&a.redraw(e,t,i,r)}}redraw_Offscreen(e,t,i,r){if(this._subVisualObjects!=null)for(let n=0;n<this._subVisualObjects.length;n++){let a=this._subVisualObjects[n];a.visible&&a.needRedraw(e,i)&&a.redraw_Offscreen(e,t,i,r)}}drawSelectedRect(e,t,i,r,n){e.save(),e.lineWidth=3,e.strokeStyle=this.webCanvas.canvasTheme.hitColor,e.strokeRect(t,i,r,n),e.restore()}needRedraw(e,t){return!e||!t?!1:e.RectToScreen(this._drawRect).intersectsWith(t)}get TopLeftPoint(){let e=this._drawRect;return new y(e.x,e.y)}get TopCenterPoint(){let e=this._drawRect;return new y(e.x+e.w/2,e.y)}get TopRightPoint(){let e=this._drawRect;return new y(e.x+e.w,e.y)}get LeftCenterPoint(){let e=this._drawRect;return new y(e.x,e.y+e.h/2)}get RightCenterPoint(){let e=this._drawRect;return new y(e.x+e.w,e.y+e.h/2)}get BottomLeftPoint(){let e=this._drawRect;return new y(e.x,e.y+e.h)}get BottomCenterPoint(){let e=this._drawRect;return new y(e.x+e.w/2,e.y+e.h)}get BottomRightPoint(){let e=this._drawRect;return new y(e.x+e.w,e.y+e.h)}addSonObj(e){this._subVisualObjects.push(e),e._parentVisualObject=this}addSonObjs(e){for(let t of e)this._subVisualObjects.push(t),t._parentVisualObject=this}addCacheObj(e){this._cacheVisualObjects.push(e),e._parentVisualObject=this}addCacheObjs(e){for(let t of e)this._cacheVisualObjects.push(t),t._parentVisualObject=this}deleteSonObj(e){let t=this._subVisualObjects.indexOf(e);t>=0&&this._subVisualObjects.splice(t,1)}deleteCacheObj(e){let t=this._cacheVisualObjects.indexOf(e);t>=0&&this._cacheVisualObjects.splice(t,1)}getHitTestHandle(e){let t;return this.getHandlePointDistance(1,e)<=this._handlePointRadius?t=1:this.getHandlePointDistance(2,e)<=this._handlePointRadius?t=2:this.getHandlePointDistance(3,e)<=this._handlePointRadius?t=3:this.getHandlePointDistance(8,e)<=this._handlePointRadius?t=8:this.getHandlePointDistance(4,e)<=this._handlePointRadius?t=4:this.getHandlePointDistance(7,e)<=this._handlePointRadius?t=7:this.getHandlePointDistance(6,e)<=this._handlePointRadius?t=6:this.getHandlePointDistance(5,e)<=this._handlePointRadius&&(t=5),t}getHandlePointDistance(e,t){let i=new y;switch(e){case 1:i=this.TopLeftPoint;break;case 2:i=this.TopCenterPoint;break;case 3:i=this.TopRightPoint;break;case 8:i=this.LeftCenterPoint;break;case 4:i=this.RightCenterPoint;break;case 7:i=this.BottomLeftPoint;break;case 6:i=this.BottomCenterPoint;break;case 5:i=this.BottomRightPoint;break}let r=t.x-i.x,n=t.y-i.y;return Math.sqrt(r*r+n*n)}HitTestObject(e,t,i){if(this._subVisualObjects!=null)for(let r=0;r<this._subVisualObjects.length;r++){let n=this._subVisualObjects[r];if(n.visible&&!n._hitTestProtected&&(n=this._subVisualObjects[r].HitTestObject(e,t,i),n))return n}return null}HitTestOnHandlePoint(e){this._drawRect;let t=this.getHandlePointDistance(1,e),i=this.getHandlePointDistance(2,e),r=this.getHandlePointDistance(3,e),n=this.getHandlePointDistance(8,e),a=this.getHandlePointDistance(4,e),l=this.getHandlePointDistance(7,e),h=this.getHandlePointDistance(6,e),o=this.getHandlePointDistance(5,e);return t<=this._handlePointRadius||i<=this._handlePointRadius||r<=this._handlePointRadius||n<=this._handlePointRadius||a<=this._handlePointRadius||l<=this._handlePointRadius||h<=this._handlePointRadius||o<=this._handlePointRadius}HitTestOnFrame(e,t){return e.RectToScreen(this._drawRect).pointOnFrame(t)}ContainPoint(e,t,i=1){let r=e.x,n=e.y,a=0,l=0,h=0;for(let o=0;o<t.length-1;o++){const u=t[o],c=t[o+1];if(u.x===c.x){if(r>u.x)continue;c.y>u.y&&n>=u.y&&n<=c.y&&(l++,a++),c.y<u.y&&n>=c.y&&n<=u.y&&(h++,a++);continue}const f=(c.y-u.y)/(c.x-u.x),g=(n-u.y)/f+u.x;r>g||(c.x>u.x&&g>=u.x&&g<=c.x&&(a++,f>=0?l++:h++),c.x<u.x&&g>=c.x&&g<=u.x&&(a++,f>=0?h++:l++))}return i===1?l-h!==0:a%2===1}keepMouseInteract(){return this._isDraggingBound}OnClick(e,t){return!1}OnDoubleClick(e,t){return!1}OnMouseDown(e,t){return!1}OnMouseLButtonUp(e,t){return!1}OnMouseMidButtonUp(e,t){return!1}OnContextMenu(e,t){return!1}OnMouseHover(e,t){return!1}OnMouseEnter(e,t){return!1}OnMouseLeave(e){return!1}OnMouseOut(e,t){return!1}MoveHandleTo(e,t,i){}OnMouseWheel(e,t){return!1}MoveTo(e,t){}OnDragEnter(e,t){}OnDragDrop(e,t){}OnDragLeave(e,t){}OnDragOver(e,t){}OnEditProperties(e,t){return!0}HitTest(e,t){return this}HitTestForFastDrag(e,t){return null}HitTestData(e,t){return null}OnDeselect(e){return!1}dispose(){}}class ut{constructor(s){d(this,"type_","vplot");d(this,"dpi_",100);d(this,"dpm_");d(this,"dprm_");d(this,"dprm_i_");d(this,"dpMilSec_");d(this,"grad_ym_s_");d(this,"grad_ym_l_");d(this,"unit","meter");d(this,"ratio_",200);d(this,"timeRatio_",200);d(this,"segmentDataDepth_",50);d(this,"startIndex_",-99990);d(this,"endIndex_",99990);d(this,"startTime_",0);d(this,"endTime_",60*60*1e3);d(this,"shortUnit");d(this,"showUnit",!0);d(this,"timeScale",!1);d(this,"isAutoDepth_",!0);this.update(s)}update(s={}){this.dpi_=s.dpi??this.dpi_,this.timeScale=s.timeScale??this.timeScale,this.dpm_=this.dpi_/.0254,this.dprm_=this.dpm_*(1/(s.ratio??this.ratio_)),this.dprm_i_=this.dprm_,this.dpMilSec_=this.dpi_/((s.timeRatio??this.timeRatio_)*1e3),this.grad_ym_s_=this.to_multiple_125_(20/this.dprm_i_),this.grad_ym_l_=this.grad_ym_s_*5,this.unit=s.unit??this.timeScale?"s":"meter",this.ratio_=s.ratio??this.ratio_,this.timeRatio_=s.timeRatio??this.timeRatio_,this.segmentDataDepth_=s.segmentDataDepth??this.segmentDataDepth_,this.startIndex_=s.startIndex??this.startIndex_,this.endIndex_=s.endIndex??this.endIndex_,this.startTime_=s.startTime??this.startTime_,this.endTime_=s.endTime??this.endTime_,this.showUnit=s.showUnit??this.showUnit,this.shortUnit=this.checkShortUnit,this.isAutoDepth_=s.isAutoDepth??this.isAutoDepth_}get checkShortUnit(){return this.unit=="meter"?"m":this.unit=="inch"?"in":this.unit=="foot"?"ft":this.unit}get ratio(){return this.ratio_}set ratio(s){s=Math.min(s,99999),this.ratio_=Math.max(s,10),this.update()}get timeRatio(){return this.timeRatio_}set timeRatio(s){s=Math.min(s,Number.MAX_VALUE),this.timeRatio_=Math.max(s,1),this.update()}get isAutoDepth(){return this.isAutoDepth_}set isAutoDepth(s){this.isAutoDepth_=s}to_multiple_125_(s){let e=[1,2,5],t=0;for(let i=-3;i<=3;i++)for(let r=0;r<e.length;r++){let n=e[r]*Math.pow(10,i);if(s>t&&s<n)return n;t=n}return s}}class p{constructor(){d(this,"deepClone",(s,e=new WeakMap)=>{if(s===null||typeof s!="object")return s;if(e.has(s))return e.get(s);let t;if(s instanceof Date)t=new Date(s);else if(s instanceof RegExp)t=new RegExp(s.source,s.flags);else if(s instanceof Map)t=new Map,s.forEach((i,r)=>t.set(r,this.deepClone(i,e)));else if(s instanceof Set)t=new Set,s.forEach(i=>t.add(this.deepClone(i,e)));else{t=Array.isArray(s)?[]:{},e.set(s,t);for(const i in s)Object.prototype.hasOwnProperty.call(s,i)&&(t[i]=this.deepClone(s[i],e))}return t})}static getLineIntersectPoint(s,e,t,i){const r=(s.x-t.x)*(e.y-t.y)-(s.y-t.y)*(e.x-t.x),n=(s.x-i.x)*(e.y-i.y)-(s.y-i.y)*(e.x-i.x);if(r*n>=0)return s.sameAs(t)?t:e.sameAs(i)?i:null;const a=(t.x-s.x)*(i.y-s.y)-(t.y-s.y)*(i.x-s.x),l=a+r-n;if(a*l>=0)return null;const h=a/(n-r),o=h*(e.x-s.x),u=h*(e.y-s.y);return new y(s.x+o,s.y+u)}static inLine(s,e,t,i,r,n){let a=(i-e)/(t-s);return n==a*r}static hitTestLine(s,e,t,i,r,n){let a=t-s,l=i-e,h=Math.abs(l*r.x-a*r.y-s*i+t*e)/Math.sqrt(Math.pow(a,2)+Math.pow(l,2)),o=r.x>=Math.min(s,t)-n,u=Math.max(s,t)+n>=r.x,c=r.y>=Math.min(e,i)-n&&r.y<=Math.max(e,i)+n;return h<=n&&o&&u&&c}static fixNumberToString(s,e){return parseFloat(s.toFixed(e)).toString()}static deepCopy(s){let e;if(s!=null)e=Array.isArray(s)?[]:{};else return null;for(const t in s)e[t]=typeof s[t]=="object"?this.deepCopy(s[t]):s[t];return e}static checkLineCross(s,e,t,i){const r=(i.x-t.x)*(s.y-t.y)-(i.y-t.y)*(s.x-t.x),n=(e.x-s.x)*(s.y-t.y)-(e.y-s.y)*(s.x-t.x),a=(i.y-t.y)*(e.x-s.x)-(i.x-t.x)*(e.y-s.y);if(a!==0){const l=r/a,h=n/a;if(0<=l&&l<=1&&0<=h&&h<=1)return!0}return!1}static checkPointInPolygon(s,e){let t,i,r,n;t=s,i=new y(-100,s.y);let a=0;for(let l=0;l<e.length-1;l++)r=e[l],n=e[l+1],this.checkLineCross(t,i,r,n)==!0&&a++;return r=e[e.length-1],n=e[0],this.checkLineCross(t,i,r,n)==!0&&a++,a%2!=0}static checkPointAboveLine(s,e,t){let i=e.x-s.x,r=e.y-s.y,n=t.x-s.x,a=t.y-s.y;return i*a-r*n<0}static checkPointUnderLine(s,e,t){let i=e.x-s.x,r=e.y-s.y,n=t.x-s.x,a=t.y-s.y;return i*a-r*n>0}static checkSameArray(s,e){let t=!0;if(e.length==s.length){for(let i=0;i<e.length;i++)if(e[i]!=s[i])return!1}else return!1;return t}static WheelRatioK(s){return s*.1}static Sleep(s){return new Promise(e=>setTimeout(e,s))}static checkInBound(s,e,t){return e<t?s>=e&&s<=t:s>=t&&s<=e}static formatDate(s,e){const t={"M+":s.getMonth()+1,"d+":s.getDate(),"h+":s.getHours(),"m+":s.getMinutes(),"s+":s.getSeconds(),"q+":Math.floor((s.getMonth()+3)/3),S:s.getMilliseconds()};/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(s.getFullYear()+"").substr(4-RegExp.$1.length)));for(let i in t)new RegExp("("+i+")").test(e)&&(e=e.replace(RegExp.$1,RegExp.$1.length==1?t[i]:("00"+t[i]).substr((""+t[i]).length)));return e}static isEmpty(s){return s===null||s===""||s===void 0||s.length===0}static setCtxLineDash(s,e){e.lineType==D.solid?s.setLineDash([]):e.lineType==D.dash?s.setLineDash(e.dashStyle):e.lineType==D.dot&&s.setLineDash(e.dotStyle)}static findTraceDataMDFromTvd(s,e){let t,i,r=null,n=this.binarySearchTvd(s,e,.1);if(n==0&&e<s[n].tvd)return t;if(n>=0){if(i=s[n],r=s[n+1],n>0)for(;i.tvd>e&&n>0;)n-=1,i=s[n],r=s[n+1];if(n<s.length-1)for(;r.tvd<e&&n+1<s.length-1;)n+=1,i=s[n],r=s[n+1];if(i.tvd==e)return i.md;if(r!=null&&r.tvd==e)return r.md;if(r!=null&&e>i.tvd&&e<r.tvd){let a=r.tvd-i.tvd,l=r.md-i.md;t=(e-i.tvd)/a*l+i.md}}return t}static findTraceDataMDFromVs(s,e){let t,i,r=null,n=this.binarySearchVs(s,e,.1);if(n==0&&e<s[n].vs)return t;if(n>=0){if(i=s[n],r=s[n+1],n>0)for(;i.vs>e&&n>0;)n-=1,i=s[n],r=s[n+1];if(n<s.length-1)for(;r.vs<e&&n+1<s.length-1;)n+=1,i=s[n],r=s[n+1];if(i.vs==e)return i.md;if(r!=null&&r.vs==e)return r.md;if(r!=null&&e>i.vs&&e<r.vs){let a=r.vs-i.vs,l=r.md-i.md;t=(e-i.vs)/a*l+i.md}}return t}static binarySearchTvd(s,e,t=0){let i=0,r=s.length-1,n=-1;for(;i<=r;){if(i>r)return-1;n=Math.floor((i+r)/2);let a=s[n].tvd;if(Math.abs(a-e)<=t)return n;a>e?r=n-1:a<e&&(i=n+1)}return i>r&&t==0?-1:n}static binarySearchVs(s,e,t=0){let i=0,r=s.length-1,n=-1;for(;i<=r;){if(i>r)return-1;n=Math.floor((i+r)/2);let a=s[n].vs;if(Math.abs(a-e)<=t)return n;a>e?r=n-1:a<e&&(i=n+1)}return i>r&&t==0?-1:n}static guid24(){return this.S4()+this.S4()+this.S4()+this.S4()+this.S4()+this.S4()}static S4(){return((1+Math.random())*65536|0).toString(16).substring(1)}static findMinMax(s){if(s.length===0)throw new Error("数组不能为空");let e=s[0],t=s[0];for(let i=1;i<s.length;i++)s[i]>e&&(e=s[i]),s[i]<t&&(t=s[i]);return{max:e,min:t}}static isNullValue(s){return s===null||isNaN(s)}static hexToRgb(s){const e=s.replace(/^#/,"");if(e.length!==6)return null;const t=parseInt(e.substring(0,2),16),i=parseInt(e.substring(2,4),16),r=parseInt(e.substring(4,6),16);return{r:t,g:i,b:r}}static rgbToHex(s,e,t){const i=r=>{const n=r.toString(16);return n.length===1?"0"+n:n};return"#"+i(s)+i(e)+i(t)}static interpolateColorRange(s,e,t){if(t<2)return[s];const i=this.hexToRgb(s),r=this.hexToRgb(e);if(!i||!r)throw new Error("颜色格式错误，请使用 '#RRGGBB' 格式");const n=[];for(let a=0;a<t;a++){const l=a/(t-1),h=Math.round(i.r+(r.r-i.r)*l),o=Math.round(i.g+(r.g-i.g)*l),u=Math.round(i.b+(r.b-i.b)*l);n.push(this.rgbToHex(h,o,u))}return n}static randomColor(){const s=Math.floor(Math.random()*156),e=Math.floor(Math.random()*156),t=Math.floor(Math.random()*156);return`rgb(${s}, ${e}, ${t})`}static generateWaveData(s,e,t,i){const r=[];for(let a=s;a<=e;a+=2){const l=Math.sin(a/e*Math.PI*i)*t;r.push(new y(a,l))}return r}}d(p,"distanceOfPointToLine",function(s,e,t,i,r,n){const a=s-t,l=e-i,h=r-t,o=n-i,u=a*h+l*o,c=h*h+o*o;let f=-1;c!=0&&(f=u/c);let g,m;f<0?(g=t,m=i):f>1?(g=r,m=n):(g=t+f*h,m=i+f*o);const w=s-g,v=e-m;return Math.sqrt(w*w+v*v)});var ce=(_=>(_[_.Time=1]="Time",_[_.Depth=2]="Depth",_))(ce||{});const ct="LogPrs";class E extends J{constructor(e,t,i){super(e);d(this,"objId_");d(this,"label_");d(this,"unit_");d(this,"index_");d(this,"lower_",0);d(this,"upper_",100);d(this,"type_");d(this,"segmentDataDepth_",100);d(this,"segmentDataTime_",1e3*60);d(this,"headRect");d(this,"defaultHeaderHeight",40);d(this,"valueFontHeight",12);d(this,"unitFontHeight",12);d(this,"valueScaleType_","linear");d(this,"dataContainer");d(this,"intLower");d(this,"intUpper");d(this,"intScaleDiff");d(this,"lowerMouseDown_",!1);d(this,"upperMouseDown_",!1);d(this,"midScaleMouseDown_",!1);d(this,"logDataMouseDown_",!1);d(this,"lRValueChanged",!1);d(this,"onEdit_");d(this,"isRenderFileId_",!0);d(this,"fileId_");d(this,"dataName_","");d(this,"channelId_");d(this,"indexType_",2);d(this,"leftHeaderPosition_",0);d(this,"rightHeaderPosition_",100);d(this,"isCutOff_",!1);d(this,"isBackUp_",!1);d(this,"maxWarpass_",1);d(this,"maxDataPoints_",0);this.objId_=i.objId??p.guid24(),this.parentVisualObject=t,this.headRect=new C,this.isDisable=i.disable??this.isDisable,this.isVisible=i.isVisible??this.isVisible,this.updateLayer()}updateLayer(e=null){this.layer=e??this.webCanvas.findObjectTypeInfo(ct).defaultLayerNo}updateDefaultLayer(){this.updateLayer(),super.updateDefaultLayer()}get parentTrack(){return this.parentVisualObject}redraw_Offscreen(e,t,i,r){super.redraw_Offscreen(e,t,i,r)}get headerHeight(){return this.defaultHeaderHeight}set headerHeight(e){this.defaultHeaderHeight=e}get lowerStr(){return p.fixNumberToString(this.lower_,1)}get upperStr(){return p.fixNumberToString(this.upper_,1)}get parentMaxObjIndex(){if(this.parentVisualObject==null)return 0;let e=this.parentVisualObject.subVisualObjects.map(t=>t).sort(function(t,i){return t.index_-i.index_});return e.length>0?e[e.length-1].index_:0}get refreshRect(){return this.headRect}get enableDrawHeadVal(){return!0}setOtherObjScaleInTrack(e){let t=this.parentVisualObject.subVisualObjects.filter(i=>i!=this).map(i=>i);for(let i of t)(e==0||e==2)&&(i.lower_=this.lower_),(e==1||e==2)&&(i.upper_=this.upper_)}HitTestObject(e,t,i){return this.parentVisualObject.keepMouseInteract()?null:this.headRect.containPoint(e.PointToView(i))&&this.parentVisualObject.layer==t?this:this.HitTestData(e,i)!=null&&this.layer==t?this:null}HitTest(e,t){let i=this.webCanvas,r=i.topCanvasElement;return e.isObjectSelected(this)&&e.RectToScreen(this.headRect).containPoint(t)&&(e.RectToScreen(this.lowerScaleRect).containPoint(t)&&this.lowerMouseDown_?(r.style.cursor=i.leftArrowIconUrl,this.intLower=this.lower_,this.intScaleDiff=Math.abs(this.upper_-this.lower_)):e.RectToScreen(this.upperScaleRect).containPoint(t)&&this.upperMouseDown_?(r.style.cursor=i.rightArrowIconUrl,this.intUpper=this.upper_,this.intScaleDiff=Math.abs(this.upper_-this.lower_)):e.RectToScreen(this.midScaleRect).containPoint(t)&&this.midScaleMouseDown_&&(r.style.cursor="col-resize",this.intUpper=this.upper_,this.intLower=this.lower_,this.intScaleDiff=Math.abs(this.upper_-this.lower_)),this.onMouseDown&&(r.style.cursor="move")),this}MoveHandleTo(e,t,i){let r=this.webCanvas,n=r.lastMouseDownPos,a=r.topCanvasElement;r.floatCanvasElement;let l=r.ctxFloat;if(e.isObjectSelected(this)&&this.onMouseDown){a.style.cursor="move";let u=i.x-n.x,c=i.y-n.y;r.redrawTransparentFloat(e,this.parentVisualObject,this.headRect,u,c),l.restore()}let h=this.parentVisualObject,o=h.webCanvas;if(o.acceptNewDroppedItem){if(o.droppedCheckFunc(h,h.parentVisualObject,h.subVisualObjects)){o.acceptNewDroppedItem=!1;return}o.acceptNewDroppedItem=!1}}OnMouseLButtonUp(e,t){let i=this.webCanvas,r=i.topCanvasElement;return e.selectedVisualObjects.length>0&&e.isObjectSelected(this)&&(this.onMouseDown?(this.onMouseDown=!1,r.style.cursor="default",i.redrawTransparentFloat(e,null,this.headRect,0,0),this.DragDropHitTest(e,t)):this.lowerMouseDown_?this.lowerMouseDown_=!1:this.upperMouseDown_?this.upperMouseDown_=!1:this.midScaleMouseDown_?this.midScaleMouseDown_=!1:this.logDataMouseDown_&&(this.logDataMouseDown_=!1),r.style.cursor="default"),!0}OnMouseMidButtonUp(e,t){let i=this.webCanvas,r=i.topCanvasElement;return e.selectedVisualObjects.length>0&&e.isObjectSelected(this)&&this.onMouseDown&&(this.onMouseDown=!1,r.style.cursor="default",i.redrawTransparentFloat(e,null,this.headRect,0,0),this.DragDropHitTest(e,t)),!0}DragDropHitTest(e,t){if(e.drawRect.containPoint(t)){let i=null;this.parentVisualObject.parentVisualObject.objType&&this.parentVisualObject.parentVisualObject.objType=="MWCLogWell"||(i=this.parentVisualObject.parentVisualObject.subVisualObjects.map(r=>r).sort(function(r,n){return r.index_-n.index_}));for(let r=0;r<i.length;r++){let n=i[r];if(!(n.type_=="depth"||n.type_=="timeScale")&&n.objType=="Track"&&e.RectToScreen(n.headRect_).containPoint(t)){if(e.RectToScreen(n.headTitleRect_).containPoint(t)&&n.subVisualObjects.length>0){let a=n.sonPrsObjs()[0].index_;n==this.parentVisualObject?n.subVisualObjects.map(l=>l).filter(l=>l.index_>=a&&l.index_<this.index_).forEach(l=>{l.index_++}):(n.subVisualObjects.map(l=>l).filter(l=>l.index_>=a).forEach(l=>{l.index_++}),this.parentVisualObject.subVisualObjects.splice(this.parentVisualObject.subVisualObjects.indexOf(this),1),this.parentVisualObject.subVisualObjects.map(l=>l).filter(l=>l.index_>this.index_).forEach(l=>{l.index_--}),n.subVisualObjects.push(this),this.parentVisualObject=n),this.index_=a,this.webCanvas.layout(),this.webCanvas.invalidate();break}else if(n.subVisualObjects.length==0){this.parentVisualObject.subVisualObjects.splice(this.parentVisualObject.subVisualObjects.indexOf(this),1),this.parentVisualObject.subVisualObjects.map(a=>a).filter(a=>a.index_>this.index_).forEach(a=>{a.index_--}),n.subVisualObjects.push(this),this.parentVisualObject=n,this.index_=1,this.webCanvas.layout(),this.webCanvas.invalidate();break}for(let a=0;a<n.sonPrsObjs().length;a++){let l=n.sonPrsObjs()[a];if(l!=this||e.RectToScreen(this.headRect).containPoint(t)){if(e.RectToScreen(l.headRect).containPoint(t)){let o=l.index_;if(n==this.parentVisualObject){let u=Math.min(o,this.index_);u==this.index_&&u++;let c=Math.max(o,this.index_);c==this.index_&&c--,n.subVisualObjects.map(f=>f).filter(f=>f.index_>=u&&f.index_<=c).forEach(f=>{o>this.index_?f.index_--:f.index_++})}else n.subVisualObjects.map(u=>u).filter(u=>u.index_>=o).forEach(u=>{u.index_++}),this.parentVisualObject.subVisualObjects.splice(this.parentVisualObject.subVisualObjects.indexOf(this),1),n.subVisualObjects.push(this),this.parentVisualObject=n;this.index_=o,this.webCanvas.layout(),this.webCanvas.invalidate();return}else if(a==n.subVisualObjects.length-1&&e.RectToScreen(n.headRect_).containPoint(t)){let o=l.index_;n==this.parentVisualObject?(n.subVisualObjects.map(u=>u).filter(u=>u.index_>this.index_).forEach(u=>{u.index_--}),this.index_=o):(this.parentVisualObject.subVisualObjects.splice(this.parentVisualObject.subVisualObjects.indexOf(this),1),n.subVisualObjects.push(this),this.parentVisualObject=n,this.index_=o+1),this.webCanvas.layout(),this.webCanvas.invalidate();return}}}}}}}onSelected_(){}OnEditProperties(){return this.onMouseDown&&(this.onMouseDown=!1),this.onEdit_(this.objId_),!0}HitTestForFastDrag(e,t){return this.headRect.containPoint(e.PointToView(t))?(this.onMouseDown=!0,this):this.lowerScaleRect.containPoint(e.PointToView(t))?(this.lowerMouseDown_=!0,this):this.upperScaleRect.containPoint(e.PointToView(t))?(this.upperMouseDown_=!0,this):this.midScaleRect.containPoint(e.PointToView(t))?(this.midScaleMouseDown_=!0,this):super.HitTestForFastDrag(e,t)}OnContextMenu(e,t){let i=this.webCanvas,r=i.topCanvasElement;if(i.lastMouseMoveClientPos,i.invalidate(),e.RectToScreen(this.headRect).containPoint(t)&&(this.onMouseDown=!1,r.style.cursor="default",this.objType=="Curve"||this.objType=="Image"||this.objType=="Activity")){let n=i;n.prsObjHeaderContextMenuFunc!=null&&n.prsObjHeaderContextMenuFunc(this)}return!0}get scaleMouseDown(){return this.lowerMouseDown_||this.upperMouseDown_||this.midScaleMouseDown_}get lowerScaleRect(){return new C(this.headRect.x,this.headRect.y,this.headRect.w,this.headRect.h)}get upperScaleRect(){return new C(this.headRect.x,this.headRect.y,this.headRect.w,this.headRect.h)}get midScaleRect(){return new C(this.headRect.x,this.headRect.y,this.headRect.w,this.headRect.h)}vIndexToPixel(e){let t=this.webCanvas,i=t.voLogPlot.measure,r=i.timeScale?i.dpMilSec_:i.dprm_i_,n=i.timeScale?t.virtualPixelOffsetTime:t.virtualPixelOffsetY;return e*r-n}convertVtoX(e){return this.parentTrack.valueScaleType_==="log"?this.parentTrack.v_to_grid_x_(e):this.intervalMapping(e)}intervalMapping(e){if(e==null)return null;const t=this.leftHeaderPosition_/100*this.headRect.w,i=this.rightHeaderPosition_/100*this.headRect.w,r=this.lower_,n=this.upper_,a=i-t,l=n-r;this.isCutOff_&&(e<r&&(e=r),e>n&&(e=n));const h=(e-r)/l;return t+h*a}get leftPositionOfHeader(){return this.leftHeaderPosition_/100*this.headRect.w}get rightPositionOfHeader(){return this.rightHeaderPosition_/100*this.headRect.w}}const fe="Image";class _e extends E{constructor(e,t,i){super(e,t,i);d(this,"headerImage_");d(this,"paletteColors_",[]);d(this,"imgHeaderHeight",60);d(this,"totalImgHeight",0);this.dataName_=i.dataName??this.dataName_,this.objId_=i.Id,this.channelId_=i.channelId??"",this.type_="image",this.segmentDataDepth_=i.segmentDataDepth??10,this.segmentDataTime_=i.segmentDataTime??this.segmentDataTime_,this.index_=i.index??this.parentMaxObjIndex+1,this.label_=i.label,this.unit_=i.unit,this.lower_=i.lower===""?-99:i.lower,this.upper_=i.upper===""?0:i.upper,this.paletteColors_=i.paletteColors,this.visible=i.isVisible??this.visible,this.updateLayer();let r=()=>{console.log(this.dataName_,"On Edit")};this.onEdit_=i.onEdit??r,this.cacheCanvas=document.createElement("canvas"),this.objType=fe}updateLayer(e=null){this.layer=e??this.webCanvas.findObjectTypeInfo(fe).defaultLayerNo}updateDefaultLayer(){this.updateLayer(),super.updateDefaultLayer()}keepMouseInteract(){return this.isDraggingBound||this.lowerMouseDown_||this.upperMouseDown_||this.midScaleMouseDown_||this.onMouseDown||this.logDataMouseDown_}update(e){this.channelId_=e.channelId??this.channelId_,this.label_=e.label??this.label_,this.unit_=e.unit??this.unit_,this.index_=e.index??this.index_,this.lower_=e.lower??this.lower_,this.upper_=e.upper??this.upper_,this.paletteColors_=e.paletteColors??this.paletteColors_,this.visible=e.isVisible??this.visible}drawHeader(e,t,i){t.font=this.textTrait.font,t.textAlign="center",t.fillText(this.label_,this.headRect.w/2,i+12);let r=this.paletteColors_;if(r!=null){let n=(this.headRect.w-4)/r.length;t.save();let a=Math.ceil(n);for(let l=0;l<r.length;l++)t.fillStyle=r[l],t.strokeStyle=r[l],t.fillRect(2+l*n,i+14,a,12)}if(t.restore(),this.textTrait.height=this.valueFontHeight,t.font=this.textTrait.font,this.lower_!=null&&(t.textAlign="left",t.fillText(this.lower_.toString(),10,i+38)),this.upper_!=null&&(t.textAlign="right",t.fillText(this.upper_.toString(),this.headRect.w-10,i+38)),this.textTrait.height=this.unitFontHeight,t.font=this.textTrait.font,this.unit_!=null&&(t.textAlign="center",t.fillText(this.unit_,this.headRect.w/2,i+38)),e.isObjectSelected(this)){const n=this.rightPositionOfHeader-this.leftPositionOfHeader;this.drawSelectedRect(t,this.leftPositionOfHeader,i,n,this.headerHeight)}}redraw(e,t,i,r){let n=e.RectToScreen(this.parentVisualObject.bodyRect_);if(this.layer==r&&n.intersectsWith(i)){t.save();let a=this.parentVisualObject.bodyRect_,l=this.webCanvas;l.canvasTheme;let h=this.cacheCanvas,o=this.cacheCanvas.getContext("2d");h.width=a.w,h.height=a.h,o.clearRect(0,0,a.w,a.h);let u=l.virtualPixelOffsetY,c=l.virtualPixelOffsetTime,f=l.voLogPlot.measure;new window.Image;let g=this.dataContainer;if(f.timeScale){let m=l.voLogPlot.topTime,w=l.voLogPlot.bottomTime;for(let v=0;g&&v<g.sections.length;v++){let b=g.sections[v];if((b.startTime<m&&b.endTime>m||b.startTime<w&&b.endTime>w||b.startTime>=m&&b.endTime<=w)&&b.imgUrl!=null&&b.img!=null){let M=b.img;if(M.complete){let O=b.startTime*f.dpMilSec_-c,R=(b.endTime-b.startTime)*f.dpMilSec_;o.drawImage(M,0,0,M.width,M.height,.5,O,a.w-3,R)}}}}else{let m=l.voLogPlot.topIndex,w=l.voLogPlot.bottomIndex;for(let v=0;g&&v<g.sections.length;v++){let b=g.sections[v];if((b.startIndex<m&&b.endIndex>m||b.startIndex<w&&b.endIndex>w||b.startIndex>=m&&b.endIndex<=w)&&b.imgUrl!=null&&b.img!=null){let M=b.img;if(M.complete){let O=b.startIndex*f.dprm_i_-u,R=(b.endIndex-b.startIndex)*f.dprm_i_;o.drawImage(M,0,0,M.width,M.height,.5,O,a.w-3,R)}}}}t.drawImage(h,1,1),t.restore()}}}class Be{constructor(){d(this,"effectType_",0);d(this,"isUseFirstColor_",!0);d(this,"firstColor_","#16b0b0");d(this,"isUseSecondColor_",!1);d(this,"secondColor_","#ffffff");d(this,"secondColorBrightness_",1);d(this,"fillTransparency_",1);d(this,"gradientMode_",0);d(this,"gradientTransform_",0);d(this,"pictureUrl_",this.generateRandomLithUrl());d(this,"isReplacementColor",!1);d(this,"patternImage_",null);d(this,"patternCache_",null);d(this,"scaleFactor_",1)}get firstColor(){return this.firstColor_}set firstColor(s){this.firstColor_=s}setScaleFactor(s){this.scaleFactor_=Math.max(.1,s),this.patternCache_=null}setSecondColor(s){s.color!==void 0&&(this.secondColor_=s.color),s.brightness!==void 0&&(this.secondColorBrightness_=s.brightness),this.isUseSecondColor_=s.useColor}get fillTransparency(){return this.fillTransparency_}set fillTransparency(s){this.fillTransparency_=Math.max(0,Math.min(1,s))}get effectType(){return this.effectType_}set effectType(s){this.effectType_=s}get gradientMode(){return this.gradientMode_}set gradientMode(s){this.gradientMode_=s}get gradientTransform(){return this.gradientTransform_}set gradientTransform(s){this.gradientTransform_=s}setPictureUrl(s){if(this.effectType_!==0){switch(this.effectType_){case 1:case 2:this.isReplacementColor=!0,this.pictureUrl_=s??this.generateRandomLithUrl();break;case 3:this.isReplacementColor=!1,this.pictureUrl_=s??"../../../../../example/src/assets/image/0a070ac9ee527f46545d7c3a462d5dc4.png";break}this.loadPatternImage()}}generateRandomLithUrl(){return`../../../../../example/src/assets/image/LITH${(Math.floor(Math.random()*63)+1).toString().padStart(2,"0")}.BMP`}parseColor(s){if(s.startsWith("#")){const e=s.slice(1);let t=0,i=0,r=0;return e.length===3?(t=parseInt(e[0]+e[0],16),i=parseInt(e[1]+e[1],16),r=parseInt(e[2]+e[2],16)):e.length===6&&(t=parseInt(e.substring(0,2),16),i=parseInt(e.substring(2,4),16),r=parseInt(e.substring(4,6),16)),{r:t,g:i,b:r}}else if(s.startsWith("rgb")){const e=s.match(/(\d+),\s*(\d+),\s*(\d+)/);if(e)return{r:parseInt(e[1],10),g:parseInt(e[2],10),b:parseInt(e[3],10)}}return{r:0,g:0,b:0}}loadPatternImage(s){var e;if(((e=this.patternImage_)==null?void 0:e.src)===this.pictureUrl_){s==null||s();return}this.patternImage_=new Image,this.patternImage_.crossOrigin="Anonymous",this.patternImage_.src=new URL(this.pictureUrl_,typeof document>"u"&&typeof location>"u"?require("url").pathToFileURL(__filename).href:typeof document>"u"?location.href:I&&I.tagName.toUpperCase()==="SCRIPT"&&I.src||new URL("WebCanvas.umd.js",document.baseURI).href).href,this.patternImage_.onload=()=>{const t=this.patternImage_.naturalWidth*this.scaleFactor_,i=this.patternImage_.naturalHeight*this.scaleFactor_,r=document.createElement("canvas"),n=r.getContext("2d");if(r.width=Math.round(t),r.height=Math.round(i),n.imageSmoothingEnabled=!0,n.imageSmoothingQuality="high",n.drawImage(this.patternImage_,0,0,this.patternImage_.naturalWidth,this.patternImage_.naturalHeight,0,0,t,i),this.isReplacementColor){const a=n.getImageData(0,0,r.width,r.height),l=a.data,h=this.parseColor(this.firstColor_),o=this.parseColor(this.secondColor_);for(let u=0;u<l.length;u+=4){const c=l[u],f=l[u+1],g=l[u+2];c===255&&f===255&&g===255?(l[u]=o.r,l[u+1]=o.g,l[u+2]=o.b):(l[u]=h.r,l[u+1]=h.g,l[u+2]=h.b)}n.putImageData(a,0,0)}this.patternCache_=n.createPattern(r,"repeat"),s==null||s()},this.patternImage_.onerror=t=>{console.error("图片加载失败",t),this.patternCache_=null}}getFillStyle(s,e,t,i,r){if(!this.isUseFirstColor_)return"";switch(this.effectType_){case 0:const n=this.createGradient(s,e,t,i,r);return this.applyColorStops(n),n;case 1:case 2:return this.patternImage_?this.patternCache_||this.firstColor_:(this.loadPatternImage(),this.firstColor_);case 3:return this.patternImage_?this.patternCache_||this.firstColor_:(this.loadPatternImage(),this.firstColor_)}}createGradient(s,e,t,i,r){let n;const a=document.createElement("canvas"),l=a.getContext("2d");let h=0;switch(this.gradientMode_){case 0:n=s.createLinearGradient(e,t,e+i,t);break;case 1:n=s.createLinearGradient(e,t,e,t+r);break;case 2:const o=e,u=t+r,c=e+i,f=t,g=(o+c)/2,m=(u+f)/2,v=-1/((f-u)/(c-o)),b=g+(t-m)/v,M=t,O=g+(t+r-m)/v,R=t+r;n=s.createLinearGradient(b,M,O,R);break;case 3:const T=e+i,P=t+r,S=e,H=t,U=(T+S)/2,W=(P+H)/2,re=-1/((H-P)/(S-T)),L=U+(t-W)/re,j=t,z=U+(t+r-W)/re,$=t+r;n=s.createLinearGradient(L,j,z,$);break;case 4:h=Math.sqrt(Math.pow(i,2)+Math.pow(r,2)),a.width=i,a.height=r;const q=l.createLinearGradient(0,0,i,0);q.addColorStop(0,this.applyTransparency(this.firstColor_)),q.addColorStop(.5,this.applyTransparency("#654254")),q.addColorStop(1,this.applyTransparency(this.getActualSecondColor())),l.globalAlpha=.9,l.fillStyle=q,l.fillRect(0,0,i,r);const G=l.createLinearGradient(0,0,0,r);G.addColorStop(0,this.applyTransparency(this.firstColor_)),G.addColorStop(.5,this.applyTransparency("#654254")),G.addColorStop(1,this.applyTransparency(this.getActualSecondColor())),l.fillStyle=G,l.fillRect(0,0,i,r),n=s.createPattern(a,"no-repeat");break;case 5:a.width=i,a.height=r,h=Math.sqrt(Math.pow(i/2,2)+Math.pow(r/2,2));const K=l.createRadialGradient(0,r/2,0,i/2,r/2,h);K.addColorStop(0,this.applyTransparency(this.firstColor_)),K.addColorStop(1,this.applyTransparency(this.getActualSecondColor())),l.fillStyle=K,l.fillRect(0,0,i/2,r);const Pe=l.createRadialGradient(i,r/2,0,i/2,r/2,h);Pe.addColorStop(0,this.applyTransparency(this.firstColor_)),Pe.addColorStop(1,this.applyTransparency(this.getActualSecondColor())),l.fillStyle=Pe,l.fillRect(i/2,0,i/2,r);const Oe=l.createRadialGradient(i/2,0,0,i/2,0,h);Oe.addColorStop(0,this.applyTransparency(this.firstColor_)),Oe.addColorStop(1,this.applyTransparency(this.getActualSecondColor())),l.fillStyle=Oe,l.fillRect(0,0,i,r/2);const Se=l.createRadialGradient(i/2,r,0,i/2,r,h);Se.addColorStop(0,this.applyTransparency(this.firstColor_)),Se.addColorStop(1,this.applyTransparency(this.getActualSecondColor())),l.fillStyle=Se,l.fillRect(0,r/2,i,r/2),n=s.createPattern(a,"no-repeat");break}return n}applyColorStops(s){const e=this.applyTransparency(this.firstColor_),t=this.applyTransparency(this.getActualSecondColor());if(s instanceof CanvasGradient)switch(this.gradientTransform_){case 0:s.addColorStop(0,e),s.addColorStop(1,t);break;case 1:s.addColorStop(0,t),s.addColorStop(1,e);break;case 2:s.addColorStop(0,e),s.addColorStop(.5,t),s.addColorStop(1,e);break;case 3:s.addColorStop(0,t),s.addColorStop(.5,e),s.addColorStop(1,t);break}}getActualSecondColor(){if(this.isUseSecondColor_)return this.secondColor_;{const s=Math.min(1,Math.max(0,this.secondColorBrightness_)),e=Math.round(255*s);return`rgb(${e},${e},${e})`}}applyTransparency(s){if(s.startsWith("#")){const e=Math.round(255*this.fillTransparency_).toString(16).padStart(2,"0");return s+e}else{if(s.startsWith("rgba"))return s.replace(/[\d.]+\)$/,`${this.fillTransparency_})`);if(s.startsWith("rgb"))return s.replace("rgb","rgba").replace(")",`, ${this.fillTransparency_})`)}return s}}const X="FillArea";class Ee extends E{constructor(e,t,i){super(e,t,i);d(this,"isDrawHeader_",!1);d(this,"isFill_",!0);d(this,"startFillBoundaryCurveType_",0);d(this,"startFillBoundaryCurve_");d(this,"startFillBoundaryConstantValue_",0);d(this,"startFillBoundaryTrack_");d(this,"startFillBoundaryTrackPosition_",0);d(this,"endFillBoundaryCurveType_",0);d(this,"endFillBoundaryCurve_");d(this,"endFillBoundaryConstantValue_",0);d(this,"endFillBoundaryTrack_");d(this,"endFillBoundaryTrackPosition_",0);d(this,"fillConditions_",0);d(this,"isFillPureColor_",!0);d(this,"fillColor_","#000000");d(this,"fillTransparency_",1);d(this,"fillEffect_",new Be);d(this,"cutoffCurve_");d(this,"isFillCutOff_",!1);d(this,"lowCutoff_",0);d(this,"highCutoff_",0);this.type_=X,this.objType=X,this.label_=i.label??X,this.isDrawHeader_=(i==null?void 0:i.isDrawHeader)??this.isDrawHeader_,this.defaultHeaderHeight=this.isDrawHeader_?20:0,this.fillColor_=(i==null?void 0:i.fillColor)??p.randomColor();let r=()=>{console.log(this.label_,"On Edit")};this.onEdit_=i.onEdit??r,this.updateLayer(),this.update(i)}updateLayer(e=null){this.layer=e??this.webCanvas.findObjectTypeInfo(X).defaultLayerNo}update(e){let t=this.webCanvas;this.isDrawHeader_=(e==null?void 0:e.isDrawHeader)??this.isDrawHeader_,this.label_=(e==null?void 0:e.label)??this.label_,this.leftHeaderPosition_=(e==null?void 0:e.leftHeaderPosition)??this.leftHeaderPosition_,this.rightHeaderPosition_=(e==null?void 0:e.rightHeaderPosition)??this.rightHeaderPosition_,this.startFillBoundaryCurveType_=(e==null?void 0:e.startFillBoundaryCurveType)??this.startFillBoundaryCurveType_,e!=null&&e.startFillBoundaryCurveId&&(this.startFillBoundaryCurve_=t.getPrsObj(e==null?void 0:e.startFillBoundaryCurveId)??this.startFillBoundaryCurve_),this.startFillBoundaryConstantValue_=(e==null?void 0:e.startFillBoundaryConstantValue)??this.startFillBoundaryConstantValue_,e!=null&&e.startFillBoundaryTrackId&&(this.startFillBoundaryTrack_=t.getPlotTrack(e==null?void 0:e.startFillBoundaryTrackId)??this.startFillBoundaryTrack_),this.startFillBoundaryTrackPosition_=(e==null?void 0:e.startFillBoundaryTrackPosition)??this.startFillBoundaryTrackPosition_,this.startFillBoundaryTrackPosition_/=100,this.endFillBoundaryCurveType_=(e==null?void 0:e.endFillBoundaryCurveType)??this.endFillBoundaryCurveType_,e!=null&&e.endFillBoundaryCurveId&&(this.endFillBoundaryCurve_=t.getPrsObj(e==null?void 0:e.endFillBoundaryCurveId)??this.endFillBoundaryCurve_),this.endFillBoundaryConstantValue_=(e==null?void 0:e.endFillBoundaryConstantValue)??this.endFillBoundaryConstantValue_,e!=null&&e.endFillBoundaryTrackId&&(this.endFillBoundaryTrack_=t.getPlotTrack(e==null?void 0:e.endFillBoundaryTrackId)??this.endFillBoundaryTrack_),this.endFillBoundaryTrackPosition_=(e==null?void 0:e.endFillBoundaryTrackPosition)??this.endFillBoundaryTrackPosition_,this.endFillBoundaryTrackPosition_/=100,this.fillConditions_=(e==null?void 0:e.fillConditions)??this.fillConditions_,this.fillColor_=(e==null?void 0:e.fillColor)??this.fillColor_,this.isFillCutOff_=(e==null?void 0:e.isFillCutOff)??this.isFillCutOff_,e!=null&&e.cutoffCurveId&&(this.cutoffCurve_=t.getPrsObj(e==null?void 0:e.cutoffCurveId)??this.cutoffCurve_),this.lowCutoff_=(e==null?void 0:e.lowCutoff)??this.lowCutoff_,this.highCutoff_=(e==null?void 0:e.highCutoff)??this.highCutoff_,this.isFillPureColor_=(e==null?void 0:e.isFillPureColor)??this.isFillPureColor_,this.fillEffect_.effectType=e.effectType??this.fillEffect_.effectType,this.fillEffect_.gradientMode=e.effectGradientMode??this.fillEffect_.gradientMode,this.fillEffect_.gradientTransform=e.effectGradientTransform??this.fillEffect_.gradientTransform,this.fillEffect_.setPictureUrl(),this.fillEffect_.firstColor=e.effectFirstColor??this.fillEffect_.firstColor,this.fillEffect_.setScaleFactor(1),this.fillTransparency_=e.fillTransparency??this.fillTransparency_,this.fillEffect_.fillTransparency=this.fillTransparency_}drawHeader(e,t,i){if(this.defaultHeaderHeight=this.isDrawHeader_?20:0,this.isDrawHeader_){if(t.save(),t.font=this.textTrait.font,t.textAlign="center",t.lineWidth=2,t.globalAlpha=this.fillTransparency_,t.fillStyle=this.isFillPureColor_?this.fillColor_:this.fillEffect_.getFillStyle(t,this.leftPositionOfHeader+1,i+1,this.rightPositionOfHeader-this.leftPositionOfHeader-2,this.headerHeight-2),t.fillRect(this.leftPositionOfHeader+1,i+1,this.rightPositionOfHeader-this.leftPositionOfHeader-2,this.headerHeight-2),t.fillStyle=this.lineTrait.foreColor,t.fillText(this.label_,(this.leftPositionOfHeader+this.rightPositionOfHeader)/2,i+15),e.isObjectSelected(this)){const r=this.rightPositionOfHeader-this.leftPositionOfHeader;this.drawSelectedRect(t,this.leftPositionOfHeader,i,r,this.headerHeight)}t.restore()}}redraw(e,t,i,r){let n=e.RectToScreen(this.parentTrack.bodyRect_);if(this.layer!=r||!n.intersectsWith(i))return;let a=this.webCanvas;const l={"0-0":(o,u)=>this.handleTrackPositionToTrackPosition(o,u),"0-1":(o,u)=>this.handleTrackPositionToCurve(o,u),"1-0":(o,u)=>this.handleCurveToTrackPosition(o,u),"1-1":(o,u)=>this.handleCurveToCurve(o,u),"1-2":(o,u)=>this.handleCurveToConstant(o,u),"1-3":(o,u)=>this.handleCurveToBackup(o,u),"2-1":(o,u)=>this.handleConstantToCurve(o,u),"3-1":(o,u)=>this.handleBackupToCurve(o,u)},h=`${this.startFillBoundaryCurveType_}-${this.endFillBoundaryCurveType_}`;if(this.isFill_&&l[h])t.save(),t.globalAlpha=this.fillTransparency_,t.fillStyle=this.fillColor_,t.lineJoin="round",l[h](t,a),t.restore();else return}handleTrackPositionToTrackPosition(e,t){let i=this.startFillBoundaryTrack_;if(!i)return;let r=this.endFillBoundaryTrack_;if(!r)return;const n=i.bodyRect_,a=r.bodyRect_,l=Math.min(n.x+n.w*this.startFillBoundaryTrackPosition_,a.x+a.w*this.endFillBoundaryTrackPosition_),h=0,o=Math.abs(n.x+n.w*this.startFillBoundaryTrackPosition_-(a.x+a.w*this.endFillBoundaryTrackPosition_)),u=Math.max(n.h,a.h);t.setRectClip(e,new C(0,0,t.clientWidth,n.h)),e.fillStyle=this.isFillPureColor_?this.fillColor_:this.fillEffect_.getFillStyle(e,l,h,o,u),e.fillRect(l,h,o,u)}handleTrackPositionToCurve(e,t){let i=this.startFillBoundaryTrack_;if(!i)return;let r=this.endFillBoundaryCurve_;if(!r)return;let n=r.parentTrack;const a=i.bodyRect_,l=n.bodyRect_;let h=l.x<a.x,o=r.getCurveViewAreaPixelsDataPoints();if(o.length===0)return;let u=[],c=a.w*this.startFillBoundaryTrackPosition_;for(let b=0;b<o.length;b++){let M=o[b];u.push(new y(c,M.y))}let f=o[0].y,g=o[o.length-1].y;if(o.length===0)return;let w=this.adjustCurveDataPoints(u,f,g),v=this.adjustCurveDataPoints(o,f,g);if(a.x===l.x){t.setRectClip(e,new C(a.x,0,a.w,a.h)),e.fillStyle=this.isFillPureColor_?this.fillColor_:this.fillEffect_.getFillStyle(e,a.x,0,a.w,a.h);let b=this.findIntersections(w,v);this.sameTrackFill(e,a,null,r,b,w,v,null,h)}else{let b=a.x<l.x;t.setRectClip(e,new C(b?a.x:l.x,0,b?l.x+l.w-a.x:a.x+a.w-l.x,a.h)),e.fillStyle=this.isFillPureColor_?this.fillColor_:this.fillEffect_.getFillStyle(e,b?a.x:l.x,0,b?l.x+l.w-a.x:a.x+a.w-l.x,a.h),this.differentTrackFill(e,a,l,w,v)}}handleCurveToTrackPosition(e,t){let i=this.startFillBoundaryCurve_;if(!i)return;let r=i.parentTrack,n=this.endFillBoundaryTrack_;if(!n)return;const a=r.bodyRect_,l=n.bodyRect_;let h=a.x<l.x,o=i.getCurveViewAreaPixelsDataPoints();if(o.length===0)return;let u=[],c=l.w*this.endFillBoundaryTrackPosition_;for(let b=0;b<o.length;b++){let M=o[b];u.push(new y(c,M.y))}let f=o[0].y,g=o[o.length-1].y;if(o.length===0)return;let w=this.adjustCurveDataPoints(o,f,g),v=this.adjustCurveDataPoints(u,f,g);if(a.x===l.x){t.setRectClip(e,new C(a.x,0,a.w,a.h)),e.fillStyle=this.isFillPureColor_?this.fillColor_:this.fillEffect_.getFillStyle(e,a.x,0,a.w,a.h);let b=this.findIntersections(w,v);this.sameTrackFill(e,l,i,null,b,w,v,null,h)}else t.setRectClip(e,new C(h?a.x:l.x,0,h?l.x+l.w-a.x:a.x+a.w-l.x,a.h)),e.fillStyle=this.isFillPureColor_?this.fillColor_:this.fillEffect_.getFillStyle(e,h?a.x:l.x,0,h?l.x+l.w-a.x:a.x+a.w-l.x,a.h),this.differentTrackFill(e,a,l,w,v)}handleCurveToCurve(e,t){let i=this.startFillBoundaryCurve_;if(!i)return;let r=this.endFillBoundaryCurve_;if(!r)return;let n=i.parentTrack,a=r.parentTrack;const l=n.bodyRect_,h=a.bodyRect_;let o=i.getCurveViewAreaPixelsDataPoints(),u=r.getCurveViewAreaPixelsDataPoints();if(o.length===0||u.length===0)return;let c=Math.max(o[0].y,u[0].y),f=Math.min(o[o.length-1].y,u[u.length-1].y),g=Math.min(o.length,u.length);if(g===0)return;let m,w;if(l.x!==h.x){let v=l.x<h.x;t.setRectClip(e,new C(v?l.x:h.x,0,v?h.x+h.w-l.x:l.x+l.w-h.x,l.h)),e.fillStyle=this.isFillPureColor_?this.fillColor_:this.fillEffect_.getFillStyle(e,v?l.x:h.x,0,v?h.x+h.w-l.x:l.x+l.w-h.x,l.h),m=this.adjustCurveDataPoints(o,c,f),w=this.adjustCurveDataPoints(u,c,f),this.differentTrackFill(e,l,h,m,w)}else{t.setRectClip(e,new C(l.x,0,l.w,l.h)),e.fillStyle=this.isFillPureColor_?this.fillColor_:this.fillEffect_.getFillStyle(e,l.x,0,l.w,l.h);let v=this.adjustCurveDataPoints(o,c,f),b=this.adjustCurveDataPoints(u,c,f),M=o.length===g;M?(m=v,w=this.interpolateCurveDataPoints(m,u,c,f,m.length)):(w=b,m=this.interpolateCurveDataPoints(w,o,c,f,w.length));let O=M?u:o,R=this.findIntersections(m,w);this.sameTrackFill(e,l,i,r,R,m,w,O,M)}}handleCurveToConstant(e,t){let i=this.startFillBoundaryCurve_;if(!i)return;const n=i.parentTrack.bodyRect_;let a=i.getCurveViewAreaPixelsDataPoints();if(a.length===0)return;let l=[],h=i.intervalMapping(this.endFillBoundaryConstantValue_);for(let w=0;w<a.length;w++){let v=a[w];l.push(new y(h,v.y))}let o=a[0].y,u=a[a.length-1].y;if(a.length===0)return;let f=this.adjustCurveDataPoints(a,o,u),g=this.adjustCurveDataPoints(l,o,u);t.setRectClip(e,new C(n.x,0,n.w,n.h)),e.fillStyle=this.isFillPureColor_?this.fillColor_:this.fillEffect_.getFillStyle(e,n.x,0,n.w,n.h);let m=this.findIntersections(f,g);this.sameTrackFill(e,n,i,null,m,f,g,null,!0)}handleCurveToBackup(e,t){let i=this.startFillBoundaryCurve_;if(!i)return;this.endFillBoundaryTrackPosition_=0;let r=i.parentTrack,n=r;if(!n)return;const a=r.bodyRect_,l=n.bodyRect_;a.x==a.x?t.setRectClip(e,new C(a.x,0,a.w,a.h)):t.setRectClip(e,new C(0,0,t.clientWidth,a.h));let h=a.x<=l.x,o=h?l.x+l.w*this.endFillBoundaryTrackPosition_-(a.x+a.w):a.x,u=i.getCurveViewAreaPixelsDataPoints();if(u.length===0)return;e.beginPath();let c=!0;for(let m of u)if(!p.isEmpty(m.y)){let w=h?m.x+a.x:m.x+o,v=m.y;c?(c=!1,e.moveTo(w,v)):e.lineTo(w,v)}const f=u[0],g=u[u.length-1];h?(e.lineTo(l.x+l.w*this.endFillBoundaryTrackPosition_,g.y),e.lineTo(l.x+l.w*this.endFillBoundaryTrackPosition_,f.y)):(e.lineTo(l.x+l.w*this.endFillBoundaryTrackPosition_,g.y),e.lineTo(l.x+l.w*this.endFillBoundaryTrackPosition_,f.y)),e.fill()}handleConstantToCurve(e,t){let i=this.endFillBoundaryCurve_;if(!i)return;const n=i.parentTrack.bodyRect_;let a=i.getCurveViewAreaPixelsDataPoints();if(a.length===0)return;let l=[],h=i.intervalMapping(this.startFillBoundaryConstantValue_);for(let w=0;w<a.length;w++){let v=a[w];l.push(new y(h,v.y))}let o=a[0].y,u=a[a.length-1].y;if(a.length===0)return;let f=this.adjustCurveDataPoints(l,o,u),g=this.adjustCurveDataPoints(a,o,u);t.setRectClip(e,new C(n.x,0,n.w,n.h)),e.fillStyle=this.isFillPureColor_?this.fillColor_:this.fillEffect_.getFillStyle(e,n.x,0,n.w,n.h);let m=this.findIntersections(f,g);this.sameTrackFill(e,n,null,i,m,f,g,null,!0)}handleBackupToCurve(e,t){let i=this.endFillBoundaryCurve_;if(!i)return;this.startFillBoundaryTrackPosition_=0;let r=i.parentTrack;const n=r.bodyRect_,a=r.bodyRect_;n.x==a.x?t.setRectClip(e,new C(n.x,0,n.w,n.h)):t.setRectClip(e,new C(0,0,t.clientWidth,n.h));let l=a.x<=n.x,h=l?n.x+n.w*this.startFillBoundaryTrackPosition_-(a.x+a.w):a.x,o=i.getCurveViewAreaPixelsDataPoints();if(o.length===0)return;e.beginPath();let u=!0;for(let g of o)if(!p.isEmpty(g.y)){let m=l?g.x+a.x:g.x+h,w=g.y;u?(u=!1,e.moveTo(m,w)):e.lineTo(m,w)}const c=o[0],f=o[o.length-1];l?(e.lineTo(n.x+n.w*this.startFillBoundaryTrackPosition_,f.y),e.lineTo(n.x+n.w*this.startFillBoundaryTrackPosition_,c.y)):(e.lineTo(n.x+n.w*this.startFillBoundaryTrackPosition_,f.y),e.lineTo(n.x+n.w*this.startFillBoundaryTrackPosition_,c.y)),e.fill()}interpolateCurveDataPoints(e,t,i,r,n){let a=[];for(let l=0;l<e.length;l++){let h=e[l];if(h.y<i||h.y>r)continue;let o=this.getInterpolatedPoint(t,h.y);o!==null&&a.push(o)}for(;a.length<n;)if(a.length>=2){let l=a[a.length-1],h=a[0];if(l&&h){let o=this.interpolateX(h,l,l.y+1);a.push({x:o,y:l.y+1})}}else a.push({x:0,y:i});return a.length>n&&(a=a.slice(0,n)),a}adjustCurveDataPoints(e,t,i){let r=[],n=this.getInterpolatedPoint(e,t);n&&r.push(n);let a=e.filter(h=>h.y>=t&&h.y<=i);r.push(...a);let l=this.getInterpolatedPoint(e,i);return l&&r.push(l),r}getInterpolatedPoint(e,t){let i=this.findIndexOfIntersection(e,t);if(i>=0&&i<e.length){if(e[i].y===t)return{x:e[i].x,y:t};if(i>0&&i<e.length){let r=e[i-1],n=e[i];return{x:this.interpolateX(r,n,t),y:t}}}return null}findIndexOfIntersection(e,t){let i=0,r=e.length-1;for(;i<=r;){let n=Math.floor((i+r)/2),a=e[n];if(a.y===t)return n;a.y<t?i=n+1:r=n-1}return i}interpolateX(e,t,i){let r=e.x,n=e.y,a=t.x,l=t.y;return r+(i-n)*(a-r)/(l-n)}differentTrackFill(e,t,i,r,n){let a=t.x<=i.x;e.beginPath();let l=!0;if(a){for(let h of r)if(!p.isEmpty(h.y)){let o=h.x+t.x,u=h.y;l?(l=!1,e.moveTo(o,u)):e.lineTo(o,u)}for(let h=n.length-1;h>=0;h--){let o=n[h];if(!p.isEmpty(o.y)){let u=o.x+i.x,c=o.y;e.lineTo(u,c)}}}else{for(let h of n)if(!p.isEmpty(h.y)){let o=h.x+i.x,u=h.y;l?(l=!1,e.moveTo(o,u)):e.lineTo(o,u)}for(let h=r.length-1;h>=0;h--){let o=r[h];if(!p.isEmpty(o.y)){let u=o.x+t.x,c=o.y;e.lineTo(u,c)}}}e.closePath(),e.fill()}sameTrackFill(e,t,i,r,n,a,l,h,o){let u=[],c=[];if(n.length===0)u.push(a),c.push(l);else{let m=[],w=[],v=n[0];this.addPointsUpToIntersection(a,v,m),this.addPointsUpToIntersection(l,v,w),u.push(m),c.push(w);for(let M=0;M<n.length-1;M++){let O=n[M],R=n[M+1];m=[],w=[],this.addPointsInIntersectionRange(a,O,R,m),this.addPointsInIntersectionRange(l,O,R,w),u.push(m),c.push(w)}let b=n[n.length-1];m=[],w=[],this.addPointsFromIntersection(a,b,m),this.addPointsFromIntersection(l,b,w),u.push(m),c.push(w)}let f=[],g=[];if(this.isFillCutOff_){const m=this.cutoffCurve_;for(let w=0;w<u.length;w++){if(i&&m.objId_===i.objId_){const v=i.intervalMapping(this.lowCutoff_),b=i.intervalMapping(this.highCutoff_);let M=[];for(const O of u[w])if(O.x>=v&&O.x<=b)M.push(O);else if(M.length>0){const R=Math.min(...M.flat().map(S=>S.y)),T=Math.max(...M.flat().map(S=>S.y));let P=[];for(const S of c[w])S.y>=R&&S.y<=T?P.push(S):P.length>0&&(g.push(P),P=[]);f.push(M),M=[]}}if(r&&m.objId_===r.objId_){const v=r.intervalMapping(this.lowCutoff_),b=r.intervalMapping(this.highCutoff_);let M=[];for(const O of c[w])if(O.x>=v&&O.x<=b)M.push(O);else if(M.length>0){const R=Math.min(...M.flat().map(S=>S.y)),T=Math.max(...M.flat().map(S=>S.y));let P=[];for(const S of u[w])S.y>=R&&S.y<=T?P.push(S):P.length>0&&(f.push(P),P=[]);g.push(M),M=[]}}}}else f=u,g=c;switch(this.fillConditions_){case 0:break;case 1:for(let m=0;m<f.length;m++){let w=f[m].length===1,v=w?f[m][0].x:f[m][1].x,b=w?g[m][0].x:g[m][1].x;v<=b&&(f.splice(m,1),g.splice(m,1),m--)}break;case 2:for(let m=0;m<f.length;m++){let w=f[m].length===1,v=w?f[m][0].x:f[m][1].x,b=w?g[m][0].x:g[m][1].x;v>=b&&(f.splice(m,1),g.splice(m,1),m--)}break}for(let m=0;m<f.length;m++)this.fillRegionV2(e,f[m],g[m],t.x,h,o)}findIntersections(e,t){let i=[];for(let r=1;r<e.length;r++){let n=e[r-1],a=e[r],l=t[r-1],h=t[r];if(this.doLinesIntersect(n,a,l,h)){let o=this.calculateIntersection(n,a,l,h);i.push(new y(o.x,o.y))}}return i}doLinesIntersect(e,t,i,r){function n(u,c,f){return(c.x-u.x)*(f.y-u.y)-(c.y-u.y)*(f.x-u.x)}let a=n(e,t,i),l=n(e,t,r),h=n(i,r,e),o=n(i,r,t);return a*l<0&&h*o<0}calculateIntersection(e,t,i,r){let n=e.x,a=e.y,l=t.x,h=t.y,o=i.x,u=i.y,c=r.x,f=r.y;const g=n===l,m=o===c;if(g&&m)return null;if(g){let O=n,T=(f-u)/(c-o)*(O-o)+u;return{x:O,y:T}}if(m){let O=o,T=(h-a)/(l-n)*(O-n)+a;return{x:O,y:T}}let w=(h-a)/(l-n),v=(f-u)/(c-o);if(w===v)return null;let b=(u-a+w*n-v*o)/(w-v),M=w*(b-n)+a;return{x:b,y:M}}addPointsUpToIntersection(e,t,i){for(let r=0;r<e.length;r++)e[r].y<t.y&&i.push(e[r]);i.push(t)}addPointsInIntersectionRange(e,t,i,r){r.push(t);for(let n=0;n<e.length;n++)e[n].y>t.y&&e[n].y<i.y&&r.push(e[n]);r.push(i)}addPointsFromIntersection(e,t,i){i.push(t);for(let r=0;r<e.length;r++)e[r].y>t.y&&i.push(e[r])}fillRegionV2(e,t,i,r,n,a){if(!(t.length===0||i.length===0)){if(e.beginPath(),a){for(let l=0;l<t.length;l++)l===0?e.moveTo(t[l].x+r,t[l].y):e.lineTo(t[l].x+r,t[l].y);for(let l=i.length-1;l>=1;l--){let h=i[l],o=i[l-1];if(e.lineTo(h.x+r,h.y),n)for(let u=n.length-1;u>=0;u--){let c=n[u];c.y<h.y&&c.y>o.y&&e.lineTo(c.x+r,c.y)}e.lineTo(o.x+r,o.y)}}else{for(let l=0;l<i.length;l++)l===0?e.moveTo(i[l].x+r,i[l].y):e.lineTo(i[l].x+r,i[l].y);for(let l=t.length-1;l>=1;l--){let h=t[l],o=t[l-1];if(e.lineTo(h.x+r,h.y),n)for(let u=n.length-1;u>=0;u--){let c=n[u];c.y<h.y&&c.y>o.y&&e.lineTo(c.x+r,c.y)}e.lineTo(o.x+r,o.y)}}e.closePath(),e.fill()}}}const ge="Curve";class me extends E{constructor(e,t,i){super(e,t,i);d(this,"showConstantValue",!1);d(this,"constantValue",0);d(this,"showLine",!0);d(this,"showStepLine",!1);d(this,"showMarker",!1);d(this,"markerShape",0);d(this,"markerSize",10);d(this,"showBar",!1);d(this,"barWidth",5);d(this,"barPosition",0);d(this,"showDigit",!1);d(this,"digitDecimal",1);d(this,"digitPosition",.5);d(this,"showDigitTime",!1);d(this,"digitTimeDecimal",0);d(this,"markerColor","#000000");d(this,"markerDrawType",0);d(this,"markerDrawRows",1);d(this,"markerDrawIndexSpacing",.1);d(this,"drawSpecDimensionIndex_",0);this.type_="curve",this.dataName_=i.dataName??this.dataName_,this.objId_=i.Id,this.channelId_=i.channelId??"",this.dataName_=i.dataName??this.dataName_,this.channelId_=i.channelId??this.channelId_,this.isRenderFileId_=i.isRenderFileId??this.isRenderFileId_,this.fileId_=this.dataName_.substring(0,this.dataName_.indexOf(":")),this.indexType_=i.indexType??this.indexType_,this.segmentDataDepth_=i.segmentDataDepth??this.segmentDataDepth_,this.segmentDataTime_=i.segmentDataTime??this.segmentDataTime_,this.label_=i.label,this.unit_=i.unit,this.index_=i.index??this.parentMaxObjIndex+1,this.lower_=i.lower===""?-99:i.lower,this.upper_=i.upper===""?0:i.upper;let r=t.parentVisualObject.measure;this.showLine=(i==null?void 0:i.showLine)??this.showLine,this.showMarker=(i==null?void 0:i.showMarker)??this.showMarker,this.markerShape=(i==null?void 0:i.markerShape)??this.markerShape,this.markerSize=(i==null?void 0:i.markerSize)??this.markerSize,this.markerColor=(i==null?void 0:i.markerColor)??this.markerColor,this.barWidth=(i==null?void 0:i.barWidth)??this.barWidth,this.barPosition=(i==null?void 0:i.barPosition)??this.barPosition,this.markerDrawType=(i==null?void 0:i.markerDrawType)??this.markerDrawType,this.markerDrawRows=(i==null?void 0:i.markerDrawRows)??this.markerDrawRows,this.markerDrawIndexSpacing=(i==null?void 0:i.markerDrawIndexSpacing)??r.timeScale?6e4:5,(i==null?void 0:i.showMarker)!=null&&(i!=null&&i.showMarker)&&this.recalcMarkerData(),this.showConstantValue=i.showConstantValue??this.showConstantValue,this.constantValue=i.constantValue??0,this.lineTrait.lineType=i.lineStyle??this.lineTrait.lineType,this.lineTrait.width=i.lineThickness??this.lineTrait.width,this.lineTrait.foreColor=i.color??this.lineTrait.foreColor,this.textTrait.family=(i==null?void 0:i.FontFamily)??this.textTrait.family,this.textTrait.foreColor=(i==null?void 0:i.color)??this.textTrait.foreColor,this.textTrait.style=(i==null?void 0:i.FontStyle)??this.textTrait.style,this.textTrait.height=(i==null?void 0:i.titleFontHeight)??this.textTrait.height,this.valueFontHeight=(i==null?void 0:i.valueFontHeight)??this.valueFontHeight,this.unitFontHeight=(i==null?void 0:i.unitFontHeight)??this.unitFontHeight,this.updateLayer();let n=()=>{console.log(this.dataName_,"On Edit")};this.onEdit_=i.onEdit??n,this.objType=ge}updateLayer(e=null){this.layer=e??this.webCanvas.findObjectTypeInfo(ge).defaultLayerNo}updateDefaultLayer(){this.updateLayer(),super.updateDefaultLayer()}update(e){this.dataName_=e.dataName??this.dataName_,this.channelId_=e.channelId??this.channelId_,this.isRenderFileId_=e.isRenderFileId??this.isRenderFileId_,this.fileId_=this.dataName_.substring(0,this.dataName_.indexOf(":")),this.indexType_=e.indexType??this.indexType_,this.label_=e.label??this.label_,this.unit_=e.unit??this.unit_,this.lRValueChanged=(e==null?void 0:e.lower)&&(e==null?void 0:e.lower)!=this.lower_,this.lower_=e.lower??this.lower_,e!=null&&e.upper&&(e==null?void 0:e.upper)!=this.upper_&&(this.lRValueChanged=!0),this.upper_=e.upper??this.upper_,this.parentTrack.syncLogObjScaleAdjustment&&(this.parentTrack.setPrsObjsLowerScale(this.lower_),this.parentTrack.setPrsObjsUpperScale(this.upper_)),this.index_=e.index??this.index_,this.isVisible=e.isVisible??this.isVisible,this.lineTrait.lineType=e.lineStyle?e.lineStyle:this.lineTrait.lineType,this.lineTrait.width=e.lineThickness??this.lineTrait.width,this.lineTrait.foreColor=e.color??this.lineTrait.foreColor,this.showLine=(e==null?void 0:e.showLine)??this.showLine,this.showStepLine=(e==null?void 0:e.showStepLine)??this.showStepLine,this.showMarker=(e==null?void 0:e.showMarker)??this.showMarker,this.markerShape=(e==null?void 0:e.markerShape)??this.markerShape,this.markerSize=(e==null?void 0:e.markerSize)??this.markerSize,this.showBar=(e==null?void 0:e.showBar)??this.showBar,this.barWidth=(e==null?void 0:e.barWidth)??this.barWidth,this.barPosition=(e==null?void 0:e.barPosition)??this.barPosition,this.showDigit=(e==null?void 0:e.showDigit)??this.showDigit,this.digitDecimal=(e==null?void 0:e.digitDecimal)??this.digitDecimal,this.digitPosition=(e==null?void 0:e.digitPosition)??this.digitPosition,this.showDigitTime=(e==null?void 0:e.showDigitTime)??this.showDigitTime,this.digitTimeDecimal=(e==null?void 0:e.digitTimeDecimal)??this.digitTimeDecimal,this.markerColor=(e==null?void 0:e.markerColor)??this.markerColor,this.markerDrawType=(e==null?void 0:e.markerDrawType)??this.markerDrawType,this.markerDrawRows=(e==null?void 0:e.markerDrawRows)??this.markerDrawRows,this.markerDrawIndexSpacing=(e==null?void 0:e.markerDrawIndexSpacing)??this.markerDrawIndexSpacing,this.leftHeaderPosition_=(e==null?void 0:e.leftHeaderPosition)??this.leftHeaderPosition_,this.rightHeaderPosition_=(e==null?void 0:e.rightHeaderPosition)??this.rightHeaderPosition_,this.isCutOff_=(e==null?void 0:e.isCutOff)??this.isCutOff_,this.isBackUp_=(e==null?void 0:e.isBackUp)??this.isBackUp_,this.maxWarpass_=(e==null?void 0:e.maxWarpass)??this.maxWarpass_;let t=!1;e!=null&&e.markerDrawRows&&(e==null?void 0:e.markerDrawRows)!=this.markerDrawRows&&(t=!0),e!=null&&e.markerDrawIndexSpacing&&(e==null?void 0:e.markerDrawIndexSpacing)!=this.markerDrawIndexSpacing&&(t=!0),this.markerDrawType=(e==null?void 0:e.markerDrawType)??this.markerDrawType,this.markerDrawRows=(e==null?void 0:e.markerDrawRows)??this.markerDrawRows,this.markerDrawIndexSpacing=(e==null?void 0:e.markerDrawIndexSpacing)??this.markerDrawIndexSpacing,((e==null?void 0:e.showMarker)!=null&&(e!=null&&e.showMarker)||(e==null?void 0:e.markerDrawType)!=null||t)&&this.recalcMarkerData(),this.showConstantValue=(e==null?void 0:e.showConstantValue)??this.showConstantValue,this.constantValue=(e==null?void 0:e.constantValue)??this.constantValue,this.textTrait.family=(e==null?void 0:e.FontFamily)??this.textTrait.family,this.textTrait.foreColor=(e==null?void 0:e.color)??this.textTrait.foreColor,this.textTrait.style=(e==null?void 0:e.FontStyle)??this.textTrait.style,this.textTrait.height=(e==null?void 0:e.titleFontHeight)??this.textTrait.height,this.valueFontHeight=(e==null?void 0:e.valueFontHeight)??this.valueFontHeight,this.unitFontHeight=(e==null?void 0:e.unitFontHeight)??this.unitFontHeight,this.drawSpecDimensionIndex_=(e==null?void 0:e.drawSpecDimensionIndex)??this.drawSpecDimensionIndex_}drawHeader(e,t,i){switch(t.save(),t.font=this.textTrait.font,t.textAlign="center",t.fillText(this.label_,(this.leftPositionOfHeader+this.rightPositionOfHeader)/2,i+15),t.strokeStyle=this.lineTrait.foreColor,t.lineWidth=2,t.beginPath(),t.moveTo(this.leftPositionOfHeader,i+20),t.lineTo(this.rightPositionOfHeader,i+20),this.lineTrait.lineType===D.dash?t.setLineDash(this.lineTrait.dashStyle):this.lineTrait.lineType===D.dot&&t.setLineDash(this.lineTrait.dotStyle),t.stroke(),this.textTrait.height=this.valueFontHeight,t.font=this.textTrait.font,this.valueScaleType_){case"linear":case"log":this.lower_!=null&&(t.textAlign="left",t.fillText(this.lower_.toString(),this.leftPositionOfHeader+10,i+35)),this.upper_!=null&&(t.textAlign="right",t.fillText(this.upper_.toString(),this.rightPositionOfHeader-10,i+35));break}this.textTrait.height=this.unitFontHeight,t.font=this.textTrait.font;let r=0;if(this.unit_!=null&&(t.textAlign="center",r=t.measureText(this.unit_).width,t.fillText(this.unit_,(this.leftPositionOfHeader+this.rightPositionOfHeader)/2,i+35)),this.fileId_!==null&&this.fileId_!==""&&this.isRenderFileId_&&t.fillText("["+this.fileId_+"]",(this.leftPositionOfHeader+this.rightPositionOfHeader)/2+r+10,i+35),e.isObjectSelected(this)){const n=this.rightPositionOfHeader-this.leftPositionOfHeader;this.drawSelectedRect(t,this.leftPositionOfHeader,i,n,this.headerHeight)}t.restore()}redraw(e,t,i,r){if(!this.dataContainer)return;let n=e.RectToScreen(this.parentTrack.bodyRect_);if(this.layer!=r||!n.intersectsWith(i))return;let a=this.webCanvas,l=a.virtualPixelOffsetY,h=a.virtualPixelOffsetTime;a.canvasTheme;let o=a.voLogPlot.measure,u=o.timeScale?o.dpMilSec_:o.dprm_i_,c=o.timeScale?h:l;t.save(),a.setRectClip(t,new C(this.leftPositionOfHeader,0,this.rightPositionOfHeader-this.leftPositionOfHeader,n.h)),t.strokeStyle=this.lineTrait.foreColor,t.lineJoin="round",this.showConstantValue?this.drawConstantLine(t,n):(this.drawCurve(t,this.dataContainer,n,u,c,0),this.isBackUp_&&this.drawWrappedCurves(t,n,u,c)),t.restore()}getCurveViewAreaDataPoints(){if(!this.dataContainer)return;let e=this.webCanvas,t=e.voLogPlot.measure,i=this.dataContainer.completeData,r=t.timeScale?e.voLogPlot.topTime:e.voLogPlot.topIndex,n=t.timeScale?e.voLogPlot.bottomTime:e.voLogPlot.bottomIndex,a=t.timeScale?i.filter(u=>u.time>r).sort(function(u,c){return u.time-c.time})[0]:i.filter(u=>u.tvd>r).sort(function(u,c){return u.tvd-c.tvd})[0],l=t.timeScale?i.filter(u=>u.time<n).sort(function(u,c){return c.time-u.time})[0]:i.filter(u=>u.tvd<n).sort(function(u,c){return c.tvd-u.tvd})[0],h=i.indexOf(a);h>0&&h--;let o=i.indexOf(l);return o<i.length-1?o+=2:o++,i.slice(h,o)}getCurveViewAreaPixelsDataPoints(){let e=this.getCurveViewAreaDataPoints(),t=this.webCanvas,i=t.virtualPixelOffsetY,r=t.virtualPixelOffsetTime,n=t.voLogPlot.measure,a=n.timeScale?n.dpMilSec_:n.dprm_i_,l=n.timeScale?r:i,h=[];const o=this.leftHeaderPosition_/100*this.headRect.w;let f=-1*(this.rightHeaderPosition_/100*this.headRect.w-o);for(let g of e)if(!p.isEmpty(g.y)){let m=n.timeScale?g.time:g.tvd;this.isBackUp_?h.push(new y(this.convertVtoX(g.y[this.drawSpecDimensionIndex_])+f,m*a-l)):h.push(new y(this.convertVtoX(g.y[this.drawSpecDimensionIndex_]),m*a-l))}return h}drawConstantLine(e,t){e.beginPath();let i=this.convertVtoX(this.constantValue);e.moveTo(i,.5),e.lineTo(i,t.h-.5),e.lineWidth=this.lineTrait.width,p.setCtxLineDash(e,this.lineTrait),e.stroke()}drawCurve(e,t,i,r,n,a){e.beginPath();let l=!0;if(this.showLine&&t.fetch_(n/r,(n+i.h)/r,(h,o)=>{if(!p.isEmpty(o)){const u=this.convertVtoX(o[this.drawSpecDimensionIndex_])+a;l?(l=!1,e.moveTo(u,h*r-n)):e.lineTo(u,h*r-n)}}),this.showStepLine){let h=0;t.fetch_(n/r,(n+i.h)/r,(o,u)=>{if(!p.isEmpty(u)){const c=this.convertVtoX(u[this.drawSpecDimensionIndex_])+a;l?(l=!1,e.moveTo(c,o*r-n)):(e.lineTo(h,o*r-n),e.lineTo(c,o*r-n)),h=c}})}this.showMarker&&(t.rowFilterData.length==0&&this.recalcMarkerData(),this.markerDrawType==0?t.fetchByRow(n/r,(n+i.h)/r,(h,o)=>{let u=this.convertVtoX(o[this.drawSpecDimensionIndex_])+a;this.drawMaker(e,u,h*r-n)}):t.fetchByIndex(n/r,(n+i.h)/r,(h,o)=>{let u=this.convertVtoX(o[this.drawSpecDimensionIndex_])+a;this.drawMaker(e,u,h*r-n)})),this.showBar&&(t.rowFilterData.length==0&&this.recalcMarkerData(),this.markerDrawType==0?t.fetchByRow(n/r,(n+i.h)/r,(h,o)=>{let u=this.convertVtoX(o[this.drawSpecDimensionIndex_])+a;this.drawBar(e,u,h*r-n)}):t.fetchByIndex(n/r,(n+i.h)/r,(h,o)=>{let u=this.convertVtoX(o[this.drawSpecDimensionIndex_])+a;this.drawBar(e,u,h*r-n)})),this.showDigit&&(t.rowFilterData.length==0&&this.recalcMarkerData(),this.markerDrawType==0?t.fetchByRow(n/r,(n+i.h)/r,(h,o)=>{let u=this.convertVtoX(o[this.drawSpecDimensionIndex_])+a;this.drawDigit(e,u,h*r-n,o[this.drawSpecDimensionIndex_])}):t.fetchByIndex(n/r,(n+i.h)/r,(h,o)=>{let u=this.convertVtoX(o[this.drawSpecDimensionIndex_])+a;this.drawDigit(e,u,h*r-n,o[this.drawSpecDimensionIndex_])})),e.lineWidth=this.lineTrait.width,p.setCtxLineDash(e,this.lineTrait),e.stroke()}drawWrappedCurves(e,t,i,r){const n=this.leftHeaderPosition_/100*this.headRect.w,l=this.rightHeaderPosition_/100*this.headRect.w-n,h=new Path2D;this.drawCurveToPath(h,this.dataContainer,t,i,r),e.stroke(h);for(let o=1;o<=this.maxWarpass_;o++){let u=-o*l;e.save(),e.translate(u,0),e.stroke(h),e.restore(),u=o*l,e.save(),e.translate(u,0),e.stroke(h),e.restore()}}drawCurveToPath(e,t,i,r,n){let a=!0;t.fetch_(n/r,(n+i.h)/r,(l,h)=>{if(!p.isEmpty(h)){const o=this.convertVtoX(h[this.drawSpecDimensionIndex_]);a?(a=!1,e.moveTo(o,l*r-n)):e.lineTo(o,l*r-n)}})}drawMaker(e,t,i){e.fillStyle=this.markerColor;let r=this.markerSize;switch(this.markerShape){case 0:e.beginPath(),e.arc(t,i,r/2,0,Math.PI*2),e.fill(),e.closePath();break;case 1:e.fillRect(t-r/2,i-r/2,r,r);break;case 2:e.beginPath();let n=Math.sqrt(2)*r/2;e.moveTo(t,i-n),e.lineTo(t+n,i+n),e.lineTo(t-n,i+n),e.closePath(),e.fill();break;case 4:e.beginPath(),e.moveTo(t-r/2,i),e.lineTo(t+r/2,i),e.moveTo(t,i-r/2),e.lineTo(t,i+r/2),e.strokeStyle=this.markerColor,e.lineWidth=r/8,e.stroke(),e.closePath();break;case 3:e.beginPath(),e.moveTo(t,i-r/2),e.lineTo(t+r/2,i),e.lineTo(t,i+r/2),e.lineTo(t-r/2,i),e.closePath(),e.fill();break;case 5:e.beginPath(),e.moveTo(t,i+r/2),e.lineTo(t-r/2,i-r/2),e.lineTo(t+r/2,i-r/2),e.closePath(),e.fill();break;case 6:let a=r/2;e.beginPath();for(let l=0;l<5;l++){let h=t+a*Math.cos(l*2*Math.PI/5-Math.PI/2),o=i+a*Math.sin(l*2*Math.PI/5-Math.PI/2),u=t+a/2*Math.cos((l*2+1)*Math.PI/5-Math.PI/2),c=i+a/2*Math.sin((l*2+1)*Math.PI/5-Math.PI/2);e.lineTo(h,o),e.lineTo(u,c)}e.closePath(),e.fill();break;default:console.warn("未定义的标记类型");break}}drawBar(e,t,i){e.fillStyle=this.markerColor;const r=this.barWidth/2,n=Math.max(this.leftPositionOfHeader,Math.min(t,this.rightPositionOfHeader));switch(e.beginPath(),this.barPosition){case 0:e.fillRect(this.leftPositionOfHeader,i-r,n-this.leftPositionOfHeader,this.barWidth);break;case 1:e.fillRect(n,i-r,this.rightPositionOfHeader-n,this.barWidth);break}e.closePath()}drawDigit(e,t,i,r){e.fillStyle=this.lineTrait.foreColor,e.font="12px Arial",e.textAlign="center",e.textBaseline="middle";const n=this.leftHeaderPosition_/100*this.headRect.w+2,a=this.rightHeaderPosition_/100*this.headRect.w+2;let l;this.showDigitTime?l=this.formatTime(r,this.digitTimeDecimal):l=this.formatValue(r,this.digitDecimal);const h=n+(this.digitPosition||.5)*(a-n);e.fillText(l,h,i)}formatTime(e,t){if(t===0)return`${e.toFixed(0)} Tick`;if(t===1){const i=Math.floor(e%60),r=Math.floor(e/60%60);return`${Math.floor(e/3600).toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}`}return`${e}`}formatValue(e,t){return e.toFixed(t)}recalcMarkerData(){this.dataContainer==null||this.dataContainer.sections.length==0||(this.markerDrawType==0?this.dataContainer.recalcRowFilterData(this.markerDrawRows):this.dataContainer.recalcIndexFilterData(this.markerDrawIndexSpacing))}}const we="Wave";class We extends E{constructor(e,t,i){super(e,t,i);d(this,"drawAllWaves_",!1);d(this,"startDrawIndex_",0);d(this,"endDrawIndex_",360);d(this,"wavePixelHeight_",80);d(this,"topScale_",1e3);d(this,"baseScale_",0);d(this,"waveSpacing_",.1);d(this,"linearities_",0);d(this,"lineThickness_",1);d(this,"fillMode_",0);d(this,"fillColor_","rgba(15,167,194,0.4)");this.type_="wave",this.objId_=i.Id,this.index_=i.index??this.parentMaxObjIndex+1,this.updateLayer();let r=()=>{console.log(this.dataName_,"On Edit")};this.onEdit_=i.onEdit??r,this.objType=we,this.update(i),this.maxDataPoints_=i.maxDataPoints??this.endDrawIndex_}updateLayer(e=null){this.layer=e??this.webCanvas.findObjectTypeInfo(we).defaultLayerNo}update(e){this.dataName_=e.dataName??this.dataName_,this.channelId_=e.channelId??this.channelId_,this.isRenderFileId_=e.isRenderFileId??this.isRenderFileId_,this.fileId_=this.dataName_.substring(0,this.dataName_.indexOf(":")),this.indexType_=e.indexType??this.indexType_,this.label_=e.label??this.label_,this.unit_=e.unit??this.unit_,this.lower_=e.lower??this.lower_,this.upper_=e.upper??this.upper_,this.segmentDataDepth_=e.segmentDataDepth??100,this.segmentDataTime_=e.segmentDataTime??this.segmentDataTime_,this.lineTrait.lineType=e.lineStyle??this.lineTrait.lineType,this.lineTrait.width=e.lineThickness??this.lineTrait.width,this.lineTrait.foreColor=e.color??this.lineTrait.foreColor,this.textTrait.family=e.FontFamily??this.textTrait.family,this.textTrait.foreColor=e.color??this.textTrait.foreColor,this.textTrait.style=e.FontStyle??this.textTrait.style,this.textTrait.height=e.titleFontHeight??this.textTrait.height,this.valueFontHeight=e.valueFontHeight??this.valueFontHeight,this.unitFontHeight=e.unitFontHeight??this.unitFontHeight,this.leftHeaderPosition_=e.leftHeaderPosition??this.leftHeaderPosition_,this.rightHeaderPosition_=e.rightHeaderPosition??this.rightHeaderPosition_,this.startDrawIndex_=e.startDrawIndex??this.startDrawIndex_,this.endDrawIndex_=e.endDrawIndex??this.endDrawIndex_,this.drawAllWaves_=e.drawAllWaves??this.drawAllWaves_,this.waveSpacing_=e.waveSpacing??this.waveSpacing_,this.topScale_=e.topScale??this.topScale_,this.baseScale_=e.baseScale??this.baseScale_,this.fillMode_=e.fillMode??this.fillMode_,this.fillColor_=e.fillColor??this.fillColor_,this.lineThickness_=e.lineThickness??this.lineThickness_}drawHeader(e,t,i){switch(t.save(),t.font=this.textTrait.font,t.textAlign="center",t.fillText(this.label_,(this.leftPositionOfHeader+this.rightPositionOfHeader)/2,i+15),this.textTrait.height=this.valueFontHeight,this.valueScaleType_){case"linear":case"log":this.baseScale_!=null&&(t.textAlign="left",t.fillText(this.baseScale_.toString(),this.leftPositionOfHeader+10,i+15)),this.topScale_!=null&&(t.textAlign="right",t.fillText(this.topScale_.toString(),this.rightPositionOfHeader-10,i+15));break}switch(t.strokeStyle=this.lineTrait.foreColor,t.lineWidth=2,t.beginPath(),t.moveTo(this.leftPositionOfHeader,i+20),t.lineTo(this.rightPositionOfHeader,i+20),this.lineTrait.lineType===D.dash?t.setLineDash(this.lineTrait.dashStyle):this.lineTrait.lineType===D.dot&&t.setLineDash(this.lineTrait.dotStyle),t.stroke(),this.textTrait.height=this.valueFontHeight,this.valueScaleType_){case"linear":case"log":this.lower_!=null&&(t.textAlign="left",t.fillText(this.lower_.toString(),this.leftPositionOfHeader+10,i+35)),this.upper_!=null&&(t.textAlign="right",t.fillText(this.upper_.toString(),this.rightPositionOfHeader-10,i+35));break}this.textTrait.height=this.unitFontHeight,t.font=this.textTrait.font;let r=0;if(this.unit_!=null&&(t.textAlign="center",r=t.measureText(this.unit_).width,t.fillText(this.unit_,(this.leftPositionOfHeader+this.rightPositionOfHeader)/2,i+35)),this.fileId_!==null&&this.fileId_!==""&&this.isRenderFileId_&&t.fillText("["+this.fileId_+"]",(this.leftPositionOfHeader+this.rightPositionOfHeader)/2+r+10,i+35),e.isObjectSelected(this)){const n=this.rightPositionOfHeader-this.leftPositionOfHeader;this.drawSelectedRect(t,this.leftPositionOfHeader,i,n,this.headerHeight)}t.restore()}redraw(e,t,i,r){if(!this.dataContainer)return;let n=e.RectToScreen(this.parentTrack.bodyRect_);if(this.layer!=r||!n.intersectsWith(i))return;let a=this.webCanvas,l=a.virtualPixelOffsetY,h=a.virtualPixelOffsetTime;a.canvasTheme;let o=a.voLogPlot.measure,u=o.timeScale?o.dpMilSec_:o.dprm_i_,c=o.timeScale?h:l;t.save(),a.setRectClip(t,new C(this.leftPositionOfHeader,0,this.rightPositionOfHeader-this.leftPositionOfHeader,n.h)),t.strokeStyle=this.lineTrait.foreColor,t.lineJoin="round",this.lineTrait.lineType===D.dash?t.setLineDash(this.lineTrait.dashStyle):this.lineTrait.lineType===D.dot&&t.setLineDash(this.lineTrait.dotStyle),t.lineWidth=this.lineThickness_,p.setCtxLineDash(t,this.lineTrait),t.fillStyle=this.fillColor_,this.drawAllWaves_?this.dataContainer.fetch_(c/u,(c+n.h)/u,(f,g)=>{p.isEmpty(g)||this.drawWave(t,f*u-c,g)}):(this.dataContainer.recalcIndexFilterData(this.waveSpacing_),this.dataContainer.fetchByIndex(c/u,(c+n.h)/u,(f,g)=>{p.isEmpty(g)||this.drawWave(t,f*u-c,g)})),t.restore()}drawWave(e,t,i){let{max:r,min:n}=p.findMinMax(i),a=r-n==0?1:r-n,l=this.endDrawIndex_-this.startDrawIndex_+1,h=(this.rightPositionOfHeader-this.leftPositionOfHeader)/(l-1),o;switch(this.linearities_){case 0:o=r/(this.topScale_-this.baseScale_)*this.wavePixelHeight_/a;break;case 1:o=r/(this.topScale_-this.baseScale_)*this.wavePixelHeight_/Math.log10(r/n);break}const u=[];for(let f=this.startDrawIndex_;f<=this.endDrawIndex_;f++){const g=this.leftPositionOfHeader+h*(f-this.startDrawIndex_);let m;switch(this.linearities_){case 0:m=t-o*(i[f]-this.baseScale_),p.isNullValue(i[f])&&(m=t);break;case 1:m=t-o*Math.log10(i[f]/this.baseScale_),i[f]/this.baseScale_<=0&&(m=t);break}u.push(new y(g,m))}switch(this.fillMode_){case 1:this.fillWave(e,u,t);break;case 2:this.fillWave(e,u.map(f=>new y(f.x,Math.min(f.y,t))),t);break;case 3:this.fillWave(e,u.map(f=>new y(f.x,Math.max(f.y,t))),t);break}const c=new Path2D;c.moveTo(u[0].x,u[0].y);for(let f=1;f<u.length;f++)c.lineTo(u[f].x,u[f].y);e.stroke(c)}fillWave(e,t,i){const r=new Path2D;r.moveTo(t[0].x,t[0].y);for(let n=1;n<t.length;n++)r.lineTo(t[n].x,t[n].y);r.lineTo(t[t.length-1].x,i),r.lineTo(t[0].x,i),r.closePath(),e.fill(r)}}const Te="Trace";class Ge extends E{constructor(e,t,i){super(e,t,i);d(this,"drawAllWaves_",!1);d(this,"startDrawIndex_",0);d(this,"endDrawIndex_",359);d(this,"wavePixelHeight_",80);d(this,"topScale_",300);d(this,"baseScale_",0);d(this,"waveSpacing_",1);d(this,"startTime_",0);d(this,"isConstantST_",!0);d(this,"samplingInterval_",1);d(this,"isConstantSI_",!0);d(this,"linearities_",0);d(this,"lineThickness_",1);d(this,"fillMode_",0);d(this,"fillColor_","rgba(15,167,194,0.4)");this.type_="trace",this.objId_=i.Id,this.index_=i.index??this.parentMaxObjIndex+1,this.updateLayer();let r=()=>{console.log(this.dataName_,"On Edit")};this.onEdit_=i.onEdit??r,this.objType=Te,this.update(i),this.maxDataPoints_=i.maxDataPoints??this.endDrawIndex_}updateLayer(e=null){this.layer=e??this.webCanvas.findObjectTypeInfo(Te).defaultLayerNo}update(e){this.dataName_=e.dataName??this.dataName_,this.channelId_=e.channelId??this.channelId_,this.isRenderFileId_=e.isRenderFileId??this.isRenderFileId_,this.fileId_=this.dataName_.substring(0,this.dataName_.indexOf(":")),this.indexType_=e.indexType??this.indexType_,this.segmentDataDepth_=e.segmentDataDepth??100,this.segmentDataTime_=e.segmentDataTime??this.segmentDataTime_,this.label_=e.label??this.label_,this.unit_=e.unit??this.unit_,this.lower_=e.lower??this.lower_,this.upper_=e.upper??this.upper_,this.lineTrait.lineType=e.lineStyle??this.lineTrait.lineType,this.lineTrait.width=e.lineThickness??this.lineTrait.width,this.lineTrait.foreColor=e.color??this.lineTrait.foreColor,this.textTrait.family=(e==null?void 0:e.FontFamily)??this.textTrait.family,this.textTrait.foreColor=(e==null?void 0:e.color)??this.textTrait.foreColor,this.textTrait.style=(e==null?void 0:e.FontStyle)??this.textTrait.style,this.textTrait.height=(e==null?void 0:e.titleFontHeight)??this.textTrait.height,this.valueFontHeight=(e==null?void 0:e.valueFontHeight)??this.valueFontHeight,this.unitFontHeight=(e==null?void 0:e.unitFontHeight)??this.unitFontHeight,this.leftHeaderPosition_=(e==null?void 0:e.leftHeaderPosition)??this.leftHeaderPosition_,this.rightHeaderPosition_=(e==null?void 0:e.rightHeaderPosition)??this.rightHeaderPosition_,this.startDrawIndex_=(e==null?void 0:e.startDrawIndex)??this.startDrawIndex_,this.endDrawIndex_=(e==null?void 0:e.endDrawIndex)??this.endDrawIndex_,this.drawAllWaves_=(e==null?void 0:e.drawAllWaves)??this.drawAllWaves_,this.waveSpacing_=(e==null?void 0:e.waveSpacing)??this.waveSpacing_,this.topScale_=(e==null?void 0:e.topScale)??this.topScale_,this.baseScale_=(e==null?void 0:e.baseScale)??this.baseScale_,this.fillMode_=(e==null?void 0:e.fillMode)??this.fillMode_,this.fillColor_=(e==null?void 0:e.fillColor)??this.fillColor_,this.lineThickness_=e.lineThickness??this.lineThickness_,this.isConstantST_=e.isConstantST??this.isConstantST_,this.startTime_=e.startTime??this.startTime_,this.isConstantSI_=e.isConstantSI??this.isConstantSI_,this.samplingInterval_=e.samplingInterval??this.samplingInterval_}drawHeader(e,t,i){switch(t.save(),t.font=this.textTrait.font,t.textAlign="center",t.fillText(this.label_,(this.leftPositionOfHeader+this.rightPositionOfHeader)/2,i+15),this.textTrait.height=this.valueFontHeight,t.strokeStyle=this.lineTrait.foreColor,t.lineWidth=2,t.beginPath(),t.moveTo(this.leftPositionOfHeader,i+20),t.lineTo(this.rightPositionOfHeader,i+20),this.lineTrait.lineType===D.dash?t.setLineDash(this.lineTrait.dashStyle):this.lineTrait.lineType===D.dot&&t.setLineDash(this.lineTrait.dotStyle),t.stroke(),this.textTrait.height=this.valueFontHeight,this.valueScaleType_){case"linear":case"log":this.lower_!=null&&(t.textAlign="left",t.fillText(this.lower_.toString(),this.leftPositionOfHeader+10,i+35)),this.upper_!=null&&(t.textAlign="right",t.fillText(this.upper_.toString(),this.rightPositionOfHeader-10,i+35));break}this.textTrait.height=this.unitFontHeight,t.font=this.textTrait.font;let r=0;if(this.unit_!=null&&(t.textAlign="center",r=t.measureText(this.unit_).width,t.fillText(this.unit_,(this.leftPositionOfHeader+this.rightPositionOfHeader)/2,i+35)),this.fileId_!==null&&this.fileId_!==""&&this.isRenderFileId_&&t.fillText("["+this.fileId_+"]",(this.leftPositionOfHeader+this.rightPositionOfHeader)/2+r+10,i+35),e.isObjectSelected(this)){const n=this.rightPositionOfHeader-this.leftPositionOfHeader;this.drawSelectedRect(t,this.leftPositionOfHeader,i,n,this.headerHeight)}t.restore()}redraw(e,t,i,r){if(!this.dataContainer)return;let n=e.RectToScreen(this.parentTrack.bodyRect_);if(this.layer!=r||!n.intersectsWith(i))return;let a=this.webCanvas,l=a.virtualPixelOffsetY,h=a.virtualPixelOffsetTime;a.canvasTheme;let o=a.voLogPlot.measure,u=o.timeScale?o.dpMilSec_:o.dprm_i_,c=o.timeScale?h:l;t.save(),a.setRectClip(t,new C(this.leftPositionOfHeader,0,this.rightPositionOfHeader-this.leftPositionOfHeader,n.h)),t.strokeStyle=this.lineTrait.foreColor,t.lineJoin="round",this.lineTrait.lineType===D.dash?t.setLineDash(this.lineTrait.dashStyle):this.lineTrait.lineType===D.dot&&t.setLineDash(this.lineTrait.dotStyle),t.lineWidth=this.lineThickness_,p.setCtxLineDash(t,this.lineTrait),t.fillStyle=this.fillColor_,this.drawAllWaves_?this.dataContainer.fetch_(c/u,(c+n.h)/u,(f,g)=>{p.isEmpty(g)||this.drawWave(t,f*u-c,g)}):(this.dataContainer.recalcIndexFilterData(this.waveSpacing_),this.dataContainer.fetchByIndex(c/u,(c+n.h)/u,(f,g)=>{p.isEmpty(g)||this.drawWave(t,f*u-c,g)})),t.restore()}drawWave(e,t,i){let{max:r,min:n}=p.findMinMax(i),a=r-n==0?1:r-n,l;switch(this.linearities_){case 0:l=r/(this.topScale_-this.baseScale_)*this.wavePixelHeight_/a;break;case 1:l=r/(this.topScale_-this.baseScale_)*this.wavePixelHeight_/Math.log10(r/n);break}const h=[];for(let u=this.startDrawIndex_;u<=this.endDrawIndex_;u++){const c=this.isConstantST_?this.intervalMapping(this.isConstantSI_?(u+this.startTime_)*this.samplingInterval_:u+this.startTime_):this.intervalMapping(this.isConstantSI_?u*this.samplingInterval_:u);let f;switch(this.linearities_){case 0:f=t-l*(i[u]-this.baseScale_),p.isNullValue(i[u])&&(f=t);break;case 1:f=t-l*Math.log10(i[u]/this.baseScale_),i[u]/this.baseScale_<=0&&(f=t);break}h.push(new y(c,f))}switch(this.fillMode_){case 1:this.fillWave(e,h,t);break;case 2:this.fillWave(e,h.map(u=>new y(u.x,Math.min(u.y,t))),t);break;case 3:this.fillWave(e,h.map(u=>new y(u.x,Math.max(u.y,t))),t);break}const o=new Path2D;o.moveTo(h[0].x,h[0].y);for(let u=1;u<h.length;u++)o.lineTo(h[u].x,h[u].y);e.stroke(o)}fillWave(e,t,i){const r=new Path2D;r.moveTo(t[0].x,t[0].y);for(let n=1;n<t.length;n++)r.lineTo(t[n].x,t[n].y);r.lineTo(t[t.length-1].x,i),r.lineTo(t[0].x,i),r.closePath(),e.fill(r)}}const Q="NMRSpectrum";class Ae extends E{constructor(e,t,i){super(e,t,i);d(this,"drawAllWaves_",!1);d(this,"startDrawIndex_",0);d(this,"endDrawIndex_",359);d(this,"wavePixelHeight_",80);d(this,"topScale_",100);d(this,"baseScale_",0);d(this,"waveSpacing_",1);d(this,"startTime_",0);d(this,"isConstantST_",!0);d(this,"samplingInterval_",1);d(this,"isConstantSI_",!0);d(this,"linearities_",0);d(this,"lineStyle_","solid");d(this,"lineColor_","#0f48c2");d(this,"lineThickness_",.5);d(this,"fillMode_",4);d(this,"fillColor_","rgba(15,167,194,1)");d(this,"waveGradientBrushes_");d(this,"lowCutOffColorValue_",0);d(this,"highCutOffColorValue_",255);d(this,"isCustomColorPalettes_",!1);this.type_=Q,this.objId_=i.Id,this.defaultHeaderHeight=80,this.objType=Q,this.updateLayer();let r=()=>{console.log(this.dataName_,"On Edit")};this.onEdit_=i.onEdit??r,this.waveGradientBrushes_=p.interpolateColorRange("#000000","#ffffff",256),this.update(i)}updateLayer(e=null){this.layer=e??this.webCanvas.findObjectTypeInfo(Q).defaultLayerNo}update(e){this.label_=e.label??this.label_,this.unit_=e.unit??this.unit_,this.index_=e.index??this.parentMaxObjIndex+1,this.lower_=e.lower??this.lower_,this.upper_=e.upper??this.upper_,this.lineColor_=e.color??this.lineColor_,this.dataName_=e.dataName??this.dataName_,this.channelId_=e.channelId??this.channelId_,this.isRenderFileId_=e.isRenderFileId??this.isRenderFileId_,this.fileId_=this.dataName_.substring(0,this.dataName_.indexOf(":")),this.segmentDataDepth_=e.segmentDataDepth??this.segmentDataDepth_,this.segmentDataTime_=e.segmentDataTime??this.segmentDataTime_,this.indexType_=e.indexType??this.indexType_,this.lineTrait.lineType=e.lineStyle??this.lineTrait.lineType,this.lineTrait.width=e.lineThickness??this.lineTrait.width,this.lineTrait.foreColor=e.color??this.lineTrait.foreColor,this.textTrait.family=e.FontFamily??this.textTrait.family,this.textTrait.foreColor=e.color??this.textTrait.foreColor,this.textTrait.style=e.FontStyle??this.textTrait.style,this.textTrait.height=e.titleFontHeight??this.textTrait.height,this.valueFontHeight=e.valueFontHeight??this.valueFontHeight,this.unitFontHeight=e.unitFontHeight??this.unitFontHeight,this.leftHeaderPosition_=e.leftHeaderPosition??this.leftHeaderPosition_,this.rightHeaderPosition_=e.rightHeaderPosition??this.rightHeaderPosition_,this.startDrawIndex_=e.startDrawIndex??this.startDrawIndex_,this.endDrawIndex_=e.endDrawIndex??this.endDrawIndex_,this.drawAllWaves_=e.drawAllWaves??this.drawAllWaves_,this.waveSpacing_=e.waveSpacing??this.waveSpacing_,this.topScale_=e.topScale??this.topScale_,this.baseScale_=e.baseScale??this.baseScale_,this.fillMode_=e.fillMode??this.fillMode_,this.fillColor_=e.fillColor??this.fillColor_,this.lower_=e.lower??this.lower_,this.upper_=e.upper??this.upper_,this.highCutOffColorValue_=e.highCutOffColorValue??this.highCutOffColorValue_,this.lowCutOffColorValue_=e.lowCutOffColorValue??this.lowCutOffColorValue_,this.isCustomColorPalettes_=e.isCustomColorPalettes??this.isCustomColorPalettes_,this.isCustomColorPalettes_&&e.colorLevels&&e.lowCutOffColor&&e.highCutOffColor?this.waveGradientBrushes_=p.interpolateColorRange(e.lowCutOffColor,e.highCutOffColor,e.colorLevels):this.isCustomColorPalettes_&&e.colorPalettes&&(this.waveGradientBrushes_=e.colorPalettes)}drawHeader(e,t,i){switch(t.save(),t.font=this.textTrait.font,t.textAlign="center",t.fillText(this.label_,(this.leftPositionOfHeader+this.rightPositionOfHeader)/2,i+15),this.textTrait.height=this.valueFontHeight,t.strokeStyle=this.lineTrait.foreColor,t.lineWidth=2,t.beginPath(),t.moveTo(this.leftPositionOfHeader,i+60),t.lineTo(this.rightPositionOfHeader,i+60),this.lineTrait.lineType===D.dash?t.setLineDash(this.lineTrait.dashStyle):this.lineTrait.lineType===D.dot&&t.setLineDash(this.lineTrait.dotStyle),t.stroke(),this.textTrait.height=this.valueFontHeight,this.valueScaleType_){case"linear":case"log":this.lower_!=null&&(t.textAlign="left",t.fillText(this.lower_.toString(),this.leftPositionOfHeader+10,i+75)),this.upper_!=null&&(t.textAlign="right",t.fillText(this.upper_.toString(),this.rightPositionOfHeader-10,i+75));break}this.textTrait.height=this.unitFontHeight,t.font=this.textTrait.font;let r=0;if(this.unit_!=null&&(t.textAlign="center",r=t.measureText(this.unit_).width,t.fillText(this.unit_,(this.leftPositionOfHeader+this.rightPositionOfHeader)/2,i+75)),this.fileId_!==null&&this.fileId_!==""&&this.isRenderFileId_&&t.fillText("["+this.fileId_+"]",(this.leftPositionOfHeader+this.rightPositionOfHeader)/2+r+10,i+75),e.isObjectSelected(this)){const n=this.rightPositionOfHeader-this.leftPositionOfHeader;this.drawSelectedRect(t,this.leftPositionOfHeader,i,n,this.headerHeight)}t.restore(),this.fillGradientWave(t,this.smoothPoints([new y(this.leftPositionOfHeader,i+60),new y(this.leftPositionOfHeader+(this.leftPositionOfHeader+this.rightPositionOfHeader)/4,i+60-20),new y(this.leftPositionOfHeader+(this.leftPositionOfHeader+this.rightPositionOfHeader)/2,i+60-10),new y(this.leftPositionOfHeader+(this.leftPositionOfHeader+this.rightPositionOfHeader)*3/4,i+60-40),new y(this.rightPositionOfHeader,i+60)],20),i+60,40,0)}redraw(e,t,i,r){if(!this.dataContainer)return;let n=e.RectToScreen(this.parentTrack.bodyRect_);if(this.layer!=r||!n.intersectsWith(i))return;let a=this.webCanvas,l=a.virtualPixelOffsetY,h=a.virtualPixelOffsetTime;a.canvasTheme;let o=a.voLogPlot.measure,u=o.timeScale?o.dpMilSec_:o.dprm_i_,c=o.timeScale?h:l;t.save(),a.setRectClip(t,new C(this.leftPositionOfHeader,0,this.rightPositionOfHeader-this.leftPositionOfHeader,n.h)),t.strokeStyle=this.lineColor_,t.lineJoin="round",t.lineWidth=this.lineThickness_,p.setCtxLineDash(t,this.lineTrait),t.fillStyle=this.fillColor_,this.drawAllWaves_?this.dataContainer.fetch_(c/u,(c+n.h)/u,(f,g)=>{p.isEmpty(g)||this.drawWave(t,f*u-c,g)}):(this.dataContainer.recalcIndexFilterData(this.waveSpacing_),this.dataContainer.fetchByIndex(c/u,(c+n.h)/u,(f,g)=>{p.isEmpty(g)||this.drawWave(t,f*u-c,g)})),t.restore()}drawWave(e,t,i){let{max:r,min:n}=p.findMinMax(i),a=r-n==0?1:r-n,l;switch(this.linearities_){case 0:l=r/(this.topScale_-this.baseScale_)*this.wavePixelHeight_/a;break;case 1:l=r/(this.topScale_-this.baseScale_)*this.wavePixelHeight_/Math.log10(r/n);break}const h=[];for(let c=this.startDrawIndex_;c<=this.endDrawIndex_;c++){const f=this.isConstantST_?this.intervalMapping(this.isConstantSI_?(c+this.startTime_)*this.samplingInterval_:c+this.startTime_):this.intervalMapping(this.isConstantSI_?c*this.samplingInterval_:c);let g;switch(this.linearities_){case 0:g=t-l*(i[c]-this.baseScale_),p.isNullValue(i[c])&&(g=t);break;case 1:g=t-l*Math.log10(i[c]/this.baseScale_),i[c]/this.baseScale_<=0&&(g=t);break}h.push(new y(f,g))}const o=this.smoothPoints(h,6);switch(this.fillMode_){case 1:this.fillWave(e,o,t);break;case 2:this.fillWave(e,o.map(c=>new y(c.x,Math.min(c.y,t))),t);break;case 3:this.fillWave(e,o.map(c=>new y(c.x,Math.max(c.y,t))),t);break;case 4:this.fillGradientWave(e,o,t,r,n);break}const u=new Path2D;u.moveTo(o[0].x,o[0].y);for(let c=1;c<o.length;c++)u.lineTo(o[c].x,o[c].y);e.stroke(u)}fillWave(e,t,i){const r=new Path2D;r.moveTo(t[0].x,t[0].y);for(let n=1;n<t.length;n++)r.lineTo(t[n].x,t[n].y);r.lineTo(t[t.length-1].x,i),r.lineTo(t[0].x,i),r.closePath(),e.fill(r)}fillGradientWave(e,t,i,r,n){if(t.length===0)return;const a=this.highCutOffColorValue_-this.lowCutOffColorValue_;if(a===0)throw new Error("highCutOffColorValue_ 与 lowCutOffColorValue 不能相等");(this.waveGradientBrushes_.length-1)/a;const l=o=>{const u=Math.abs(o-i),c=Math.max(n,Math.min(r,u)),f=(this.highCutOffColorValue_-this.lowCutOffColorValue_)/(r-n),m=(this.lowCutOffColorValue_+(c-n)*f-this.lowCutOffColorValue_)/(this.highCutOffColorValue_-this.lowCutOffColorValue_),w=Math.round(m*(this.waveGradientBrushes_.length-1));return Math.max(0,Math.min(this.waveGradientBrushes_.length-1,w))},h=.5;for(let o=0;o<t.length-1;o++){const u=t[o],c=t[o+1];e.beginPath(),e.moveTo(u.x-h,i),e.lineTo(u.x-h,u.y),e.lineTo(c.x+h,c.y),e.lineTo(c.x+h,i),e.closePath();const f=l(u.y);e.fillStyle=this.fillMode_==4?this.waveGradientBrushes_[f]:this.fillColor_,e.fill()}}smoothPoints(e,t){const i=e.length,r=e.map(h=>h.x),n=e.map(h=>h.y),a=new Array(i).fill(0),l=[new y(r[0],n[0])];this.spline(r,n,i,0,0,0,a);for(let h=0;h<i-1;h++){const o=r[h],u=r[h+1],c=(u-o)/t;for(let f=1;f<t;f++){const g=o+f*c,m=this.splint(r,n,a,i,g);m!==null&&l.push(new y(g,m))}l.push(new y(u,n[h+1]))}return l}spline(e,t,i,r,n,a,l){const h=new Array(i).fill(0);r===1?(l[0]=-.5,h[0]=3/(e[1]-e[0])*((t[1]-t[0])/(e[1]-e[0])-n)):(l[0]=0,h[0]=0);for(let o=1;o<i-1;o++){const u=(e[o]-e[o-1])/(e[o+1]-e[o-1]),c=u*l[o-1]+2;l[o]=(u-1)/c;const f=(t[o+1]-t[o])/(e[o+1]-e[o]),g=(t[o]-t[o-1])/(e[o]-e[o-1]);h[o]=(6*(f-g)/(e[o+1]-e[o-1])-u*h[o-1])/c}r===1?(l[i-1]=.5,h[i-1]=3/(e[i-1]-e[i-2])*(a-(t[i-1]-t[i-2])/(e[i-1]-e[i-2]))):l[i-1]=0;for(let o=i-2;o>=0;o--)l[o]=l[o]*l[o+1]+h[o]}splint(e,t,i,r,n){let a=0,l=r-1;for(;l-a>1;){const c=Math.floor((l+a)/2);e[c]>n?l=c:a=c}const h=e[l]-e[a];if(h===0)return null;const o=(e[l]-n)/h,u=(n-e[a])/h;return o*t[a]+u*t[l]+((o*o*o-o)*i[a]+(u*u*u-u)*i[l])*(h*h)/6}}let ft=60;const Ne=60,Ue=60,be=40,Z="Track";var ee=(_=>(_.Normal="normal",_.Depth="depth",_.TimeScale="timeScale",_))(ee||{});class ve extends J{constructor(e,t,i){super(e);d(this,"trackId_");d(this,"index_");d(this,"trackName_");d(this,"valueScaleType_","linear");d(this,"type_");d(this,"showVerticalGrid_",!0);d(this,"showHorizontalGrid_",!0);d(this,"showTitle",!0);d(this,"showMidLine",!1);d(this,"topHorizonGrid",!1);d(this,"topVerticalGrid",!1);d(this,"width_",100);d(this,"horizontalMajorGridSpacing_",10);d(this,"horizontalMidGridSpacing_",5);d(this,"horizontalMinorGridSpacing_",1);d(this,"verticalMajorGridSpacing_");d(this,"verticalMinorGridSpacing_");d(this,"showMajorString",!0);d(this,"showMidString",!0);d(this,"showMinorString",!0);d(this,"horiMajorGridLineTrait",new N);d(this,"horiMidGridLineTrait",new N);d(this,"horiMinorGridLineTrait",new N);d(this,"caliMajorGridLineTrait",new N);d(this,"caliMinorGridLineTrait",new N);d(this,"titleHeight_",30);d(this,"headTitleRect_");d(this,"headRect_");d(this,"bodyRect_");d(this,"initRect");d(this,"offsetYInit");d(this,"offsetXInit");d(this,"lower");d(this,"upper");d(this,"titleTextTrait",new k);d(this,"indexMajorTextTrait",new k);d(this,"indexMidTextTrait",new k);d(this,"indexMinorTextTrait",new k);d(this,"midLineTrait");d(this,"mainScaleObj",!1);d(this,"syncLogObjScaleAdjustment",!1);d(this,"onEdit_");d(this,"v_to_grid_x_");d(this,"headerFillColor_");d(this,"scaleFillColor_");d(this,"dataFillColor_");d(this,"isVerticalScaleOnTop_",!1);this.trackId_=t.Id??p.guid24(),this.trackName_=t.label,this.type_=t.type,this.headRect_=t.headRect?new C(t.headRect.x,t.headRect.y,t.headRect.w,t.headRect.h):new C??new C,this.bodyRect_=t.bodyRect?new C(t.bodyRect.x,t.bodyRect.y,t.bodyRect.w,t.bodyRect.h):new C??new C,this.headTitleRect_=t.headTitleRect?new C(t.headTitleRect.x,t.headTitleRect.y,t.headTitleRect.w,t.headTitleRect.h):new C??new C,this.index_=t.index??0,this.valueScaleType_=t.valueScaleType??this.valueScaleType_,this.lower=t.lower??0,this.upper=t.upper??100,this.showVerticalGrid_=t.showVerticalGrid??!0,this.showHorizontalGrid_=t.showHorizontalGrid??!0,this.topHorizonGrid=t.topHorizonGrid??this.topHorizonGrid,this.topVerticalGrid=t.topVerticalGrid??this.topVerticalGrid,this.isVerticalScaleOnTop_=t.isVerticalScaleOnTop??this.isVerticalScaleOnTop_,this.objType=Z,this.parentVisualObject=i,this.updateLayer(),this.updateWidth_(t.width),this.horizontalMajorGridSpacing_=t.horizontalMajorGridSpacing??this.horizontalMajorGridSpacing_,this.horizontalMidGridSpacing_=t.horizontalMidGridSpacing??this.horizontalMidGridSpacing_,this.horizontalMinorGridSpacing_=t.horizontalMinorGridSpacing??this.horizontalMinorGridSpacing_,this.verticalMajorGridSpacing_=t.verticalMajorGridSpacing??2,this.verticalMinorGridSpacing_=t.verticalMinorGridSpacing??5,this.horiMajorGridLineTrait.lineType=t.horiMajorLineStyle??this.horiMajorGridLineTrait.lineType,this.horiMajorGridLineTrait.foreColor=t.horiMajorLineColor??e.canvasTheme.majorGridColor,this.horiMajorGridLineTrait.width=t.horiMajorLineThickness??2,this.horiMidGridLineTrait.lineType=t.horiMidLineStyle??this.horiMidGridLineTrait.lineType,this.horiMidGridLineTrait.foreColor=t.horiMidLineColor??e.canvasTheme.gridColor,this.horiMidGridLineTrait.width=t.horiMidLineThickness??1,this.horiMinorGridLineTrait.lineType=t.horiMinorLineStyle??this.horiMinorGridLineTrait.lineType,this.horiMinorGridLineTrait.foreColor=t.horiMinorLineColor??e.canvasTheme.gridColor,this.horiMinorGridLineTrait.width=t.horiMinorLineThickness??.5,this.caliMajorGridLineTrait.lineType=t.caliMajorLineStyle??this.caliMajorGridLineTrait.lineType,this.caliMajorGridLineTrait.foreColor=t.caliMajorLineColor??e.canvasTheme.gridColor,this.caliMajorGridLineTrait.width=t.caliMajorLineThickness??1,this.caliMinorGridLineTrait.lineType=t.caliMinorLineStyle??this.caliMinorGridLineTrait.lineType,this.caliMinorGridLineTrait.foreColor=t.caliMinorLineColor??e.canvasTheme.gridColor,this.caliMinorGridLineTrait.width=t.caliMinorLineThickness??.5,this.showMajorString=t.showMajorString??this.showMajorString,this.showMidString=t.showMidString??this.showMidString,this.showMinorString=t.showMinorString??this.showMinorString;let r=()=>{console.log(this.trackName_,"On Edit")};this.onEdit_=t.onEdit??r,this.subVisualObjects=new Array;let n=t.logPrsVos;this.loadLogPrs(n),(this.type_=="depth"||this.type_=="timeScale")&&(this.headRect_.h=ft),this.titleTextTrait.family=t.titleFontFamily??e.canvasTheme.headerBoldFont.family,this.titleTextTrait.height=t.titleFontHeight??e.canvasTheme.headerBoldFont.height,this.titleTextTrait.style=t.titleFontStyle??e.canvasTheme.headerBoldFont.style,this.titleTextTrait.foreColor=t.titleColor??this.titleTextTrait.foreColor,this.indexMajorTextTrait.family=t.indexMajorFontFamily??e.canvasTheme.boldMidFont.family,this.indexMajorTextTrait.height=t.indexMajorFontHeight??e.canvasTheme.boldMidFont.height,this.indexMajorTextTrait.style=t.indexMajorFontStyle??e.canvasTheme.boldMidFont.style,this.indexMajorTextTrait.foreColor=t.indexMajorColor??this.indexMajorTextTrait.foreColor,this.indexMidTextTrait.family=t.indexMidFontFamily??e.canvasTheme.midFont.family,this.indexMidTextTrait.height=t.indexMidFontHeight??e.canvasTheme.midFont.height,this.indexMidTextTrait.style=t.indexMidFontStyle??e.canvasTheme.midFont.style,this.indexMidTextTrait.foreColor=t.indexMidColor??this.indexMajorTextTrait.foreColor,this.indexMinorTextTrait.family=t.indexMidFontFamily??e.canvasTheme.midFont.family,this.indexMinorTextTrait.height=t.indexMidFontHeight??e.canvasTheme.midFont.height,this.indexMinorTextTrait.style=t.indexMidFontStyle??e.canvasTheme.midFont.style,this.indexMinorTextTrait.foreColor=t.indexMidColor??this.indexMajorTextTrait.foreColor,this.midLineTrait=new N(null),this.midLineTrait.width=2,this.midLineTrait.foreColor="#ff0000",this.showTitle=t.showTitle??this.showTitle,this.showMidLine=t.showMidLine??this.showMidLine,this.midLineTrait.lineType=t.midLineStyle??this.midLineTrait.lineType,this.midLineTrait.width=t.midLineThickness??this.midLineTrait.width,this.midLineTrait.foreColor=t.midLinecolor??this.midLineTrait.foreColor,this.headerFillColor_=t.headerFillColor??e.canvasTheme.backgroundColor,this.scaleFillColor_=t.scaleFillColor??e.canvasTheme.backgroundColor,this.dataFillColor_=t.dataFillColor??e.canvasTheme.backgroundColor}autoLayoutContents(e){switch(this.type_){case"normal":if(this.subVisualObjects=this.subVisualObjects.map(t=>t).sort(function(t,i){return t.index_-i.index_}),this.headTitleRect_.h=this.titleHeight_,this.headRect_.h=this.titleHeight_,this.subVisualObjects)for(let t=0;t<this.subVisualObjects.length;t++){let i=this.subVisualObjects[t];i.headRect.w=this.headRect_.w,i.headRect.h=this.parPlot.showHeader?i.headerHeight:0,this.headRect_.h+=i.headerHeight}break;case"depth":case"timeScale":this.headRect_.h=be;break}this.parPlot.showHeader||(this.headTitleRect_.h=0,this.headRect_.h=0),super.autoLayoutContents(e)}update(e){this.type_=e.type??this.type_,this.trackName_=e.label??this.trackName_,this.valueScaleType_=e.valueScaleType??this.valueScaleType_,this.index_=e.index??this.index_,this.showVerticalGrid_=e.showVerticalGrid??this.showVerticalGrid_,this.showHorizontalGrid_=e.showHorizontalGrid??this.showHorizontalGrid_,this.topHorizonGrid=e.topHorizonGrid??this.topHorizonGrid,this.topVerticalGrid=e.topVerticalGrid??this.topVerticalGrid,this.horizontalMajorGridSpacing_=e.horizontalMajorGridSpacing??this.horizontalMajorGridSpacing_,this.horizontalMidGridSpacing_=e.horizontalMidGridSpacing??this.horizontalMidGridSpacing_,this.horizontalMinorGridSpacing_=e.horizontalMinorGridSpacing??this.horizontalMinorGridSpacing_,this.verticalMajorGridSpacing_=e.verticalMajorGridSpacing??this.verticalMajorGridSpacing_,this.verticalMinorGridSpacing_=e.verticalMinorGridSpacing??this.verticalMinorGridSpacing_,e.width&&this.updateWidth_(e.width),e.onEdit!=null&&(this.onEdit_=e.onEdit),this.lower=e.lower??this.lower,this.upper=e.upper??this.upper,this.horiMajorGridLineTrait.lineType=e.horiMajorLineStyle??this.horiMajorGridLineTrait.lineType,this.horiMajorGridLineTrait.foreColor=e.horihMajorLineColor??this.horiMajorGridLineTrait.foreColor,this.horiMajorGridLineTrait.width=e.horiMajorLineThickness??this.horiMajorGridLineTrait.width,this.horiMidGridLineTrait.lineType=e.horiMidLineStyle??this.horiMidGridLineTrait.lineType,this.horiMidGridLineTrait.foreColor=e.horiMidLineColor??this.horiMidGridLineTrait.foreColor,this.horiMidGridLineTrait.width=e.horiMidLineThickness??this.horiMidGridLineTrait.width,this.horiMinorGridLineTrait.lineType=e.horiMinorLineStyle??this.horiMinorGridLineTrait.lineType,this.horiMinorGridLineTrait.foreColor=e.horiMinorLineColor??this.horiMinorGridLineTrait.foreColor,this.horiMinorGridLineTrait.width=e.horiMinorLineThickness??this.horiMinorGridLineTrait.width,this.caliMajorGridLineTrait.lineType=e.caliMajorLineStyle??this.caliMajorGridLineTrait.lineType,this.caliMajorGridLineTrait.foreColor=e.caliMajorLineColor??this.caliMajorGridLineTrait.foreColor,this.caliMajorGridLineTrait.width=e.caliMajorLineThickness??this.caliMajorGridLineTrait.width,this.caliMinorGridLineTrait.lineType=e.caliMinorLineStyle??this.caliMinorGridLineTrait.lineType,this.caliMinorGridLineTrait.foreColor=e.caliMinorLineColor??this.caliMinorGridLineTrait.foreColor,this.caliMinorGridLineTrait.width=e.caliMinorLineThickness??this.caliMinorGridLineTrait.width,this.showMajorString=e.showMajorString??this.showMajorString,this.showMidString=e.showMidString??this.showMidString,this.showMinorString=e.showMinorString??this.showMinorString,this.titleTextTrait.family=e.titleFontFamily??this.titleTextTrait.family,this.titleTextTrait.height=e.titleFontHeight??this.titleTextTrait.height,this.titleTextTrait.style=e.titleFontStyle??this.titleTextTrait.style,this.titleTextTrait.foreColor=e.titleColor??this.titleTextTrait.foreColor,this.indexMajorTextTrait.family=e.indexMajorFontFamily??this.indexMajorTextTrait.family,this.indexMajorTextTrait.height=e.indexMajorFontHeight??this.indexMajorTextTrait.height,this.indexMajorTextTrait.style=e.indexMajorFontStyle??this.indexMajorTextTrait.style,this.indexMajorTextTrait.foreColor=e.indexMajorColor??this.indexMajorTextTrait.foreColor,this.indexMidTextTrait.family=e.indexMidFontFamily??this.indexMidTextTrait.family,this.indexMidTextTrait.height=e.indexMidFontHeight??this.indexMidTextTrait.height,this.indexMidTextTrait.style=e.indexMidFontStyle??this.indexMidTextTrait.style,this.indexMidTextTrait.foreColor=e.indexMidColor??this.indexMidTextTrait.foreColor,this.indexMinorTextTrait.family=e.indexMinorFontFamily??this.indexMinorTextTrait.family,this.indexMinorTextTrait.height=e.indexMinorFontHeight??this.indexMinorTextTrait.height,this.indexMinorTextTrait.style=e.indexMinorFontStyle??this.indexMinorTextTrait.style,this.indexMinorTextTrait.foreColor=e.indexMinorColor??this.indexMinorTextTrait.foreColor,this.showTitle=e.showTitle??this.showTitle,this.showMidLine=e.showMidLine??this.showMidLine,this.midLineTrait.lineType=e.midLineStyle??this.midLineTrait.lineType,this.midLineTrait.width=e.midLineThickness??this.midLineTrait.width,this.midLineTrait.foreColor=e.midLinecolor??this.midLineTrait.foreColor,this.headerFillColor_=e.headerFillColor??this.headerFillColor_,this.scaleFillColor_=e.scaleFillColor??this.scaleFillColor_,this.dataFillColor_=e.dataFillColor??this.dataFillColor_,this.isVerticalScaleOnTop_=e.isVerticalScaleOnTop??this.isVerticalScaleOnTop_}redraw(e,t,i,r){this.drawHead(e,t,i,r),this.drawBody(e,t,i,r)}drawHead(e,t,i,r){let n=e.RectToScreen(this.headRect_);if(n.h==0)return;let a=this.webCanvas,l=a.canvasTheme,h=a.voLogPlot.measure;if(n.x<e.drawRect.w&&this.layer==r&&n.intersectsWith(i)){switch(t.save(),t.translate(n.x+1,n.y),a.setRectClip(t,new C(0,0,n.x+n.w>e.drawRect.w?e.drawRect.w-n.x:n.w,n.h)),t.lineWidth=2,t.strokeStyle=l.lineColor,(i.w>n.w||i.h>n.h)&&(t.fillStyle=this.headerFillColor_,t.fillRect(0,0,n.w,this.titleHeight_),t.fillStyle=this.scaleFillColor_,t.fillRect(0,this.titleHeight_,n.w,a.clientHeight-this.titleHeight_),t.strokeRect(0,0,n.w,n.h)),t.lineWidth=2,t.strokeStyle=l.borderColor,this.type_){case"depth":t.lineWidth=1,t.font=this.titleTextTrait.font,t.fillStyle=this.titleTextTrait.foreColor,t.textAlign="center",t.fillText(this.trackName_,this.headRect_.w/2,16);let o=`1:${(h.timeScale?h.timeRatio:h.ratio).toFixed(0)}`,u=h.showUnit?`(${h.shortUnit})`:"";if(n.h>be)t.fillText(o,this.headRect_.w/2,36),t.fillText(u,this.headRect_.w/2,54);else{let m=o+" "+u;t.fillText(m,this.headRect_.w/2,36)}break;case"timeScale":t.lineWidth=1,t.font=this.titleTextTrait.font,t.fillStyle=this.titleTextTrait.foreColor,t.textAlign="center",t.fillText(this.trackName_,this.headRect_.w/2,16);let c=`S${(h.timeScale?h.timeRatio:h.ratio).toFixed(0)}`,f=h.showUnit?`(${h.shortUnit})`:"";if(n.h>be)t.fillText(c,this.headRect_.w/2,36),t.fillText(f,this.headRect_.w/2,54);else{let m=c+" "+f;t.fillText(m,this.headRect_.w/2,36)}break;case"normal":this.showTitle&&this.headTitleRect_.intersectsWith(i)&&(t.lineWidth=1,t.font=this.titleTextTrait.font,t.fillStyle=this.titleTextTrait.foreColor,t.textAlign="center",t.fillText(this.trackName_,this.headRect_.w/2,this.titleHeight_/2+5),this.subVisualObjects.length==0&&(t.strokeStyle=l.gridColor,t.lineWidth=2,t.moveTo(0,this.titleHeight_),t.lineTo(n.w,this.titleHeight_),t.stroke()));let g=this.titleHeight_;if(this.subVisualObjects!=null)for(let m=0;m<this.subVisualObjects.length;m++){let w=this.subVisualObjects[m];e.RectToScreen(w.headRect).intersectsWith(i)&&(t.lineWidth=2,t.strokeStyle=l.lineColor,t.strokeRect(0,g,n.w,w.headerHeight),t.fillStyle=w.lineTrait.foreColor,w.drawHeader(e,t,g)),g+=w.headerHeight}break}e.isObjectSelected(this)&&(this.drawSelectedRect(t,0,0,n.w,n.h),this.type_==="normal"&&(t.save(),t.strokeStyle=l.hitColor,t.lineWidth=2,t.beginPath(),t.moveTo(0,this.titleHeight_),t.lineTo(n.w,this.titleHeight_),t.closePath(),t.stroke(),t.restore())),t.restore()}}drawBody(e,t,i,r){let n=e.RectToScreen(this.bodyRect_),a=this.webCanvas,l=a.canvasTheme,h=a.voLogPlot.measure;if(n.x<e.drawRect.w){if(t.save(),t.translate(n.x+1,n.y),a.setRectClip(t,new C(0,0,n.x+n.w>e.drawRect.w?e.drawRect.w-n.x:n.w,n.h)),this.layer==r&&n.intersectsWith(i)){t.strokeStyle=l.lineColor,t.lineWidth=2,t.fillStyle=this.dataFillColor_,t.fillRect(2,0,n.w-4,n.h),t.strokeRect(0,0,n.w,n.h);let o=a.virtualPixelOffsetY,u=a.virtualPixelOffsetTime,c=this.horizontalMajorGridSpacing_*6e3*h.dpMilSec_,f=this.horizontalMidGridSpacing_*2e3*h.dpMilSec_,g=this.horizontalMinorGridSpacing_*1e3*h.dpMilSec_,m=this.horizontalMajorGridSpacing_*h.dprm_i_,w=this.horizontalMidGridSpacing_*h.dprm_i_,v=this.horizontalMinorGridSpacing_*h.dprm_i_;switch(this.type_){case"depth":t.lineWidth=2,this.showMinorString&&(t.beginPath(),t.strokeStyle=l.majorGridColor,t.font=this.indexMinorTextTrait.font,t.textAlign="center",this.iter_(o,v,n.h,T=>{let P=(o+T)/h.dprm_i_;if(T>3){t.moveTo(0,T),t.lineTo(5,T);let S=parseFloat(P.toFixed(1)).toString();t.fillText(S,this.width_/2,T+5),t.moveTo(this.width_,T),t.lineTo(this.width_-5,T)}},[w,m]),t.stroke()),this.showMidString&&(t.beginPath(),t.font=this.indexMidTextTrait.font,t.fillStyle=this.indexMidTextTrait.foreColor,t.textAlign="center",this.iter_(o,w,n.h,T=>{let P=(o+T)/h.dprm_i_;if(T>3){t.moveTo(0,T),t.lineTo(5,T);let S=parseFloat(P.toFixed(1)).toString();t.fillText(S,this.width_/2,T+5),t.moveTo(this.width_,T),t.lineTo(this.width_-5,T)}},[m]),t.stroke()),this.showMajorString&&(t.beginPath(),t.font=this.indexMajorTextTrait.font,t.fillStyle=this.indexMajorTextTrait.foreColor,t.textAlign="center",this.iter_(o,m,n.h,T=>{let P=(o+T)/h.dprm_i_;if(T>3){t.moveTo(0,T),t.lineTo(10,T);let S=parseFloat(P.toFixed(1)).toString();t.fillText(S,this.width_/2,T+5),t.moveTo(this.width_,T),t.lineTo(this.width_-10,T)}}),t.stroke()),t.restore();break;case"timeScale":t.save(),a.setRectClip(t,new C(0,0,n.w,n.h)),this.showMinorString&&(t.beginPath(),t.font=this.indexMinorTextTrait.font,t.fillStyle=this.indexMinorTextTrait.foreColor,t.textAlign="center",this.iter_(u,g,n.h,T=>{T>=0&&(t.moveTo(0,T),t.lineTo(5,T),t.moveTo(this.width_,T),t.lineTo(this.width_-5,T))},[f,c]),t.stroke()),this.showMidString&&(t.beginPath(),t.font=this.indexMidTextTrait.font,t.fillStyle=this.indexMidTextTrait.foreColor,t.textAlign="center",this.iter_(u,f,n.h,T=>{let P=(u+T)/h.dpMilSec_;T>=0&&(t.moveTo(0,T),t.lineTo(5,T),t.fillText(String(Math.round(P/1e3)),this.width_/2,T+5),t.moveTo(this.width_,T),t.lineTo(this.width_-5,T))},[c]),t.stroke()),this.showMajorString&&(t.beginPath(),t.font=this.indexMajorTextTrait.font,t.fillStyle=this.indexMajorTextTrait.foreColor,t.textAlign="center",this.iter_(u,c,n.h,T=>{let P=(u+T)/h.dpMilSec_;T>=0&&(t.moveTo(0,T),t.lineTo(10,T),t.fillText(String(Math.round(P/1e3)),this.width_/2,T+5),t.moveTo(this.width_,T),t.lineTo(this.width_-10,T))}),t.stroke()),t.restore();break;case"normal":this.showHorizontalGrid_&&(t.strokeStyle=this.horiMajorGridLineTrait.foreColor,t.lineWidth=this.horiMajorGridLineTrait.width,p.setCtxLineDash(t,this.horiMajorGridLineTrait),t.beginPath(),h.timeScale?this.iter_(u,c,n.h,T=>{t.moveTo(0,T),t.lineTo(n.w,T)}):this.iter_(o,m,n.h,T=>{t.moveTo(0,T),t.lineTo(n.w,T)}),t.stroke(),t.strokeStyle=this.horiMidGridLineTrait.foreColor,t.lineWidth=this.horiMidGridLineTrait.width,p.setCtxLineDash(t,this.horiMidGridLineTrait),t.beginPath(),h.timeScale?this.iter_(u,f,n.h,T=>{t.moveTo(0,T),t.lineTo(n.w,T)},[c]):this.iter_(o,w,n.h,T=>{t.moveTo(0,T),t.lineTo(n.w,T)},[m]),t.stroke(),t.beginPath(),t.strokeStyle=this.horiMinorGridLineTrait.foreColor,t.lineWidth=this.horiMinorGridLineTrait.width,p.setCtxLineDash(t,this.horiMinorGridLineTrait),h.timeScale?this.iter_(u,g,n.h,T=>{t.moveTo(0,T),t.lineTo(n.w,T)},[c,f]):this.iter_(o,v,n.h,T=>{t.moveTo(0,T),t.lineTo(n.w,T)},[m,w]),t.stroke());let b=0,M=this.valueScaleType_,O=this.lower,R=this.upper;if(O<R&&O>0||(M="linear"),this.showVerticalGrid_)switch(t.strokeStyle=this.caliMajorGridLineTrait.foreColor,t.lineWidth=this.caliMajorGridLineTrait.width,p.setCtxLineDash(t,this.caliMajorGridLineTrait),t.beginPath(),M){case"log":let T=Math.sign(O)*Math.log10(Math.abs(O));a.logTrackScaleNotRound?T=Math.floor(T):T=T<0?Math.floor(T):Math.ceil(T);let P=Math.sign(R)*Math.log10(Math.abs(R));a.logTrackScaleNotRound?P=Math.ceil(P):P=P<0?Math.floor(P):Math.ceil(P);let S=Math.sign(P-T);if(this.v_to_grid_x_=L=>(Math.log10(L)-T)/(P-T)*n.w,b==0){let L=10;for(let j=T;j<P;j+=S){let z,$;z=Math.pow(10,j),$=Math.pow(10,j+1);let q=($-z)/L;a.logTrackScaleNotRound&&j==T&&(z=O);for(let G=z;G<$&&!(a.logTrackScaleNotRound&&G>=R);G+=q){let K=this.v_to_grid_x_(G);t.beginPath(),t.moveTo(K,1),t.lineTo(K,n.h-2),t.stroke()}if(a.logTrackScaleNotRound&&$>=R)break}}break;case"linear":let H=this.verticalMajorGridSpacing_*this.verticalMinorGridSpacing_,U=this.verticalMajorGridSpacing_,W=n.w/H,se=n.w/U;t.save(),t.beginPath(),t.strokeStyle=this.caliMajorGridLineTrait.foreColor,t.lineWidth=this.caliMajorGridLineTrait.width,p.setCtxLineDash(t,this.caliMajorGridLineTrait);let re=[];for(let L=1;L<U;L++){let j=L*se;t.moveTo(j,1),t.lineTo(j,n.h-1),re.push(j)}t.stroke(),t.beginPath(),t.strokeStyle=this.caliMinorGridLineTrait.foreColor,t.lineWidth=this.caliMinorGridLineTrait.width,p.setCtxLineDash(t,this.caliMinorGridLineTrait);for(let L=1;L<H;L++){let j=L*W;re.indexOf(j)>=0||(t.moveTo(j,1),t.lineTo(j,n.h-1))}t.stroke(),t.restore();break}b++;break}}if(this.subVisualObjects.length>0){t.save();for(let o=0;o<this.subVisualObjects.length;o++){let u=this.subVisualObjects[o];u.visible&&u.type_!=X&&u.redraw(e,t,i,r)}t.restore()}if(this.layer===r-1&&n.intersectsWith(i)&&this.isVerticalScaleOnTop_)switch(t.strokeStyle=l.lineColor,t.lineWidth=2,this.type_){case"normal":let o=0,u=this.valueScaleType_,c=this.lower,f=this.upper;if(c<f&&c>0||(u="linear"),this.showVerticalGrid_)switch(t.strokeStyle=this.caliMajorGridLineTrait.foreColor,t.lineWidth=this.caliMajorGridLineTrait.width,p.setCtxLineDash(t,this.caliMajorGridLineTrait),t.beginPath(),u){case"log":let g=Math.sign(c)*Math.log10(Math.abs(c));a.logTrackScaleNotRound?g=Math.floor(g):g=g<0?Math.floor(g):Math.ceil(g);let m=Math.sign(f)*Math.log10(Math.abs(f));a.logTrackScaleNotRound?m=Math.ceil(m):m=m<0?Math.floor(m):Math.ceil(m);let w=Math.sign(m-g);if(this.v_to_grid_x_=T=>(Math.log10(T)-g)/(m-g)*n.w,o==0){let T=10;for(let P=g;P<m;P+=w){let S,H;S=Math.pow(10,P),H=Math.pow(10,P+1);let U=(H-S)/T;a.logTrackScaleNotRound&&P==g&&(S=c);for(let W=S;W<H&&!(a.logTrackScaleNotRound&&W>=f);W+=U){let se=this.v_to_grid_x_(W);t.beginPath(),t.moveTo(se,1),t.lineTo(se,n.h-2),t.stroke()}if(a.logTrackScaleNotRound&&H>=f)break}}break;case"linear":let v=this.verticalMajorGridSpacing_*this.verticalMinorGridSpacing_,b=this.verticalMajorGridSpacing_,M=n.w/v,O=n.w/b;t.save(),t.beginPath(),t.strokeStyle=this.caliMajorGridLineTrait.foreColor,t.lineWidth=this.caliMajorGridLineTrait.width,p.setCtxLineDash(t,this.caliMajorGridLineTrait);let R=[];for(let T=1;T<b;T++){let P=T*O;t.moveTo(P,1),t.lineTo(P,n.h-1),R.push(P)}t.stroke(),t.beginPath(),t.strokeStyle=this.caliMinorGridLineTrait.foreColor,t.lineWidth=this.caliMinorGridLineTrait.width,p.setCtxLineDash(t,this.caliMinorGridLineTrait);for(let T=1;T<v;T++){let P=T*M;R.indexOf(P)>=0||(t.moveTo(P,1),t.lineTo(P,n.h-1))}t.stroke(),t.restore();break}o++;break}t.restore()}}addSonObj(e){let t=e;super.addSonObj(t)}updateLayer(e=null){this.layer=e??this.webCanvas.findObjectTypeInfo(Z).defaultLayerNo}updateDefaultLayer(){this.updateLayer(),super.updateDefaultLayer()}sonPrsObjs(){return this.subVisualObjects.filter(e=>e instanceof E).map(e=>e).sort(function(e,t){return e.index_-t.index_})}getObjsMinLValue(){return Math.min.apply(Math,this.subVisualObjects.filter(e=>e.visible).map(function(e){return e.lower_}))}getObjsMaxRValue(){return Math.max.apply(Math,this.subVisualObjects.filter(e=>e.visible).map(function(e){return e.upper_}))}get minLValueStr(){return p.fixNumberToString(this.getObjsMinLValue(),1)}get maxRValueStr(){return p.fixNumberToString(this.getObjsMaxRValue(),1)}updateWidth_(e){this.width_=e,this.headRect_.w=this.width_,this.headTitleRect_.w=this.width_}get parPlot(){return this.parentVisualObject}OnEditProperties(e,t){return this.onMouseDown&&(this.onMouseDown=!1),this.onEdit_(this.trackId_),!0}HitTest(e,t){let r=this.webCanvas.topCanvasElement;if(e.isObjectSelected(this)){if(this.isDraggingBound&&this.HitTestOnFrame(e,t)){if(this.initRect=this.headRect_.clone(),this.webCanvas.topCanvasElement.style.cursor="e-resize",e.RectToScreen(this.headRect_).pointAtBoundLeft(t)){let n=this.parentVisualObject.subVisualObjects.map(a=>a).sort(function(a,l){return l.index_-a.index_}).find(a=>a.index_<this.index_);n&&(n.initRect=n.headRect_.clone())}return this}else if(this.onMouseDown&&(e.RectToScreen(this.headTitleRect_).containPoint(t)||e.RectToScreen(this.headRect_).containPoint(t)))return r.style.cursor="move",this}return null}MoveHandleTo(e,t,i){let r=this.webCanvas,n=r.lastMouseDownPos,a=r.topCanvasElement,l=r.virtualPixelOffsetY,h=r.virtualPixelOffsetTime,o=r.voLogPlot.measure;if(e.RectToScreen(this.bodyRect_).containPoint(i)){if(r.mouseOverSectionFunc!=null){let u=this.subVisualObjects.map(c=>c).filter(c=>c.type_=="curve");if(u!=null&&u.length>0){let c=o.timeScale?(i.y-this.bodyRect_.y+h)/o.dpMilSec_:(i.y-this.bodyRect_.y+l)/o.dprm_i_,f="";for(let v=0;v<u.length;v++){let b=u[v];if(b.dataContainer!=null){let M=b.dataContainer.fetch_nearest_logplot(c);if(M.ok&&!p.isEmpty(M.value)){c=M.index;let O=parseFloat(M.value.toFixed(2));f=f+"</br>",f=f+"<span style='color:"+b.lineTrait.foreColor+"'>"+b.label_+"："+O+"("+b.unit_+")</span>"}}}let g=i.x/r.dpr,m=i.y/r.dpr,w=new Date(c);r.mouseOverSectionFunc(o.timeScale?p.formatDate(w,"yyyy-MM-dd hh:mm:ss"):c.toFixed(2),f,g,m)}}this.onMouseHover=!0}else this.onMouseHover=!1;if(r.floatCanvasElement,r.ctxFloat,(e.selectedVisualObjects.length==0||e.selectedVisualObjects.length>0&&!e.selectedObject.onMouseDown)&&this.HitTestOnFrame(e,i)&&(a.style.cursor="e-resize"),!(!n&&!r.acceptNewDroppedItem)&&(e.RectToScreen(this.drawRect).containPoint(i)&&r.acceptNewDroppedItem&&(this.type_=="normal"&&r.droppedCheckFunc(this,this.parentVisualObject,this.subVisualObjects),r.acceptNewDroppedItem=!1),e.isObjectSelected(this))){let u=i.x-n.x;if(this.isDraggingBound&&e.drawRect.containPoint(i)){if(a.style.cursor="e-resize",e.RectToScreen(this.initRect).pointAtBoundRight(n))o.timeScale?this.updateWidth_(Math.max(this.initRect.w+u,Ue)):this.updateWidth_(Math.max(this.initRect.w+u,Ne)),e.autoLayoutContents(),r.redrawTransparentFloat(e,this.parentVisualObject,e.drawRect,0,0);else if(e.RectToScreen(this.initRect).pointAtBoundLeft(n)){let c=this.parentVisualObject.subVisualObjects.map(f=>f).sort(function(f,g){return g.index_-f.index_}).find(f=>f.index_<this.index_);c&&(o.timeScale?this.updateWidth_(Math.max(this.initRect.w+u,Ue)):c.updateWidth_(Math.max(c.initRect.w+u,Ne)),e.autoLayoutContents(),r.redrawTransparentFloat(e,this.parentVisualObject,e.drawRect,0,0))}}else if(this.onMouseDown){a.style.cursor="move";let c=i.y-n.y;r.redrawTransparentFloat(e,this,e.RectToScreen(this.headRect_),u,c)}}}OnMouseLeave(e){let t=this.webCanvas;return this.onMouseHover?(t.mouseLeaveSectionFunc&&t.mouseLeaveSectionFunc(),this.onMouseHover=!1,!0):!1}setPrsObjsLowerScale(e){for(const t of this.sonPrsObjs())t.lower_=e}setPrsObjsUpperScale(e){for(const t of this.sonPrsObjs())t.upper_=e}setPrsObjsLRScale(e,t){for(const i of this.sonPrsObjs())i.lower_=e,i.upper_=t}OnMouseWheel(e,t){let i=this.webCanvas,r=i.lastMouseMovePos,n=i.virtualPixelOffsetY,a=i.virtualPixelOffsetTime,l=i.voLogPlot.measure;if(e.RectToScreen(this.bodyRect_).containPoint(r)){if(i.mouseOverSectionFunc!=null){let h=this.subVisualObjects.map(o=>o).filter(o=>o.type_=="curve");if(h!=null&&h.length>0){let o=l.timeScale?(r.y-this.bodyRect_.y+a)/l.dpMilSec_:(r.y-this.bodyRect_.y+n)/l.dprm_i_,u="";for(let m=0;m<h.length;m++){let w=h[m];if(w.dataContainer!=null){let v=w.dataContainer.fetch_nearest_logplot(o);if(v.ok&&!p.isEmpty(v.value)){o=v.index;let b=parseFloat(v.value.toFixed(2));u=u+"</br>",u=u+"<span style='color:"+w.lineTrait.foreColor+"'>"+w.label_+"："+b+"("+w.unit_+")</span>"}}}let c=r.x/i.dpr,f=r.y/i.dpr,g=new Date(o);i.mouseOverSectionFunc(l.timeScale?p.formatDate(g,"yyyy-MM-dd hh:mm:ss"):o.toFixed(2),u,c,f)}}this.onMouseHover=!0}else this.onMouseHover=!1;return!0}HitTestOnFrame(e,t){if(this.parentVisualObject&&this.parentVisualObject.subVisualObjects){let i=this.parentVisualObject.subVisualObjects.map(r=>r).sort(function(r,n){return r.index_-n.index_})[0];return e.RectToScreen(this.headRect_).pointAtBoundLeft(t)&&i!=this||e.RectToScreen(this.headRect_).pointAtBoundRight(t)||e.RectToScreen(this.bodyRect_).pointAtBoundLeft(t)&&i!=this||e.RectToScreen(this.bodyRect_).pointAtBoundRight(t)}else return!1}HitTestForFastDrag(e,t){if(this.HitTestOnFrame(e,t))return this.isDraggingBound=!0,this;if(this.headTitleRect_.containPoint(e.PointToView(t)))return this.onMouseDown=!0,this;if(e.visualObjects[0].subVisualObjects.indexOf(this)>=0&&this.bodyRect_.containPoint(e.PointToView(t)))return this.onMouseDown=!0,this;for(let i=0;i<this.subVisualObjects.length;i++){let r=this.subVisualObjects[i];if(r.headRect.containPoint(e.PointToView(t)))return r}return this.headRect_.containPoint(e.PointToView(t))?(this.onMouseDown=!0,this):super.HitTestForFastDrag(e,t)}OnMouseLButtonUp(e,t){let i=this.webCanvas,r=i.topCanvasElement;return e.selectedVisualObjects.length>0&&e.isObjectSelected(this)&&(this.onMouseDown&&(this.onMouseDown=!1,r.style.cursor="default",i.redrawTransparentFloat(e,null,this.headRect_,0,0),this.DragDropHitTest(e,t)),this.isDraggingBound&&(this.isDraggingBound=!1,r.style.cursor="default",i.redrawTransparentFloat(e,null,e.drawRect,0,0),e.selectObject(null,!1),i.layout(),i.invalidate())),!0}OnMouseMidButtonUp(e,t){let r=this.webCanvas.topCanvasElement;return e.selectedVisualObjects.length>0&&e.isObjectSelected(this)&&this.onMouseDown&&(this.onMouseDown=!1,r.style.cursor="default"),!0}OnMouseRButtonUp(e,t){let r=this.webCanvas.topCanvasElement;return this.onMouseDown&&(this.onMouseDown=!1,r.style.cursor="default"),!0}DragDropHitTest(e,t){if(e.drawRect.containPoint(t)){let i=this.parentVisualObject.subVisualObjects.map(r=>r).sort(function(r,n){return r.index_-n.index_});this.webCanvas,e.drawRect;for(let r=0;r<i.length;r++){let n=i[r],a=e.RectToScreen(n.drawRect);if(n!=this){if(a.containPoint(t)){let l=n.index_,h=Math.min(l,this.index_);h==this.index_&&h++;let o=Math.max(l,this.index_);o==this.index_&&o--,i.filter(u=>u.index_>=h&&u.index_<=o).forEach(u=>{l>this.index_?u.index_--:u.index_++}),this.index_=l,this.webCanvas.layout(),this.webCanvas.invalidate();break}else if(r==i.length-1&&a.x+a.w<t.x){let l=n.index_;i.filter(h=>h.index_>this.index_).forEach(h=>{h.index_--}),this.index_=l,this.webCanvas.layout(),this.webCanvas.invalidate();break}}}}}OnContextMenu(e,t){let i=this.webCanvas,r=i.topCanvasElement;return i.lastMouseMoveClientPos,i.invalidate(),(e.RectToScreen(this.bodyRect_).containPoint(t)||e.RectToScreen(this.headRect_).containPoint(t))&&(this.onMouseDown=!1,r.style.cursor="default",i.trackContextMenuFunc!=null&&i.trackContextMenuFunc(this)),!0}after_layout_(){this.update_v_to_grid_x_(),this.drawRect.w=this.headRect_.w,this.drawRect.h=this.headRect_.h+this.bodyRect_.h}update_v_to_grid_x_(){if(this.type_!=="normal")return;let e=this.webCanvas,t=this.bodyRect_,i=this.lower,r=this.upper,n=this.valueScaleType_;if(i<r&&i>0||(n="linear"),n=="log"){let a=Math.sign(i)*Math.log10(Math.abs(i));a=a<0?Math.floor(a):Math.ceil(a);let l=Math.sign(r)*Math.log10(Math.abs(r));l=l<0?Math.floor(l):Math.ceil(l),e.logTrackScaleNotRound&&(a=Math.sign(i)*Math.log10(Math.abs(i)),l=Math.sign(r)*Math.log10(Math.abs(r))),this.v_to_grid_x_=h=>((h>0?Math.log10(h):0)-a)/(l-a)*t.w}for(let a=0;a<this.subVisualObjects.length;a++){let l=this.subVisualObjects[a];if(l.visible&&l.type_=="curve"){let h=l,o=h.lower_,u=h.upper_,c=this.valueScaleType_;if(o<u&&o>0||(c="linear"),c=="log"){let f=Math.sign(o)*Math.log10(Math.abs(o));f=f<0?Math.floor(f):Math.ceil(f);let g=Math.sign(u)*Math.log10(Math.abs(u));g=g<0?Math.floor(g):Math.ceil(g),e.logTrackScaleNotRound&&(f=Math.sign(o)*Math.log10(Math.abs(o)),g=Math.sign(u)*Math.log10(Math.abs(u))),h.v_to_grid_x_=m=>((m>0?Math.log10(m):0)-f)/(g-f)*t.w}else{let f=t.w/(u-o);h.v_to_grid_x_=g=>(g-o)*f}}}}update_v_to_grid_y_(){let e=this.bodyRect_;if(this.type_!="depth")for(let t=0;t<this.subVisualObjects.length;t++){let i=this.subVisualObjects[t];if(i.type_=="curve"){let r=i,n=r.lower_,a=r.upper_,l=this.valueScaleType_;if(n<a&&n>0||(l="linear"),l=="log"){let h=Math.sign(n)*Math.log10(Math.abs(n));h=h<0?Math.floor(h):Math.ceil(h);let o=Math.sign(a)*Math.log10(Math.abs(a));o=o<0?Math.floor(o):Math.ceil(o),r.v_to_grid_x_=u=>(Math.log10(u)-h)/(o-h)*e.w}else{let h=e.w/(a-n);r.v_to_grid_x_=o=>(o-n)*h}}}}get refreshRect(){let e=this.webCanvas.logPlotView,t=this.drawRect.clone();return t.intersect(e.drawRect),t}drawLogPrs_OffScreen(e,t,i,r){for(let n=0;n<this.subVisualObjects.length;n++){let a=this.subVisualObjects[n];a.visible&&a.redraw_Offscreen(e,t,i,r)}}iter_old(e,t,i,r,n=[]){let a=e%t,l=a,h=[];if(n.length>0)for(const o of n){let u=e%o,c=u;for(c=u>=0?o-u:Math.abs(u);c<i;c+=o)h.includes(c)||h.push(c)}for(l=a>=0?t-a:Math.abs(a);l<i;l+=t)h.find(o=>Math.abs(o-l)<.1)==null&&r(l)}iter2_(e,t,i,r,n){let a=e%t,l=t-a,h=(e+l)/r,o=t/r;for(;l<i;l+=t)n(l,h),h+=o}iter_(e,t,i,r,n=[]){let a=e%t,l=a>=0?t-a:Math.abs(a),h=new Set;for(const o of n){let u=e%o,c=u>0?o-u:0;for(;c<i;c+=o)h.add(c)}for(;l<i;l+=t)h.has(l)||r(l)}loadLogPrs(e){if(e)for(let t=0;t<e.length;t++){let i=e[t];switch(i.type){case"curve":this.subVisualObjects.push(new me(this.webCanvas,this,i));break;case"image":this.subVisualObjects.push(new _e(this.webCanvas,this,i));break;case"wave":this.subVisualObjects.push(new We(this.webCanvas,this,i));break;case"trace":this.subVisualObjects.push(new Ge(this.webCanvas,this,i));break;case"NMRSpectrum":this.subVisualObjects.push(new Ae(this.webCanvas,this,i));break;default:console.warn(`Unsupported type: ${i.type}`);break}}}}class _t{constructor(s){d(this,"Id");d(this,"LayerName");d(this,"ForeColor");d(this,"Time");d(this,"MD");d(this,"TVD");d(this,"VS");d(this,"TVDSS");Object.assign(this,s)}update(s){this.Id=(s==null?void 0:s.Id)??this.Id,this.LayerName=(s==null?void 0:s.LayerName)??this.LayerName,this.ForeColor=(s==null?void 0:s.ForeColor)??this.ForeColor,this.MD=(s==null?void 0:s.MD)??this.MD,this.TVD=(s==null?void 0:s.TVD)??this.TVD,this.VS=(s==null?void 0:s.VS)??this.VS,this.TVDSS=(s==null?void 0:s.TVDSS)??this.TVDSS,this.Time=(s==null?void 0:s.Time)??this.Time}}var Ye=(_=>(_[_["#5f0f40"]=0]="#5f0f40",_[_["#9a031e"]=1]="#9a031e",_[_["#e36414"]=2]="#e36414",_[_["#52796f"]=3]="#52796f",_[_["#0081a7"]=4]="#0081a7",_[_.endCount=5]="endCount",_))(Ye||{});const ye="Topset";class Xe extends J{constructor(e,t,i){super(e);d(this,"topsetId_");d(this,"data_");d(this,"onEdit_",!1);d(this,"intData");this.topsetId_=t.data.Id,this.parentVisualObject=i,this.objType=ye,this.data_=new _t(t.data),this.updateLayer(),this.lineTrait.foreColor=t.color??Ye[0],this.lineTrait.lineType=t.lineStyle?D[t.lineStyle]:this.lineTrait.lineType,this.lineTrait.width=t.lineThickness??1}updateLayer(e=null){this.layer=e??this.webCanvas.findObjectTypeInfo(ye).defaultLayerNo}updateDefaultLayer(){this.updateLayer(),super.updateDefaultLayer()}updateData(e){Object.assign(this.data_,e),this.lineTrait.foreColor=e.color??this.lineTrait.foreColor,this.lineTrait.lineType=e.lineStyle?D[e.lineStyle]:this.lineTrait.lineType,this.lineTrait.width=e.lineThickness??this.lineTrait.width}autoLayoutContents(e){let t=this.webCanvas,i=this.parentVisualObject,r=i.measure;this.drawRect.x=i.drawRect.x,this.drawRect.y=r.timeScale?i.clientBodysRect.y+this.data_.Time*r.dpMilSec_-t.virtualPixelOffsetTime-10:i.clientBodysRect.y+this.data_.MD*r.dprm_i_-t.virtualPixelOffsetY-10,this.drawRect.w=i.drawRect.w,this.drawRect.h=10,super.autoLayoutContents(e)}needRedraw(e,t){return e.RectToScreen(this.drawRect).intersectsWith(t)}HitTestOnFrame(e,t){return!1}HitTestForFastDrag(e,t){return null}HitTest(e,t){return this.webCanvas.topCanvasElement,e.isObjectSelected(this),null}MoveHandleTo(e,t,i){return!1}redraw(e,t,i,r){let n=this.webCanvas,a=n.canvasTheme,l=n.voLogPlot.measure,h=e.RectToScreen(n.voLogPlot.clientBodysRect),o=n.virtualPixelOffsetY,u=n.virtualPixelOffsetTime;if(r==this.layer){t.save(),t.translate(h.x,h.y),n.setRectClip(t,new C(.5,.5,h.w-1,h.h-1)),t.strokeStyle=this.lineTrait.foreColor,t.fillStyle=this.lineTrait.foreColor,t.font=a.normalFont.font;let c=l.timeScale?this.data_.Time*l.dpMilSec_-u:this.data_.MD*l.dprm_i_-o;t.lineWidth=this.lineTrait.width,this.lineTrait.lineType==D.dash?t.setLineDash(this.lineTrait.dashStyle):this.lineTrait.lineType==D.dot&&t.setLineDash(this.lineTrait.dotStyle),t.beginPath(),t.moveTo(1,c),t.lineTo(h.w-2,c),t.fillText(this.data_.LayerName,5,c-5),t.stroke(),t.restore()}}iter_(e,t,i,r){let n=e%t;for(let a=t-n;a<i;a+=t)r(a)}iter2_(e,t,i,r,n){let a=e%t,l=t-a,h=(e+l)/r,o=t/r;for(;l<=i;l+=t)n(l,h),h+=o}}class gt{constructor(s){d(this,"Id");d(this,"MD");d(this,"Time");d(this,"Description");Object.assign(this,s)}}var ze=(_=>(_[_["#5f0f40"]=0]="#5f0f40",_[_["#9a031e"]=1]="#9a031e",_[_["#e36414"]=2]="#e36414",_[_["#52796f"]=3]="#52796f",_[_["#0081a7"]=4]="#0081a7",_[_.endCount=5]="endCount",_))(ze||{});const pe="Bookmarker";class mt extends J{constructor(e,t,i){super(e);d(this,"bookmarkerId_");d(this,"data_");d(this,"onEdit_",!1);d(this,"intData");this.bookmarkerId_=t.data.Id,this.parentVisualObject=i,this.objType=pe,this.data_=new gt(t.data),this.updateLayer(),this.lineTrait.foreColor=t.color??ze[2],this.lineTrait.lineType=t.lineStyle?D[t.lineStyle]:this.lineTrait.lineType,this.lineTrait.width=t.lineThickness??2}updateLayer(e=null){this.layer=e??this.webCanvas.findObjectTypeInfo(pe).defaultLayerNo}updateDefaultLayer(){this.updateLayer(),super.updateDefaultLayer()}updateData(e){Object.assign(this.data_,e),this.lineTrait.foreColor=e.color??this.lineTrait.foreColor,this.lineTrait.lineType=e.lineStyle?D[e.lineStyle]:this.lineTrait.lineType,this.lineTrait.width=e.lineThickness??this.lineTrait.width}autoLayoutContents(e){let t=this.webCanvas,i=this.parentVisualObject,r=i.measure;this.drawRect.x=i.drawRect.x,this.drawRect.y=r.timeScale?i.clientBodysRect.y+this.data_.Time*r.dpMilSec_-t.virtualPixelOffsetTime-3:i.clientBodysRect.y+this.data_.MD*t.voLogPlot.measure.dprm_i_-t.virtualPixelOffsetY-3,this.drawRect.w=i.drawRect.w,this.drawRect.h=6,super.autoLayoutContents(e)}needRedraw(e,t){return e.RectToScreen(this.drawRect).intersectsWith(t)}HitTestOnFrame(e,t){return!1}HitTestForFastDrag(e,t){return null}HitTest(e,t){return this.webCanvas.topCanvasElement,e.isObjectSelected(this),null}MoveHandleTo(e,t,i){return!1}redraw(e,t,i,r){let n=this.webCanvas;if(n.hideBookmarker)return;let a=n.canvasTheme,l=n.voLogPlot.measure,h=e.RectToScreen(n.voLogPlot.clientBodysRect),o=n.virtualPixelOffsetY,u=n.virtualPixelOffsetTime;if(r==this.layer){t.save(),t.translate(h.x,h.y),n.setRectClip(t,new C(.5,.5,h.w-1,h.h-1)),t.strokeStyle=this.lineTrait.foreColor,t.fillStyle=this.lineTrait.foreColor,t.font=a.normalFont.font;let c=l.timeScale?this.data_.Time*l.dpMilSec_-u:this.data_.MD*l.dprm_i_-o;t.lineWidth=this.lineTrait.width,this.lineTrait.lineType==D.dash?t.setLineDash(this.lineTrait.dashStyle):this.lineTrait.lineType==D.dot&&t.setLineDash(this.lineTrait.dotStyle),t.beginPath(),t.moveTo(1,c),t.lineTo(h.w-2,c);let f=this.data_.MD.toFixed(2),g=f.length;for(;g<11;g++)f=f+"  ";t.fillText(f+this.data_.Description,5,c-5),t.stroke(),t.restore()}}OnContextMenu(e,t){let i=this.webCanvas;i.topCanvasElement;let r=i.lastMouseMoveClientPos;if(i.invalidate(),this.HitTestOnLine(e,t)){let n=i.bookmarkerContextMenu;n!=null&&(n.style.display="block",n.style.left=r.x+"px",n.style.top=r.y+"px"),i.mouseLeaveSectionFunc&&i.mouseLeaveSectionFunc()}return!0}HitTestOnLine(e,t){this.webCanvas;let i=e.PointToScreen(new y(this.drawRect.x,this.drawRect.y)),r=e.PointToScreen(new y(this.drawRect.x+this.drawRect.w,this.drawRect.y));return!!(e.drawRect.containPoint(t)&&t.x>i.x&&t.x<r.x&&p.hitTestLine(i.x,i.y,r.x,r.y,t,10))}iter_(e,t,i,r){let n=e%t;for(let a=t-n;a<i;a+=t)r(a)}iter2_(e,t,i,r,n){let a=e%t,l=t-a,h=(e+l)/r,o=t/r;for(;l<=i;l+=t)n(l,h),h+=o}}class he{constructor(s){d(this,"startIndex");d(this,"endIndex");d(this,"startTime");d(this,"endTime");d(this,"logData",new Array);d(this,"imgUrl");d(this,"dataLoaded",!1);d(this,"img");Object.assign(this,s)}}const $e="VoLogPlot";class wt extends J{constructor(e,t){super(e);d(this,"opts_plot_");d(this,"measure_");d(this,"markMouseDown",!1);d(this,"bodyMouseDown",!1);d(this,"lastMouseDownPos",new y(0,0));d(this,"lastMouseMovePos");d(this,"thumb_ryInit");d(this,"body_yInit");d(this,"clientBodysRect");d(this,"headerWidth_",0);d(this,"headerHeight_",0);d(this,"showHeader",!0);this.opts_plot_=t,this.measure_=new ut(this.opts_plot_.measure);let i=this.opts_plot_.tracks;if(this.subVisualObjects=new Array,i!=null)for(let r=0;r<i.length;r++){let n=i[r];this.subVisualObjects.push(new ve(this.webCanvas,n,this))}this.clientBodysRect=new C,this.updateLayer()}updateLayer(e=null){this.layer=e??this.webCanvas.findObjectTypeInfo($e).defaultLayerNo}updateDefaultLayer(){this.updateLayer(),super.updateDefaultLayer()}addTopsets(e){(e||[]).forEach((t,i)=>{let r={};r.data=t,this.subVisualObjects.push(new Xe(this.webCanvas,r,this))})}addBookmarkers(e){(e||[]).forEach((t,i)=>{let r={};r.data=t,this.subVisualObjects.push(new mt(this.webCanvas,r,this))})}get topsetItems(){return this.subVisualObjects.filter(e=>e.objType==ye).map(e=>e).sort(function(e,t){return e.data_.MD-t.data_.MD})}get bookmarkerItems(){return this.subVisualObjects.filter(e=>e.objType==pe).map(e=>e).sort(function(e,t){return e.data_.MD-t.data_.MD})}update_path_(e,t){let i=e.split("/");try{i.length==2&&(this.measure.ratio=t)}catch(r){console.warn(r)}}HitTestObject(e,t,i){let r=this.webCanvas;if(!r.isMouseMoveHitTest)for(let n=0;n<this.bookmarkerItems.length;n++){let a=this.bookmarkerItems[n];if(a.drawRect.containPoint(e.PointToView(i))&&a.layer==t)return a}for(let n=0;n<this.trackItems.length;n++){let a=this.trackItems[n];if((a.headTitleRect_.containPoint(e.PointToView(i))||a.HitTestOnFrame(e,i))&&a.layer==t)return a;if((a.bodyRect_.containPoint(e.PointToView(i))||a.HitTestOnFrame(e,i))&&a.layer==t)return a;if(r.acceptNewDroppedItem&&(a.headTitleRect_.containPoint(e.PointToView(i))||a.bodyRect_.containPoint(e.PointToView(i))))return a;for(let l=0;l<a.subVisualObjects.length;l++){let h=a.subVisualObjects[l];if(h.headRect.containPoint(e.PointToView(i))&&a.layer==t)return h}if(a.headRect_.containPoint(e.PointToView(i))&&a.layer==t)return a}return null}get headerWidth(){return this.headerWidth_}get headerHeight(){return this.headerHeight_}getMousePointToDoc(e){let t=this.webCanvas.dpr;return new y(e.offsetX*t,e.offsetY*t)}updateParams_(e,t,i){this.measure.ratio=parseFloat(i),this.measure.startIndex_=parseFloat(e),this.measure.endIndex_=parseFloat(t)}updateTrack_(e,t){this.trackItems.find(r=>r.trackId_==e).update(t)}autoLayoutContents(e){this.webCanvas;let t=this.trackItems;this.measure_;let i=0,r=0,n=0;this.drawRect.x=e.origin.x,this.drawRect.y=0,this.drawRect.h=e.clientHeight;for(let h=0;h<t.length;h++){let o=t[h];o.showTitle||(o.titleHeight_=0),o.drawRect.y=o.headRect_.y=o.headTitleRect_.y=this.drawRect.y,o.drawRect.x=o.headRect_.x=o.headTitleRect_.x=i+e.origin.x,o.autoLayoutContents(e),i+=o.headRect_.w,r=Math.max(r,o.headRect_.h);let u=o.headTitleRect_.h;for(let c=0;c<o.subVisualObjects.length;c++){let f=o.subVisualObjects[c];f.headRect.x=o.headRect_.x,f.headRect.y=o.drawRect.y+u,u+=f.headerHeight}}n=i,this.showHeader||(r=0);for(let h=0;h<t.length;h++){let o=t[h];o.type_=="depth"||o.type_=="timeScale"||o.subVisualObjects.length==0?o.headTitleRect_.h=r:o.subVisualObjects.length>0&&o.headRect_.h<r&&o.subVisualObjects[o.subVisualObjects.length-1],o.headRect_.h=r}this.headerWidth_=n,this.clientBodysRect.x=this.drawRect.x,this.clientBodysRect.y=r,this.clientBodysRect.w=n,this.drawRect.w=n;for(let h=0;h<t.length;h++){let o=t[h],u=t[h].headRect_,c=t[h].bodyRect_;c.x=o.drawRect.x,c.y=r,c.w=u.w,o.headTitleRect_.w=u.w,c.h=e.clientHeight-r,o.after_layout_()}this.clientBodysRect.h=e.clientHeight-r;let a=this.topsetItems;if(a!=null&&a.length>0)for(let h=0;h<a.length;h++)a[h].autoLayoutContents(e);let l=this.bookmarkerItems;if(l!=null&&l.length>0)for(let h=0;h<l.length;h++)l[h].autoLayoutContents(e)}redraw(e,t,i,r){let n=this.webCanvas,a=e.RectToScreen(this.clientBodysRect),l=n.getFillAreas();if(l.length>0){t.save(),t.translate(1,a.y);for(let h=0;h<l.length;h++){let o=l[h];o.visible&&o.redraw(e,t,i,r)}t.restore()}t.save(),n.setRectClip(t,e.drawRect),super.redraw(e,t,i,r),t.restore()}needRedraw(e,t){return e.drawRect.intersectsWith(t)}getPrsObj_(e){let t=this.trackItems;for(let i=0;i<t.length;i++){let r=t[i].subVisualObjects.map(n=>n);if(r!=null&&r.length>0){for(let n=0;n<r.length;n++)if(r[n].objId_==e)return r[n]}}}get measure(){return this.measure_}get topIndex(){return this.webCanvas.virtualPixelOffsetY/this.measure.dprm_i_}get bottomIndex(){return(this.webCanvas.virtualPixelOffsetY+this.trackItems[0].bodyRect_.h)/this.measure.dprm_i_}get topTime(){return this.webCanvas.virtualPixelOffsetTime/this.measure.dpMilSec_}get bottomTime(){return(this.webCanvas.virtualPixelOffsetTime+this.trackItems[0].bodyRect_.h)/this.measure.dpMilSec_}get trackItems(){return this.subVisualObjects.filter(e=>e.objType==Z).map(e=>e).sort(function(e,t){return e.index_-t.index_})}readLogPrsData(){for(const e of this.trackItems){const t=e.subVisualObjects.filter(i=>i instanceof E);if(t.length)for(const i of t){const r=i.dataContainer;!r||r.loadingData||r.getData(this.measure.timeScale?this.topTime:this.topIndex,this.measure.timeScale?this.bottomTime:this.bottomIndex)}}}setSectionsTime(){for(const e of this.trackItems)if(e.subVisualObjects.length!=0)for(const t of e.subVisualObjects){const i=t,r=i.dataContainer;if(!r||!r.sections||r.sections.length>0)continue;let n=[],a=Math.max(this.measure_.startTime_,r.startTime),l=Math.min(this.measure_.endTime_,r.endTime),h=i.segmentDataTime_,o=Math.ceil((l-a)/h);for(let u=0;u<o;u++){let c=a+u*h,f=Math.min(c+h,l);if(n.push(new he({startTime:c,endTime:f})),l-f<2){f=l,n.push(new he({startTime:c,endTime:f}));break}}r.sections=n}}setSectionsIndex(){for(const e of this.trackItems)if(e.subVisualObjects.length!=0)for(const t of e.subVisualObjects){const i=t,r=i.dataContainer;if(!r||!r.sections||r.sections.length>0)continue;let n=[],a=Math.max(this.measure_.startIndex_,r.startIndex),l=Math.min(this.measure_.endIndex_,r.endIndex),h=i.segmentDataDepth_,o=Math.ceil((l-a)/h);for(let u=0;u<o;u++){let c=a+u*h,f=Math.min(c+h,l);if(n.push(new he({startIndex:c,endIndex:f})),l-f<2){f=l,n.push(new he({startIndex:c,endIndex:f}));break}}r.sections=n}}}class Tt{constructor(s){d(this,"_webCanvas");d(this,"_drawRect",new C);d(this,"_origin",new y(0,0));d(this,"_selectedVisualObjects",new Array);d(this,"_visualObjects",new Array);this._webCanvas=s}get webCanvas(){return this._webCanvas}set webCanvas(s){this._webCanvas=s}get drawRect(){return this._drawRect}set drawRect(s){this._drawRect=s}get selectedVisualObjects(){return this._selectedVisualObjects}set selectedVisualObjects(s){this._selectedVisualObjects=s}get visualObjects(){return this._visualObjects}set visualObjects(s){this._visualObjects=s}get origin(){return this._origin}get clientWidth(){return this._drawRect.w}get clientHeight(){return this._drawRect.h}get selectedObject(){return this._selectedVisualObjects.length>0?this._selectedVisualObjects[this._selectedVisualObjects.length-1]:null}autoLayoutContents(){for(let s=0;s<this._visualObjects.length;s++)this._visualObjects[s].autoLayoutContents(this)}redraw(s,e){var i;if(!e||!this._visualObjects||!this._webCanvas||!oe)return;const t=(i=this._webCanvas.findObjectTypeInfo(oe))==null?void 0:i.defaultLayerNo;if(t!==void 0)for(let r=0;r<=t;r++)for(const n of this._visualObjects)try{if(!n.visible)continue;let a=e.filter(l=>n.needRedraw(this,l));if(a.length===0)continue;for(let l=0;l<a.length;l++){let h=a[l].clone(),o=n.refreshRect.clone();o.intersect(h),r==0&&this.redrawBackGround(s,o),n.redraw(this,s,o,r)}}catch(a){console.warn(`Error in redraw for object ${n}:`,a)}}scrollTo(s=null,e=null){this.origin.x=s,this.origin.y=e,this.autoLayoutContents(),this.invalidateView()}selectObject(s,e){if(s==null){if(this._selectedVisualObjects.length<=0)return;for(const t of this._selectedVisualObjects)t.OnDeselect(this);this._selectedVisualObjects=[],this.invalidateView()}if(e){if(this._selectedVisualObjects.length==0){this._selectedVisualObjects.push(s),this.InvalidateObject(s);return}if(this.IsObjectPrimarilySelected(s))return;{this.InvalidateObject(this.GetPrimarilySelectedObject());let t=this._selectedVisualObjects.indexOf(s);t>=0&&this._selectedVisualObjects.splice(t,1),this._selectedVisualObjects.push(s),this.InvalidateObject(s)}}else if(this.isObjectSelected(s))if(this.IsObjectPrimarilySelected(s))this._selectedVisualObjects.splice(this._selectedVisualObjects.length-1,1);else{let t=this._selectedVisualObjects.indexOf(s);this._selectedVisualObjects.splice(t,1)}else return}invalidateView(){this.redraw(this._webCanvas.ctx,[this._drawRect])}PointToScreen(s){let e=this._drawRect;return new y(e.x+s.x,e.y+s.y)}PointToView(s){let e=this._drawRect;return new y(s.x-e.x,s.y-e.y)}RectToScreen(s){return new C(this._drawRect.x+s.x,this._drawRect.y+s.y,s.w,s.h)}RectToView(s){return new C(s.x-this._drawRect.x,s.y-this._drawRect.y,s.w,s.h)}OnMouseMove(s){return!1}OnMouseLButtonUp(s){return!1}OnMouseMidButtonUp(s){return!1}OnMouseRButtonUp(s){return!1}OnContextMenu(s){return!1}HitTestObject(s){if(this._drawRect.containPoint(s)&&this._visualObjects.length>0){for(let e=this._webCanvas.findObjectTypeInfo(oe).defaultLayerNo;e>=0;e--)for(let t=0;t<this._visualObjects.length;t++)if(this._visualObjects[t].visible&&!this._visualObjects[t].hitTestProtected){let i=this._visualObjects[t].HitTestObject(this,e,s);if(i)return i}}return null}HitTest(s){return!!this._drawRect.containPoint(s)}InvalidateObject(s){let e=this.RectToScreen(s.refreshRect);this.Invalidate([e])}Invalidate(s){this.redraw(this._webCanvas.ctx,s)}SelectAllObjects(){this._selectedVisualObjects=[];for(let s=0;s<this._visualObjects.length;s++)this._selectedVisualObjects.push(this._visualObjects[s])}GetPrimarilySelectedObject(){return this._selectedVisualObjects.length==0?null:this._selectedVisualObjects[this._selectedVisualObjects.length-1]}IsObjectPrimarilySelected(s){return this._selectedVisualObjects.length>0&&this._selectedVisualObjects.indexOf(s)==this._selectedVisualObjects.length-1}isObjectSelected(s){return this._selectedVisualObjects.indexOf(s)>=0}redrawBackGround(s,e){s.clearRect(e.x+1,e.y,e.w,e.h),s.fillStyle=this._webCanvas.canvasTheme.backgroundColor,s.fillRect(e.x+1,e.y,e.w,e.h)}}class qe extends Tt{constructor(e){super(e);d(this,"voLogPlot");this.voLogPlot=new wt(e,e.opts.plot),this.visualObjects.push(this.voLogPlot)}autoLayoutContents(){let e=this.webCanvas;this.drawRect.x=0,this.drawRect.y=0,this.drawRect.w=e.clientWidth,this.drawRect.h=e.clientHeight,super.autoLayoutContents()}}const Ke=24;class Ce{constructor(s){d(this,"_webCanvas");d(this,"_cacheCanvas");d(this,"_textTrait",new k);d(this,"_lineTrait",new N);d(this,"_drawRect",new C);d(this,"_isVisible",!0);d(this,"_hitTestProtected",!1);d(this,"_onMouseDown",!1);d(this,"_onMouseHover",!1);d(this,"_parentVisualComponent");d(this,"_subVisualComponents");this._webCanvas=s}get webCanvas(){return this._webCanvas}set webCanvas(s){this._webCanvas=s}get cacheCanvas(){return this._cacheCanvas}set cacheCanvas(s){this._cacheCanvas=s}get textTrait(){return this._textTrait}set textTrait(s){this._textTrait=s}get lineTrait(){return this._lineTrait}set lineTrait(s){this._lineTrait=s}get drawRect(){return this._drawRect}set drawRect(s){this._drawRect=s}get onMouseDown(){return this._onMouseDown}set onMouseDown(s){this._onMouseDown=s}get isVisible(){return this._isVisible}set isVisible(s){this._isVisible=s}get hitTestProtected(){return this._hitTestProtected}set hitTestProtected(s){this._hitTestProtected=s}get onMouseHover(){return this._onMouseHover}set onMouseHover(s){this._onMouseHover=s}get parentVisualComponent(){return this._parentVisualComponent}set parentVisualComponent(s){this._parentVisualComponent=s}get subVisualComponents(){return this._subVisualComponents}set subVisualComponents(s){this._subVisualComponents=s}autoLayoutContents(){if(this._subVisualComponents)for(let s=0;s<this._subVisualComponents.length;s++)this._subVisualComponents[s].autoLayoutContents()}redraw(s){if(this._subVisualComponents)for(let e=0;e<this._subVisualComponents.length;e++){let t=this._subVisualComponents[e];t._isVisible&&t.needRedraw(s)&&t.redraw(s)}}needRedraw(s){return this._drawRect.intersectsWith(s)}HitTestObject(s){if(this._drawRect.containPoint(s))return this;if(this._subVisualComponents)for(let e=0;e<this._subVisualComponents.length;e++){let t=this._subVisualComponents[e].HitTestObject(s);if(t)return t}return null}HitTestOnFrame(s){return this._drawRect.pointOnFrame(s)}ContainPoint(s,e,t=1){let i=s.x,r=s.y,n=0,a=0,l=0;for(let h=0;h<e.length-1;h++){const o=e[h],u=e[h+1];if(o.x===u.x){if(i>o.x)continue;u.y>o.y&&r>=o.y&&r<=u.y&&(a++,n++),u.y<o.y&&r>=u.y&&r<=o.y&&(l++,n++);continue}const c=(u.y-o.y)/(u.x-o.x),f=(r-o.y)/c+o.x;i>f||(u.x>o.x&&f>=o.x&&f<=u.x&&(n++,c>=0?a++:l++),u.x<o.x&&f>=u.x&&f<=o.x&&(n++,c>=0?l++:a++))}return t===1?a-l!==0:n%2===1}get ctx_(){return this._webCanvas.ctx}OnClick(s){return!1}OnDoubleClick(s){return!1}OnMouseDown(s){return!1}OnMouseLButtonUp(s){return!1}OnMouseMidButtonUp(s){return!1}OnMouseHover(s){return!1}OnMouseEnter(s){return!0}OnMouseLeave(s){return!1}OnMouseOut(s){return!1}MoveHandleTo(s,e){}MoveTo(s){}OnDragEnter(s){}OnDragDrop(s){}OnDragLeave(s){}OnDragOver(s){}OnEditProperties(){return!0}HitTestForFastDrag(s){return null}HitTest(s){if(this._drawRect.containPoint(s))return this;if(this._subVisualComponents)for(let e=0;e<this._subVisualComponents.length;e++){let t=this._subVisualComponents[e].HitTest(s);if(t)return t}return null}dispose(){}}const F=24;class de extends Ce{constructor(e){super(e);d(this,"img_");d(this,"pressedImg_");d(this,"mouseDownTimer_");this.drawRect.w=F,this.drawRect.h=F,this.cacheCanvas=document.createElement("canvas"),this.cacheCanvas.width=F,this.cacheCanvas.height=F}timeout(){}autoLayoutContents(){super.autoLayoutContents()}OnMouseLButtonUp(e){let t=this.webCanvas,i=this.drawRect;return t.selectedVisualComponents.length>0&&t.IsComponentSelected(this)&&t.SelectComponent(null,!1),this.onMouseDown&&(this.onMouseDown=!1,clearTimeout(this.mouseDownTimer_),t.invalidate(i)),!1}OnMouseOut(e){let t=this.webCanvas,i=this.drawRect;return t.selectedVisualComponents.length>0&&t.IsComponentSelected(this)&&t.SelectComponent(null,!1),this.onMouseDown&&(this.onMouseDown=!1,clearTimeout(this.mouseDownTimer_),t.invalidate(i)),!1}OnMouseEnter(e){return!1}HitTestObject(e){return this.drawRect.containPoint(e)?this:null}HitTestForFastDrag(e){return this}MoveHandleTo(e,t){}redraw(e){let t=this.ctx_,i=this.drawRect;this.webCanvas;let r=this.img_,n=this.pressedImg_,a=this.cacheCanvas.getContext("2d");i.intersectsWith(e)&&(t.save(),t.translate(i.x,i.y),this.onMouseDown?n.complete&&(a.clearRect(0,0,F,F),a.drawImage(n,0,0,F,F),t.drawImage(this.cacheCanvas,0,0)):r.complete&&(a.clearRect(0,0,F,F),a.drawImage(r,0,0,F,F),t.drawImage(this.cacheCanvas,0,0)),t.restore())}}class bt extends de{constructor(s){super(s);const e=new Image,t=new Image;e.setAttribute("crossOrigin","Anonymous"),t.setAttribute("crossOrigin","Anonymous"),this.img_=e,this.pressedImg_=t,this.img_.onload=i=>{this.redraw(this.drawRect)}}autoLayoutContents(){super.autoLayoutContents()}OnMouseLButtonUp(s){return super.OnMouseLButtonUp(s)}OnMouseOut(s){return super.OnMouseOut(s)}OnMouseEnter(s){return super.OnMouseEnter(s)}HitTestForFastDrag(s){return this.drawRect.containPoint(s)&&(this.onMouseDown=!0,this.mouseDownTimer_=setTimeout(()=>{},100)),this}MoveHandleTo(s,e){super.MoveHandleTo(s,e)}redraw(s){super.redraw(s)}}class vt extends de{constructor(s){super(s);const e=new Image,t=new Image;e.setAttribute("crossOrigin","Anonymous"),t.setAttribute("crossOrigin","Anonymous"),this.img_=e,this.pressedImg_=t,this.img_.onload=i=>{this.redraw(this.drawRect)}}autoLayoutContents(){super.autoLayoutContents()}OnMouseLButtonUp(s){return super.OnMouseLButtonUp(s)}OnMouseOut(s){return super.OnMouseOut(s)}OnMouseEnter(s){return super.OnMouseEnter(s)}HitTestForFastDrag(s){return this.drawRect.containPoint(s)&&(this.onMouseDown=!0,this.mouseDownTimer_=setTimeout(()=>{},100)),this}MoveHandleTo(s,e){super.MoveHandleTo(s,e)}redraw(s){super.redraw(s)}}class yt extends Ce{constructor(e){super(e);d(this,"upButton_");d(this,"downButton_");d(this,"thumb_IntV");d(this,"thumb_ryInit");d(this,"thumbRect_",new C);d(this,"snapY_",0);d(this,"snapHeight_",0);d(this,"thumbHeight_",0);d(this,"thumbH_SnapH_Ratio",1);d(this,"pageDist_",0);this.drawRect.w=Ke,this.subVisualComponents=Array(),this.onStart_()}onStart_(){this.upButton_=new bt(this.webCanvas),this.downButton_=new vt(this.webCanvas),this.subVisualComponents.push(this.upButton_,this.downButton_)}autoLayoutContents(){super.autoLayoutContents()}OnMouseLButtonUp(e){let t=this.webCanvas,i=this.drawRect;return t.selectedVisualComponents.length>0&&t.isComponentSelected(this)&&t.SelectComponent(null,!1),this.onMouseDown&&(this.onMouseDown=!1,t.invalidate(i)),!1}OnMouseEnter(e){return!1}OnMouseOut(e){return this.webCanvas,this.drawRect,!0}HitTestObject(e){return super.HitTestObject(e)}HitTestForFastDrag(e){return this}MoveHandleTo(e,t){}redraw(e){super.redraw(e)}update_(){let e=this.drawRect,t=this.webCanvas.clientHeight,i=e.h;this.pageDist_=i,this.snapHeight_=i/(t+i)*e.h,this.thumbHeight_=this.snapHeight_+16}}class pt extends yt{constructor(e){super(e);d(this,"minThumbHeight_",20)}onStart_(){}autoLayoutContents(){let e=this.webCanvas;this.drawRect.w=e.scrollBarWidth,this.drawRect.x=e.clientWidth,this.drawRect.y=e.voLogPlot.clientBodysRect.y,this.drawRect.h=e.voLogPlot.clientBodysRect.h,this.update_(),super.autoLayoutContents()}update_(){let e=this.webCanvas.voLogPlot.measure,t=e.timeScale?e.endTime_-e.startTime_:e.endIndex_-e.startIndex_,i=this.drawRect.h/(e.timeScale?e.dpMilSec_:e.dprm_i_);this.pageDist_=i,this.snapHeight_=i/t*this.drawRect.h,this.thumbHeight_=this.snapHeight_,this.thumbHeight_=Math.max(this.minThumbHeight_,this.thumbHeight_),this.thumbH_SnapH_Ratio=this.thumbHeight_/this.snapHeight_}thumb_real_to_ratio(e){let t=this.webCanvas.voLogPlot.measure,i=this.pageDist_;return(e-t.startIndex_)/(i+t.endIndex_-t.startIndex_)}thumbRealTimetoRatio(e){let t=this.webCanvas.voLogPlot.measure,i=this.pageDist_;return(e-t.startTime_)/(i+t.endTime_-t.startTime_)}thumb_ratio_to_real(e){let t=this.webCanvas.voLogPlot.measure,i=this.pageDist_;return e*(i+t.endIndex_-t.startIndex_)+t.startIndex_}thumbRatioToRealTime(e){let t=this.webCanvas.voLogPlot.measure,i=this.pageDist_;return e*(i+t.endTime_-t.startTime_)+t.startTime_}thumb_px_to_ratio(e){return e/(this.drawRect.h+this.thumbHeight_)}HitTestForFastDrag(e){let t=this.webCanvas,i=t.topCanvasElement;return this.thumbRect_.translate(this.drawRect.x,this.drawRect.y).containPoint(e)&&(i.style.cursor="pointer",this.onMouseDown=!0,this.thumb_IntV=t.voLogPlot.measure.timeScale?t.virtualPixelOffsetTime:t.virtualPixelOffsetY,t.lineIndicator.hide(),t.invalidate(this.drawRect)),this}MoveHandleTo(e,t){let i=this.webCanvas,r=i.lastMouseDownPos,n=i.topCanvasElement,a=i.voLogPlot.measure;if(this.thumbRect_.translate(this.drawRect.x,this.drawRect.y).containPoint(t)?(n.style.cursor="pointer",this.onMouseHover=!0,i.invalidate(this.drawRect)):this.onMouseHover&&(this.onMouseHover=!1,i.invalidate(this.drawRect)),i.isComponentSelected(this)&&this.onMouseDown){let h=a.timeScale?a.endTime_-a.startTime_:a.endIndex_-a.startIndex_,o=(t.y-r.y)/(this.drawRect.h-this.thumbHeight_);a.timeScale?i.virtualPixelOffsetTime=this.thumb_IntV+o*(h-this.pageDist_)*a.dpMilSec_:i.virtualPixelOffsetY=this.thumb_IntV+o*(h-this.pageDist_)*a.dprm_i_}}redraw(e){let t=this.ctx_,i=this.drawRect,r=this.webCanvas,n=r.canvasTheme,a=r.voLogPlot.measure,l=2;if(i.intersectsWith(e)){t.save(),t.translate(i.x+1,i.y);let h=i.w-1;t.rect(0,0,h,i.h),t.clip(),t.fillStyle=n.scrollbarBGColor,t.fillRect(0,0,h,i.h);let o=a.timeScale?a.endTime_-a.startTime_:a.endIndex_-a.startIndex_,u=this.snapY_=(a.timeScale?r.realTime-a.startTime_:r.realY-a.startIndex_)/(o-this.pageDist_)*(i.h-this.thumbHeight_);t.strokeStyle=this.onMouseHover?n.scrollbarOverColor:n.scrollbarColor;let c=9;t.lineJoin="round",t.lineWidth=c,this.thumbRect_.x=l,this.thumbRect_.y=u,this.thumbRect_.w=h-l-l,this.thumbRect_.h=this.thumbHeight_,t.fillStyle=this.onMouseHover?n.scrollbarOverColor:n.scrollbarColor,t.strokeRect(this.thumbRect_.x+c*.5,this.thumbRect_.y+c*.5,this.thumbRect_.w-c,this.thumbRect_.h-c),this.onMouseDown&&(t.fillStyle=n.scrollbarDownColor,t.strokeStyle=n.scrollbarDownColor,t.strokeRect(this.thumbRect_.x+c*.5,this.thumbRect_.y+c*.5,this.thumbRect_.w-c,this.thumbRect_.h-c),t.fillRect(this.thumbRect_.x+c*.5,this.thumbRect_.y+c*.5,this.thumbRect_.w-c,this.thumbRect_.h-c)),t.lineWidth=1,t.strokeStyle=n.gridColor,t.strokeRect(0,0,h,i.h),t.restore()}super.redraw(e)}}class Ct extends de{constructor(s){super(s);const e=new Image,t=new Image;e.setAttribute("crossOrigin","Anonymous"),t.setAttribute("crossOrigin","Anonymous"),this.img_=e,this.pressedImg_=t,this.img_.onload=i=>{this.redraw(this.drawRect)}}autoLayoutContents(){super.autoLayoutContents()}OnMouseLButtonUp(s){return super.OnMouseLButtonUp(s)}OnMouseOut(s){return super.OnMouseOut(s)}OnMouseEnter(s){return super.OnMouseEnter(s)}HitTestForFastDrag(s){return this}MoveHandleTo(s,e){super.MoveHandleTo(s,e)}redraw(s){super.redraw(s)}}class Mt extends de{constructor(s){super(s);const e=new Image,t=new Image;e.setAttribute("crossOrigin","Anonymous"),t.setAttribute("crossOrigin","Anonymous"),this.img_=e,this.pressedImg_=t,this.img_.onload=i=>{this.redraw(this.drawRect)}}autoLayoutContents(){super.autoLayoutContents()}OnMouseLButtonUp(s){return super.OnMouseLButtonUp(s)}OnMouseOut(s){return super.OnMouseOut(s)}OnMouseEnter(s){return super.OnMouseEnter(s)}HitTestForFastDrag(s){return this}MoveHandleTo(s,e){super.MoveHandleTo(s,e)}redraw(s){super.redraw(s)}}class Pt extends Ce{constructor(e){super(e);d(this,"leftButton_");d(this,"rightButton_");d(this,"thumb_rxInit");d(this,"thumbRect_",new C);d(this,"snapX_",0);d(this,"snapWidth_",0);d(this,"thumbWidth_",0);d(this,"pageDist_",0);this.drawRect.h=Ke,this.subVisualComponents=Array(),this.onStart_()}onStart_(){this.leftButton_=new Ct(this.webCanvas),this.rightButton_=new Mt(this.webCanvas),this.subVisualComponents.push(this.leftButton_,this.rightButton_)}OnMouseLButtonUp(e){let t=this.webCanvas,i=this.drawRect;return t.selectedVisualComponents.length>0&&t.isComponentSelected(this)&&t.SelectComponent(null,!1),this.onMouseDown&&(this.onMouseDown=!1,t.invalidate(i)),!1}autoLayoutContents(){super.autoLayoutContents()}OnMouseOut(e){return this.webCanvas,this.drawRect,!0}HitTestObject(e){return super.HitTestObject(e)}HitTestForFastDrag(e){return this}MoveHandleTo(e,t){}redraw(e){let t=this.drawRect;this.webCanvas.canvasTheme,this.update_(),t.intersectsWith(e),super.redraw(e)}update_(){let e=this.drawRect,t=this.webCanvas.clientWidth,i=e.w;this.pageDist_=i,this.snapWidth_=i/(t+i)*e.w,this.thumbWidth_=this.snapWidth_+16}}class Ot extends Pt{constructor(e){super(e);d(this,"snapX_",0)}onStart_(){}OnMouseLButtonUp(e){let t=this.webCanvas,i=this.drawRect;return t.selectedVisualComponents.length>0&&t.isComponentSelected(this)&&t.SelectComponent(null,!1),this.onMouseDown&&(this.onMouseDown=!1,t.invalidate(i)),!1}autoLayoutContents(){let e=this.webCanvas;this.drawRect.h=12,this.drawRect.x=0,this.drawRect.y=e.clientHeight,this.drawRect.w=e.clientWidth,super.autoLayoutContents(),e.offsetX=e.offsetX}thumb_ratio_to_px(e){let t=this.pageDist_;return e*(t+this.webCanvas.voLogPlot.headerWidth)}thumb_px_to_ratio(e){return e/(this.drawRect.w+this.snapWidth_)}HitTestForFastDrag(e){let t=this.webCanvas,i=t.topCanvasElement;return this.thumbRect_.translate(this.drawRect.x,this.drawRect.y).containPoint(e)&&(i.style.cursor="pointer",this.onMouseDown=!0,this.thumb_rxInit=this.thumb_px_to_ratio(this.snapX_),t.lineIndicator.hide(),t.invalidate(this.drawRect)),this}MoveHandleTo(e,t){let i=this.webCanvas,r=i.lastMouseDownPos,n=i.topCanvasElement;if(this.thumbRect_.translate(this.drawRect.x,this.drawRect.y).containPoint(t)?(n.style.cursor="pointer",this.onMouseHover=!0,i.invalidate(this.drawRect)):this.onMouseHover&&(this.onMouseHover=!1,i.invalidate(this.drawRect)),i.isComponentSelected(this)&&this.onMouseDown){let l=this.thumb_rxInit+this.thumb_px_to_ratio(t.x-r.x);i.offsetX=this.thumb_ratio_to_px(l)}}redraw(e){let t=this.ctx_,i=this.drawRect,r=this.webCanvas,n=r.canvasTheme;r.voLogPlot.measure;let a=2;if(this.update_(),i.intersectsWith(e)){t.save(),t.translate(i.x,i.y),t.rect(0,0,i.w,i.h),t.clip(),t.fillStyle=n.scrollbarBGColor,t.fillRect(0,0,i.w,i.h);let l=r.voLogPlot.headerWidth,h=this.snapX_;h=this.snapX_=r.offsetX/l*i.w;let o=h+this.snapWidth_/2-this.thumbWidth_/2;{t.strokeStyle=this.onMouseHover?n.scrollbarOverColor:n.scrollbarColor;let u=9;t.lineJoin="round",t.lineWidth=u;let c=Math.min(0,o);this.thumbRect_.x=o-c,this.thumbRect_.y=a,this.thumbRect_.w=this.thumbWidth_+c,this.thumbRect_.h=i.h-a*2,t.fillStyle=this.onMouseHover?n.scrollbarOverColor:n.scrollbarColor,t.strokeRect(h+u*.5,this.thumbRect_.y+u*.5,this.snapWidth_-u,this.thumbRect_.h-u),this.onMouseDown&&(t.fillStyle=n.scrollbarDownColor,t.strokeRect(h+u*.5,this.thumbRect_.y+u*.5,this.snapWidth_-u,this.thumbRect_.h-u))}t.lineWidth=1,t.strokeStyle=n.gridColor,t.strokeRect(0,0,i.w,i.h),t.restore()}super.redraw(e)}update_(){let e=this.webCanvas,t=this.drawRect;e.voLogPlot.measure;let i=e.voLogPlot.headerWidth,r=e.clientWidth;this.pageDist_=r,this.snapWidth_=Math.min(t.w,r/i*t.w),this.thumbWidth_=this.snapWidth_}}class St{constructor(){d(this,"parent");d(this,"lastMouseInObject")}get isDirty(){return this.parent.isDirty}set isDirty(s){this.parent.isDirty=s}OnKeyDown(s,e){return this.parent.currentTool==null?!1:e.key=="Escape"?(this.parent.currentTool!=this.parent.defaultTool&&(this.parent.currentTool.onDeselected(s),this.parent.currentTool=this.parent.defaultTool,this.parent.currentTool.onSelected(s)),!0):!1}OnKeyPress(s,e){return!1}OnKeyUp(s,e){return!1}OnGotFocus(s,e){return!1}OnLostFocus(s,e){return!1}onSelected(s){}onDeselecting(s){return!1}onDeselected(s){}OnScrolling(s){}OnScrolled(s){}onCancel(s){}OnContextMenu(s,e){return!1}OnEnter(s,e){}OnLeave(s,e){}OnEditProperties(){}endEdit(){}saveEditingData(){}}const Je="LogPlot";class xt extends St{constructor(){super()}get name(){return Je}get isDirty(){return this.parent.isDirty}set isDirty(s){this.parent.isDirty=s}OnMouseMove(s,e){if(e.button!=0||e.target!=s.topCanvasElement)return!1;s.isMouseMoveHitTest=!0;let t=s.topCanvasElement;if(t.style.cursor="default",(s.addingLayerLine||s.addingControlLine||s.addingFaultLine)&&(t.style.cursor="crosshair"),s.lastMouseMovePos=s.getMousePointToDoc(e),s.selectedVisualComponents.length>0)for(let i=0;i<s.selectedVisualComponents.length;i++)s.selectedVisualComponents[i].MoveHandleTo(0,s.lastMouseMovePos);else if(s.HitTestComponent(s.lastMouseMovePos)!=null)s.HitTestComponent(s.lastMouseMovePos).MoveHandleTo(0,s.lastMouseMovePos);else for(let i=0;i<s.visualComponents.length;i++){let r=s.visualComponents[i];r.onMouseHover&&(r.onMouseHover=!1,s.invalidate(r.drawRect))}for(let i=0;i<s.webCanvasViews.length;i++){let r=s.webCanvasViews[i];if(r.selectedVisualObjects.length>0)for(let n=0;n<r.selectedVisualObjects.length;n++)r.selectedVisualObjects[n].MoveHandleTo(r,0,s.lastMouseMovePos);if(r.HitTestObject(s.lastMouseMovePos)!=null){let n=r.HitTestObject(s.lastMouseMovePos);(r.selectedVisualObjects.length==0||r.selectedVisualObjects.length>0&&!r.GetPrimarilySelectedObject().isDraggingBound)&&n.MoveHandleTo(r,0,s.lastMouseMovePos)}else this.lastMouseInObject!=null&&(this.lastMouseInObject.OnMouseLeave(r),this.lastMouseInObject=null),r.selectedVisualObjects.length==0&&r.OnMouseMove(s.lastMouseMovePos),s.acceptNewDroppedItem&&(s.droppedCheckFunc(null,s.voLogPlot,null),s.acceptNewDroppedItem=!1)}return s.isMouseMoveHitTest=!1,!0}OnMouseDown(s,e){return s.clearContextMenu(),e.button==0?this.OnMouseLButtonDown(s,e):(e.button==2?(s.addingLayerLine||s.addingControlLine||s.addingFaultLine)&&(s.addingLayerLine=s.addingControlLine=s.addingFaultLine=!1):(s.lastMouseDownPos=s.getMousePointToDoc(e),s.lastMouseMovePos=s.getMousePointToDoc(e)),!1)}OnMouseLButtonDown(s,e){if(s.lastMouseDownPos=s.getMousePointToDoc(e),s.lastMouseMovePos=s.getMousePointToDoc(e),s.visualComponents&&s.visualComponents.length>0)for(let t=0;t<s.visualComponents.length;t++){let i=s.visualComponents[t];if(i.onMouseDown=!1,i.subVisualComponents&&i.subVisualComponents.length>0)for(let r=0;r<i.subVisualComponents.length;r++)i.subVisualComponents[r].onMouseDown=!1}if(s.HitTestComponent(s.lastMouseDownPos)!=null){let t=s.HitTestComponent(s.lastMouseDownPos),i=t.HitTestForFastDrag(s.lastMouseDownPos);if(i&&(t=i,s.selectedVisualComponents.length==0||s.SelectComponent(null,!1),s.SelectComponent(t,!0)),s.selectedVisualComponents.length==1){let r=s.SelectedComponent.HitTest(s.lastMouseDownPos);r?t=r:s.SelectComponent(null,!1)}return!0}else s.HitTestComponent(s.lastMouseDownPos)==null&&s.SelectComponent(null,!1);for(let t=0;t<s.webCanvasViews.length;t++){let i=s.webCanvasViews[t];if(i.HitTestObject(s.lastMouseDownPos)!=null){let r=i.HitTestObject(s.lastMouseDownPos),n=r.HitTestForFastDrag(i,s.lastMouseDownPos);if(n&&(r=n,i.selectedVisualObjects.length==0||i.selectObject(null,!1),i.selectObject(r,!0)),i.selectedVisualObjects.length==1){let a=i.selectedObject.HitTest(i,s.lastMouseDownPos);a&&(r=a)}}else i.HitTestObject(s.lastMouseDownPos)==null&&(i.selectObject(null,!1),i.HitTest(s.lastMouseDownPos))}return!0}OnMouseUp(s,e){if(e.button==0){if(s.lastMouseMovePos=s.getMousePointToDoc(e),s.selectedVisualComponents.length>0)for(let t=0;t<s.selectedVisualComponents.length;t++)return s.selectedVisualComponents[t].OnMouseLButtonUp(s.lastMouseMovePos),!0;for(let t=0;t<s.webCanvasViews.length;t++){let i=s.webCanvasViews[t];if(i.selectedVisualObjects.length>0)for(let r=0;r<i.selectedVisualObjects.length;r++)i.selectedVisualObjects[r].OnMouseLButtonUp(i,s.lastMouseMovePos)}return!0}return!1}OnMouseOut(s,e){if(e.target==document.body&&e.relatedTarget!=s.floatCanvasElement&&e.relatedTarget!=s.topCanvasElement&&e.relatedTarget!=s.canvas){if(s.selectedVisualComponents.length>0)for(let t=0;t<s.selectedVisualComponents.length;t++)s.selectedVisualComponents[t].OnMouseOut(s.lastMouseMovePos);for(let t=0;t<s.webCanvasViews.length;t++){let i=s.webCanvasViews[t];if(i.selectedVisualObjects.length>0)for(let r=0;r<i.selectedVisualObjects.length;r++)i.selectedVisualObjects[r].OnMouseOut(i,s.lastMouseMovePos)}}return!0}OnMouseHover(s,e){return!1}OnMouseEnter(s,e){for(let t=0;t<s.webCanvasViews.length;t++){let i=s.webCanvasViews[t];if(i.selectedVisualObjects.length>0)for(let r=0;r<i.selectedVisualObjects.length;r++)i.selectedVisualObjects[r]}return!0}OnMouseLeave(s,e){return!1}OnMouseWheel(s,e){s.lastMouseMovePos=s.getMousePointToDoc(e),s.logPlotView.drawRect.containPoint(s.lastMouseMovePos)&&(s.virtualPixelOffsetY=s.virtualPixelOffsetY+Math.sign(e.deltaY)*50,s.voLogPlot.measure.timeScale&&(s.virtualPixelOffsetTime=s.virtualPixelOffsetTime+Math.sign(e.deltaY)*s.voLogPlot.measure.timeRatio*500*s.voLogPlot.measure.dpMilSec_));for(let t=0;t<s.webCanvasViews.length;t++){let i=s.webCanvasViews[t];if(i.HitTestObject(s.lastMouseMovePos)!=null){let r=i.HitTestObject(s.lastMouseMovePos);(i.selectedVisualObjects.length==0||i.selectedVisualObjects.length>0&&!i.GetPrimarilySelectedObject().keepMouseInteract())&&r.OnMouseWheel(i,e)}}return!0}OnMouseClick(s,e){let t=s.dpr;return s.clearContextMenu(),new y(e.offsetX*t,e.offsetY*t),!1}OnMouseDoubleClick(s,e){if(e.button==0){s.clearContextMenu(),s.dpr,s.virtualPixelOffsetY,s.lastMouseDownPos=s.getMousePointToDoc(e),s.lastMouseMovePos=s.getMousePointToDoc(e);for(let t=0;t<s.webCanvasViews.length;t++){let i=s.webCanvasViews[t];if(i.HitTestObject(s.lastMouseDownPos)!=null){let r=i.HitTestObject(s.lastMouseDownPos),n=r.HitTestForFastDrag(i,s.lastMouseDownPos);if(n&&(r=n,i.selectedVisualObjects.length==0||i.selectObject(null,!1),i.selectObject(r,!0),r.OnEditProperties(i,s.lastMouseDownPos)),i.selectedVisualObjects.length==1){let a=i.selectedObject.HitTest(i,s.lastMouseDownPos);a?r=a:i.selectObject(null,!1)}s.lineIndicator.hide()}else i.selectObject(null,!1)}return!0}return!1}OnKeyDown(s,e){if(this.parent.currentTool==null)return!1;let t=s.dpr;e.key=="PageUp"?s.virtualPixelOffsetY=s.virtualPixelOffsetY-100*t:e.key=="PageDown"?s.virtualPixelOffsetY=s.virtualPixelOffsetY+100*t:e.key=="ArrowUp"||e.key=="Up"?s.virtualPixelOffsetY=s.virtualPixelOffsetY-10*t:e.key=="ArrowDown"||e.key=="Down"?s.virtualPixelOffsetY=s.virtualPixelOffsetY+10*t:e.key=="ArrowLeft"||e.key=="Up"?s.offsetX=s.offsetX-10*t:(e.key=="ArrowRight"||e.key=="Down")&&(s.offsetX=s.offsetX+10*t)}OnKeyPress(s,e){return!1}OnKeyUp(s,e){return!1}OnGotFocus(s,e){return!1}OnLostFocus(s,e){return!1}onSelected(){}onDeselecting(){return!1}onDeselected(){}OnScrolling(){}OnScrolled(){}onCancel(){}OnContextMenu(s,e){s.defaultContextMenu,s.SelectComponent(null,!1),s.invalidate(),s.lastMouseMovePos=s.getMousePointToDoc(e),s.lastMouseMoveClientPos=s.getMouseClientPoint(e);for(let t=0;t<s.webCanvasViews.length;t++){let i=s.webCanvasViews[t];if(i.HitTestObject(s.lastMouseMovePos)!=null){let r=i.HitTestObject(s.lastMouseMovePos);if(r!=null&&(i.selectedVisualObjects.length==0||i.selectObject(null,!1),i.selectObject(r,!0)),i.selectedVisualObjects.length==0)return!1;if(i.selectedVisualObjects.length>0){let n=i.selectedObject;if(n!=null)return n.OnContextMenu(i,s.lastMouseMovePos),!0;i.selectObject(null,!1)}}else i.selectObject(null,!1)}return!1}OnEnter(s,e){}OnLeave(s,e){}OnEditProperties(){}endEdit(){}saveEditingData(){}}const Dt="Night";class Rt extends ue{get Name(){return Dt}constructor(s){let e=Object.assign({backgroundColor:"#202124",grayBGColor:"#0d0d0d",stBGColor:"#000008",lineColor:"#F1F1F1",lightBorderColor:"#333333",borderColor:"#D2D2D2",gridColor:"#989898",lightGridColor:"#2d2d30",intpNodeColor:"#A5A5A5",majorGridColor:"#AAAAAA",indicatorColor:"rgba(127,127,0,.5)",hitColor:"#FF0000",dragPointColor:"#bfbfbf",scrollbarColor:"#686868",scrollbarBGColor:"#424242",scrollbarOverColor:"#7B7B7B",scrollbarDownColor:"#A1A1A1"},s);super(e)}}class jt{constructor(s,e){}}class kt extends jt{constructor(s,e){super(s,e);let t=e;if(s!=null&&s.length==null){let i=s;this.setLogPrsDataContainer(i,t)}else(s||[]).forEach((i,r)=>{this.setLogPrsDataContainer(i,t)})}addDataContainer(s,e){let t=e;if(s!=null&&s.length==null){let i=s;this.setLogPrsDataContainer(i,t)}else(s||[]).forEach((i,r)=>{this.setLogPrsDataContainer(i,t)})}findLogPrsDataContainer(s,e){let t=e;return t.voLogPlot?t.getPrsObj(s).dataContainer:null}setLogPrsDataContainer(s,e){if(!e.voLogPlot)return;let t=e.voLogPlot.trackItems;s.canvas=e;for(const i of t)for(const r of i.subVisualObjects){const n=r;if(n.objId_===s.logPrsObjId){n.dataContainer=s;return}}}}const te="NMRSpectrumImage";class Lt extends E{constructor(e,t,i){super(e,t,i);d(this,"drawAllWaves_",!1);d(this,"startDrawIndex_",0);d(this,"endDrawIndex_",359);d(this,"wavePixelHeight_",80);d(this,"topScale_",100);d(this,"baseScale_",0);d(this,"waveSpacing_",1);d(this,"startTime_",0);d(this,"isConstantST_",!0);d(this,"samplingInterval_",1);d(this,"isConstantSI_",!0);d(this,"linearities_",0);d(this,"lineStyle_","solid");d(this,"lineColor_","#0f48c2");d(this,"lineThickness_",.5);d(this,"fillMode_",4);d(this,"fillColor_","rgba(15,167,194,1)");d(this,"waveGradientBrushes_");d(this,"lowCutOffColorValue_",0);d(this,"highCutOffColorValue_",255);d(this,"isCustomColorPalettes_",!1);this.objId_=i.Id,this.type_=te,this.objType=te,this.defaultHeaderHeight=80,this.updateLayer();let r=()=>{console.log(this.dataName_,"On Edit")};this.onEdit_=i.onEdit??r,this.waveGradientBrushes_=p.interpolateColorRange("#000000","#ffffff",256),this.update(i)}updateLayer(e=null){this.layer=e??this.webCanvas.findObjectTypeInfo(te).defaultLayerNo}update(e){this.label_=e.label??this.label_,this.unit_=e.unit??this.unit_,this.index_=e.index??this.parentMaxObjIndex+1,this.lower_=e.lower??this.lower_,this.upper_=e.upper??this.upper_,this.lineColor_=e.color??this.lineColor_,this.dataName_=e.dataName??this.dataName_,this.channelId_=e.channelId??this.channelId_,this.isRenderFileId_=e.isRenderFileId??this.isRenderFileId_,this.fileId_=this.dataName_.substring(0,this.dataName_.indexOf(":")),this.segmentDataDepth_=e.segmentDataDepth??this.segmentDataDepth_,this.segmentDataTime_=e.segmentDataTime??this.segmentDataTime_,this.indexType_=e.indexType??this.indexType_,this.lineTrait.lineType=e.lineStyle??this.lineTrait.lineType,this.lineTrait.width=e.lineThickness??this.lineTrait.width,this.lineTrait.foreColor=e.color??this.lineTrait.foreColor,this.textTrait.family=e.FontFamily??this.textTrait.family,this.textTrait.foreColor=e.color??this.textTrait.foreColor,this.textTrait.style=e.FontStyle??this.textTrait.style,this.textTrait.height=e.titleFontHeight??this.textTrait.height,this.valueFontHeight=e.valueFontHeight??this.valueFontHeight,this.unitFontHeight=e.unitFontHeight??this.unitFontHeight,this.leftHeaderPosition_=e.leftHeaderPosition??this.leftHeaderPosition_,this.rightHeaderPosition_=e.rightHeaderPosition??this.rightHeaderPosition_,this.startDrawIndex_=e.startDrawIndex??this.startDrawIndex_,this.endDrawIndex_=e.endDrawIndex??this.endDrawIndex_,this.drawAllWaves_=e.drawAllWaves??this.drawAllWaves_,this.waveSpacing_=e.waveSpacing??this.waveSpacing_,this.topScale_=e.topScale??this.topScale_,this.baseScale_=e.baseScale??this.baseScale_,this.fillMode_=e.fillMode??this.fillMode_,this.fillColor_=e.fillColor??this.fillColor_,this.lower_=e.lower??this.lower_,this.upper_=e.upper??this.upper_,this.highCutOffColorValue_=e.highCutOffColorValue??this.highCutOffColorValue_,this.lowCutOffColorValue_=e.lowCutOffColorValue??this.lowCutOffColorValue_,this.isCustomColorPalettes_=e.isCustomColorPalettes??this.isCustomColorPalettes_,this.isCustomColorPalettes_&&e.colorLevels&&e.lowCutOffColor&&e.highCutOffColor?this.waveGradientBrushes_=p.interpolateColorRange(e.lowCutOffColor,e.highCutOffColor,e.colorLevels):this.isCustomColorPalettes_&&e.colorPalettes&&(this.waveGradientBrushes_=e.colorPalettes)}drawHeader(e,t,i){switch(t.save(),t.font=this.textTrait.font,t.textAlign="center",t.fillText(this.label_,(this.leftPositionOfHeader+this.rightPositionOfHeader)/2,i+15),this.textTrait.height=this.valueFontHeight,t.strokeStyle=this.lineTrait.foreColor,t.lineWidth=2,t.beginPath(),t.moveTo(this.leftPositionOfHeader,i+60),t.lineTo(this.rightPositionOfHeader,i+60),this.lineTrait.lineType===D.dash?t.setLineDash(this.lineTrait.dashStyle):this.lineTrait.lineType===D.dot&&t.setLineDash(this.lineTrait.dotStyle),t.stroke(),this.textTrait.height=this.valueFontHeight,this.valueScaleType_){case"linear":case"log":this.lower_!=null&&(t.textAlign="left",t.fillText(this.lower_.toString(),this.leftPositionOfHeader+10,i+75)),this.upper_!=null&&(t.textAlign="right",t.fillText(this.upper_.toString(),this.rightPositionOfHeader-10,i+75));break}this.textTrait.height=this.unitFontHeight,t.font=this.textTrait.font;let r=0;if(this.unit_!=null&&(t.textAlign="center",r=t.measureText(this.unit_).width,t.fillText(this.unit_,(this.leftPositionOfHeader+this.rightPositionOfHeader)/2,i+75)),this.fileId_!==null&&this.fileId_!==""&&this.isRenderFileId_&&t.fillText("["+this.fileId_+"]",(this.leftPositionOfHeader+this.rightPositionOfHeader)/2+r+10,i+75),e.isObjectSelected(this)){const n=this.rightPositionOfHeader-this.leftPositionOfHeader;this.drawSelectedRect(t,this.leftPositionOfHeader,i,n,this.headerHeight)}t.restore(),this.fillGradientWave(t,this.smoothPoints([new y(this.leftPositionOfHeader,i+60),new y(this.leftPositionOfHeader+(this.leftPositionOfHeader+this.rightPositionOfHeader)/4,i+60-20),new y(this.leftPositionOfHeader+(this.leftPositionOfHeader+this.rightPositionOfHeader)/2,i+60-10),new y(this.leftPositionOfHeader+(this.leftPositionOfHeader+this.rightPositionOfHeader)*3/4,i+60-40),new y(this.rightPositionOfHeader,i+60)],20),i+60,40,0)}redraw(e,t,i,r){if(!this.dataContainer)return;let n=e.RectToScreen(this.parentTrack.bodyRect_);if(this.layer!=r||!n.intersectsWith(i))return;let a=this.webCanvas,l=a.virtualPixelOffsetY,h=a.virtualPixelOffsetTime;a.canvasTheme;let o=a.voLogPlot.measure,u=o.timeScale?o.dpMilSec_:o.dprm_i_,c=o.timeScale?h:l;t.save(),a.setRectClip(t,new C(this.leftPositionOfHeader,0,this.rightPositionOfHeader-this.leftPositionOfHeader,n.h)),t.strokeStyle=this.lineColor_,t.lineJoin="round",t.lineWidth=this.lineThickness_,p.setCtxLineDash(t,this.lineTrait),t.fillStyle=this.fillColor_,this.drawAllWaves_?this.dataContainer.fetch_(c/u,(c+n.h)/u,(f,g)=>{p.isEmpty(g)||this.drawWave(t,f*u-c,g)}):(this.dataContainer.recalcIndexFilterData(this.waveSpacing_),this.dataContainer.fetchByIndex(c/u,(c+n.h)/u,(f,g)=>{p.isEmpty(g)||this.drawWave(t,f*u-c,g)})),t.restore()}drawWave(e,t,i){let{max:r,min:n}=p.findMinMax(i),a=r-n==0?1:r-n,l;switch(this.linearities_){case 0:l=r/(this.topScale_-this.baseScale_)*this.wavePixelHeight_/a;break;case 1:l=r/(this.topScale_-this.baseScale_)*this.wavePixelHeight_/Math.log10(r/n);break}const h=[];for(let c=this.startDrawIndex_;c<=this.endDrawIndex_;c++){const f=this.isConstantST_?this.intervalMapping(this.isConstantSI_?(c+this.startTime_)*this.samplingInterval_:c+this.startTime_):this.intervalMapping(this.isConstantSI_?c*this.samplingInterval_:c);let g;switch(this.linearities_){case 0:g=t-l*(i[c]-this.baseScale_),p.isNullValue(i[c])&&(g=t);break;case 1:g=t-l*Math.log10(i[c]/this.baseScale_),i[c]/this.baseScale_<=0&&(g=t);break}h.push(new y(f,g))}const o=this.smoothPoints(h,10);switch(this.fillMode_){case 1:this.fillWave(e,o,t);break;case 2:this.fillWave(e,o.map(c=>new y(c.x,Math.min(c.y,t))),t);break;case 3:this.fillWave(e,o.map(c=>new y(c.x,Math.max(c.y,t))),t);break;case 4:this.fillGradientWave(e,o,t,r,n);break}const u=new Path2D;u.moveTo(o[0].x,o[0].y);for(let c=1;c<o.length;c++)u.lineTo(o[c].x,o[c].y);e.stroke(u)}fillWave(e,t,i){const r=new Path2D;r.moveTo(t[0].x,t[0].y);for(let n=1;n<t.length;n++)r.lineTo(t[n].x,t[n].y);r.lineTo(t[t.length-1].x,i),r.lineTo(t[0].x,i),r.closePath(),e.fill(r)}fillGradientWave(e,t,i,r,n){if(t.length===0)return;if(this.highCutOffColorValue_-this.lowCutOffColorValue_===0)throw new Error("highCutOffColorValue_ 与 lowCutOffColorValue 不能相等");const l=o=>{const u=Math.abs(o-i),c=Math.max(n,Math.min(r,u)),f=(this.highCutOffColorValue_-this.lowCutOffColorValue_)/(r-n),m=(this.lowCutOffColorValue_+(c-n)*f-this.lowCutOffColorValue_)/(this.highCutOffColorValue_-this.lowCutOffColorValue_),w=Math.round(m*(this.waveGradientBrushes_.length-1));return Math.max(0,Math.min(this.waveGradientBrushes_.length-1,w))},h=.5;for(let o=0;o<t.length-1;o++){const u=t[o],c=t[o+1];e.beginPath(),e.moveTo(u.x-h,i),e.lineTo(u.x-h,u.y),e.lineTo(c.x+h,c.y),e.lineTo(c.x+h,i),e.closePath();const f=l(u.y);e.fillStyle=this.fillMode_==4?this.waveGradientBrushes_[f]:this.fillColor_,e.fill()}}smoothPoints(e,t){const i=e.length,r=e.map(h=>h.x),n=e.map(h=>h.y),a=new Array(i).fill(0),l=[new y(r[0],n[0])];this.spline(r,n,i,0,0,0,a);for(let h=0;h<i-1;h++){const o=r[h],u=r[h+1],c=(u-o)/t;for(let f=1;f<t;f++){const g=o+f*c,m=this.splint(r,n,a,i,g);m!==null&&l.push(new y(g,m))}l.push(new y(u,n[h+1]))}return l}spline(e,t,i,r,n,a,l){const h=new Array(i).fill(0);r===1?(l[0]=-.5,h[0]=3/(e[1]-e[0])*((t[1]-t[0])/(e[1]-e[0])-n)):(l[0]=0,h[0]=0);for(let o=1;o<i-1;o++){const u=(e[o]-e[o-1])/(e[o+1]-e[o-1]),c=u*l[o-1]+2;l[o]=(u-1)/c;const f=(t[o+1]-t[o])/(e[o+1]-e[o]),g=(t[o]-t[o-1])/(e[o]-e[o-1]);h[o]=(6*(f-g)/(e[o+1]-e[o-1])-u*h[o-1])/c}r===1?(l[i-1]=.5,h[i-1]=3/(e[i-1]-e[i-2])*(a-(t[i-1]-t[i-2])/(e[i-1]-e[i-2]))):l[i-1]=0;for(let o=i-2;o>=0;o--)l[o]=l[o]*l[o+1]+h[o]}splint(e,t,i,r,n){let a=0,l=r-1;for(;l-a>1;){const c=Math.floor((l+a)/2);e[c]>n?l=c:a=c}const h=e[l]-e[a];if(h===0)return null;const o=(e[l]-n)/h,u=(n-e[a])/h;return o*t[a]+u*t[l]+((o*o*o-o)*i[a]+(u*u*u-u)*i[l])*(h*h)/6}}class It extends lt{constructor(e){super(e);d(this,"_offsetX",0);d(this,"_offsetY",0);d(this,"_logPlotView");d(this,"_vScrollBar");d(this,"_hScrollBar");d(this,"_scrollBarWidth",12);d(this,"_addingLayerLine",!1);d(this,"_addingControlLine",!1);d(this,"_addingFaultLine",!1);d(this,"_acceptNewDroppedItem",!1);d(this,"_droppedCheckFunc");d(this,"_isMouseMoveHitTest",!1);d(this,"_hideBookmarker",!1);d(this,"_bookmarkerContextMenu");d(this,"_trackContextMenuFunc");d(this,"_prsObjHeaderContextMenuFunc");d(this,"_mouseOverSectionFunc");d(this,"_mouseLeaveSectionFunc");d(this,"_logTrackScaleNotRound",!0);d(this,"_virtualPixelOffsetY");d(this,"_virtualPixelOffsetX");d(this,"_virtualPixelOffsetTime");d(this,"_lineIndicator");d(this,"_toolManager");d(this,"_addingNote",!1);this.startup()}startup(){this.objTypeInfos=[new V($e,1),new V(Z,2),new V(X,3),new V(fe,4),new V(ge,5),new V(we,6),new V(Te,7),new V(Q,8),new V(te,9),new V(null,9)],this.loadObjTypeInfos(this.objTypeInfos),this._logPlotView=new qe(this),this.webCanvasViews&&this.webCanvasViews.push(this._logPlotView),this._vScrollBar=new pt(this),this._hScrollBar=new Ot(this),this.visualComponents.push(this._vScrollBar,this._hScrollBar),this._bookmarkerContextMenu=document.getElementById("bookmarkerContextMenu"),this.contextMenus.push(this._bookmarkerContextMenu),this.virtualPixelOffsetY=this.voLogPlot.measure.startIndex_*this.voLogPlot.measure.dprm_i_,this.virtualPixelOffsetTime=this.voLogPlot.measure.startTime_*this.voLogPlot.measure.dpMilSec_,this._lineIndicator=new ht(this),this._toolManager=new ot(this),this._toolManager.registerTool(new xt),this._toolManager.defaultTool=this._toolManager.findTool(Je),this._toolManager.currentTool=this._toolManager.defaultTool,this.setEvent(this._toolManager)}get logPlotView(){return this._logPlotView}set logPlotView(e){this._logPlotView=e}get vScrollBar(){return this._vScrollBar}set vScrollBar(e){this._vScrollBar=e}get hScrollBar(){return this._hScrollBar}set hScrollBar(e){this._hScrollBar=e}get scrollBarWidth(){return this._scrollBarWidth}set scrollBarWidth(e){this._scrollBarWidth=e}get addingLayerLine(){return this._addingLayerLine}set addingLayerLine(e){this._addingLayerLine=e}get addingControlLine(){return this._addingControlLine}set addingControlLine(e){this._addingControlLine=e}get addingFaultLine(){return this._addingFaultLine}set addingFaultLine(e){this._addingFaultLine=e}get acceptNewDroppedItem(){return this._acceptNewDroppedItem}set acceptNewDroppedItem(e){this._acceptNewDroppedItem=e}get droppedCheckFunc(){return this._droppedCheckFunc}set droppedCheckFunc(e){this._droppedCheckFunc=e}get isMouseMoveHitTest(){return this._isMouseMoveHitTest}set isMouseMoveHitTest(e){this._isMouseMoveHitTest=e}get hideBookmarker(){return this._hideBookmarker}set hideBookmarker(e){this._hideBookmarker=e}get bookmarkerContextMenu(){return this._bookmarkerContextMenu}set bookmarkerContextMenu(e){this._bookmarkerContextMenu=e}get trackContextMenuFunc(){return this._trackContextMenuFunc}set trackContextMenuFunc(e){this._trackContextMenuFunc=e}get prsObjHeaderContextMenuFunc(){return this._prsObjHeaderContextMenuFunc}set prsObjHeaderContextMenuFunc(e){this._prsObjHeaderContextMenuFunc=e}get mouseOverSectionFunc(){return this._mouseOverSectionFunc}set mouseOverSectionFunc(e){this._mouseOverSectionFunc=e}get mouseLeaveSectionFunc(){return this._mouseLeaveSectionFunc}set mouseLeaveSectionFunc(e){this._mouseLeaveSectionFunc=e}get logTrackScaleNotRound(){return this._logTrackScaleNotRound}set logTrackScaleNotRound(e){this._logTrackScaleNotRound=e}get lineIndicator(){return this._lineIndicator}set lineIndicator(e){this._lineIndicator=e}get toolManager(){return this._toolManager}set toolManager(e){this._toolManager=e}get addingNote(){return this._addingNote}set addingNote(e){this._addingNote=e}get clientWidth(){let e=this.voLogPlot.measure,t=e.timeScale?e.endTime_-e.startTime_:e.endIndex_-e.startIndex_,i=this.voLogPlot.clientBodysRect.h/(e.timeScale?e.dpMilSec_:e.dprm_i_);return t>i?this.stageWidth-this._scrollBarWidth:this.stageWidth}get clientHeight(){return this.voLogPlot.headerWidth>this.clientWidth?this.stageHeight-this._scrollBarWidth:this.stageHeight}get offsetX(){return this._offsetX}set offsetX(e){let t=Math.max(0,this.voLogPlot.headerWidth-this._logPlotView.drawRect.w);e=Math.min(e,t),e=Math.max(0,e),this.offsetX!=e&&(this._offsetX=e,this._hScrollBar.redraw(this._hScrollBar.drawRect),this._logPlotView.scrollTo(-e,0))}get offsetY(){return this._offsetY}set offsetY(e){let t=Math.max(0,this.voLogPlot.headerHeight-this._logPlotView.drawRect.h);e=Math.min(e,t),e=Math.max(0,e),this.offsetY!=e&&(this._offsetY=e,this._vScrollBar.redraw(this._hScrollBar.drawRect),this._logPlotView.scrollTo(0,-e))}get realTime(){return this.virtualPixelOffsetTime/this.voLogPlot.measure.dpMilSec_}set realTime(e){this.virtualPixelOffsetTime=e*this.voLogPlot.measure.dpMilSec_}get voLogPlot(){return this._logPlotView.voLogPlot}get virtualPixelOffsetX(){return this._virtualPixelOffsetX}set virtualPixelOffsetX(e){e=Math.max(e,this.voLogPlot.measure.startIndex_*this.voLogPlot.measure.dprm_i_),e=Math.min(e,Math.max(this.voLogPlot.measure.endIndex_-this._hScrollBar.pageDist_,this.voLogPlot.measure.startIndex_)*this.voLogPlot.measure.dprm_i_),this.virtualPixelOffsetX!=e&&(this._virtualPixelOffsetX=e,this.voLogPlot.readLogPrsData(),this._hScrollBar.redraw(this._hScrollBar.drawRect),this._logPlotView.scrollTo(-e,this._logPlotView.origin.y))}get stageRect(){return new C(0,0,this.stageWidth,this.stageHeight)}get realY(){return this.virtualPixelOffsetY/this.voLogPlot.measure.dprm_i_}set realY(e){this.virtualPixelOffsetY=e*this.voLogPlot.measure.dprm_i_}get SelectedObject(){if(this.webCanvasViews){for(let e=0;e<this.webCanvasViews.length;e++)if(this.webCanvasViews[e].selectedVisualObjects.length>0)return this.webCanvasViews[e].selectedVisualObjects[this.webCanvasViews[e].selectedVisualObjects.length-1]}return null}get SelectedComponent(){return this.selectedVisualComponents.length>0?this.selectedVisualComponents[this.selectedVisualComponents.length-1]:null}get plotMeasureType(){return this.voLogPlot.measure.timeScale?"time":"depth"}set plotMeasureType(e){}addDefaultTrack(e=null){let t=this.voLogPlot.trackItems,i=0;t.length>0&&(i=t.length);let r=p.guid24();for(;t.find(h=>h.trackId_==r)!=null;)r=p.guid24();let n,a;switch(e.type){case"depth":n="Depth "+String(i),a=60;break;case"normal":n="Track "+String(i),a=300;break;default:n="Track "+String(i),a=300;break}let l={Id:r,type:"normal",valueScaleType:"linear",label:n,index:i,width:a,showVerticalGrid:!0,showHorizontalGrid:!0};return Object.assign(l,e),this.voLogPlot.subVisualObjects.push(new ve(this,l,this.voLogPlot)),this.layout(),this.invalidate(),l.Id}setTrackHeaderHideShow(){this.voLogPlot.showHeader=!this.voLogPlot.showHeader,this.layout(),this.invalidate()}goToTime(e){this.virtualPixelOffsetTime=Date.parse(e)*this.voLogPlot.measure_.dpMilSec_}goToTimestamp(e){this.virtualPixelOffsetTime=e*this.voLogPlot.measure_.dpMilSec_}addPlotTrack(e){this.voLogPlot.subVisualObjects.push(new ve(this,e,this.voLogPlot)),this.layout(),this.invalidate()}addTopsets(e){(e||[]).forEach((t,i)=>{let r={};r.data=t,this.voLogPlot.subVisualObjects.push(new Xe(this,r,this.voLogPlot))}),this.layout(),this.invalidate()}deletePlotTrack(e){let t=this.voLogPlot.trackItems.find(i=>i.trackId_==e);if(t!=null){let i=this.voLogPlot.subVisualObjects;i.splice(i.indexOf(t),1),this.clearSelectedObject(t)}this.layout(),this.invalidate()}deleteBookmarker(e){let t=this.voLogPlot.bookmarkerItems.find(i=>i.bookmarkerId_==e);if(t!=null){let i=this.voLogPlot.subVisualObjects;i.splice(i.indexOf(t),1)}this.layout(),this.invalidate()}addPlotPrsObj(e,t){const i=this.voLogPlot.trackItems.find(r=>r.trackId_===e);if(!i){console.error(`Track with ID ${e} not found`);return}switch(t.type){case"curve":i.subVisualObjects.push(new me(this,i,t));break;case"image":i.subVisualObjects.push(new _e(this,i,t));break;case"wave":i.subVisualObjects.push(new We(this,i,t));break;case"trace":i.subVisualObjects.push(new Ge(this,i,t));break;case Q:i.subVisualObjects.push(new Ae(this,i,t));break;case te:i.subVisualObjects.push(new Lt(this,i,t));break;default:console.warn(`Func addPlotPrsObj Unsupported type: ${t.type}`);break}this.layout(),this.invalidate()}deletePlotPrsObj(e){let t=this.getPrsObj(e);if(t!=null){let i=t.parentVisualObject;i.subVisualObjects.splice(i.subVisualObjects.indexOf(t),1),this.clearSelectedObject(t),this.layout(),this.invalidate()}}clearSelectedObject(e){for(let t=0;t<this.webCanvasViews.length;t++)this.webCanvasViews[t].selectedVisualObjects.length>0&&this.webCanvasViews[t].selectedVisualObjects.indexOf(e)>=0&&this.webCanvasViews[t].selectedVisualObjects.splice(this.webCanvasViews[t].selectedVisualObjects.indexOf(e),1)}startAddingNote(){this._addingNote=!0}HitPlotBody(e,t){return!1}applyAllTracks(e){for(const t of this.voLogPlot.trackItems)t.update(e)}getSelectedObject(){for(let e=0;e<this.webCanvasViews.length;e++)if(this.webCanvasViews[e].selectedVisualObjects.length>0)return this.webCanvasViews[e].selectedVisualObjects[this.webCanvasViews[e].selectedVisualObjects.length-1];return null}getDeepTrack(){return this.voLogPlot.trackItems.find(t=>t.type_=="depth"||t.type_=="timeScale")}changeNightMode(e){e?this.canvasTheme=new Rt(null):this.canvasTheme=new ue(null),this.invalidate()}getPlotBookmarkers(){let e=new Array;return e=this.voLogPlot.bookmarkerItems,e}onDropItem(e){this.selectedVisualComponents=[];for(let t=0;t<this.webCanvasViews.length;t++){let i=this.webCanvasViews[t];i.selectedVisualObjects.length>0&&(i.selectedVisualObjects=[])}this._acceptNewDroppedItem=!0,this._droppedCheckFunc=e}addTrackDropItem(e,t){if(e.objType==Z){let i=!1;if(t!=null){let r=t.opts;r!=null&&(r.type=="curve"?(e.subVisualObjects.push(new me(this,e,r)),i=!0):r.type=="image"&&(e.subVisualObjects.push(new _e(this,e,r)),i=!0)),i&&this.addDataContainer(t.dataOpts)}}this.layout(),this.invalidate()}getTopIndex(){return this.voLogPlot.measure.timeScale?this.realTime:this.realY}getLastMouseDownIndex(){let e=this.voLogPlot.trackItems;if(e.length>0&&this.lastMouseDownPos){let t=e[e.length-1],i=this.voLogPlot.measure,r=this.virtualPixelOffsetY,n=this.virtualPixelOffsetTime;return this.lastMouseDownPos.y>t.bodyRect_.y?i.timeScale?(this.lastMouseDownPos.y-t.bodyRect_.y+n)/i.dpMilSec_:(this.lastMouseDownPos.y-t.bodyRect_.y+r)/i.dprm_i_:null}else return null}setMouseOverPlotChartFunc(e){e!=null?this._mouseOverSectionFunc=e:this._mouseOverSectionFunc=null}setMouseLeavePlotChartFunc(e){e!=null?this._mouseLeaveSectionFunc=e:this._mouseLeaveSectionFunc=null}setTrackContextMenuFunc(e){this._trackContextMenuFunc=e}setPrsObjHeaderContextMenuFunc(e){this._prsObjHeaderContextMenuFunc=e}getMousePointToDoc(e){return new y((e.clientX-this.appContainer.getBoundingClientRect().left+document.body.scrollLeft)*this.dpr,(e.clientY-this.appContainer.getBoundingClientRect().top+document.body.scrollTop)*this.dpr)}getMouseClientPoint(e){return new y(e.clientX,e.clientY)}clearTrackSelectedStatus(){}clearMarkSelectedStatus(){}updatePlotPrsObj(e,t){this.getPrsObj(e).update(t),this.layout(),this.invalidate()}updatePlotMeasureRatio(e){let t=this.realY;console.log("keep real y",t),this.updatePlot("measure/ratio",e),this.realY=t,this.invalidate()}updatePlot(e,t){this.update_path_(e,t),this.invalidate()}update_path_(e,t){let i=e.split("/");try{i.length==2}catch(r){console.warn(r)}}HitTestComponent(e){if(this.visualComponents.length>0){for(let t=0;t<this.visualComponents.length;t++)if(this.visualComponents[t].isVisible&&!this.visualComponents[t].hitTestProtected){let i=this.visualComponents[t].HitTestObject(e);if(i)return i}}return null}redrawTransparentFloat(e,t,i,r,n){if(this.clearFloatCanvas(),this.ctxFloat.save(),this.ctxFloat.globalAlpha=.5,this.ctxFloat.translate(r,n),t)for(let a=0;a<this.findObjectTypeInfo(oe).defaultLayerNo+1;a++)t.redraw(e,this.ctxFloat,i,a);this.ctxFloat.restore()}clearFloatCanvas(){this.ctxFloat.clearRect(0,0,this.floatCanvasElement.width,this.floatCanvasElement.height)}clearContextMenu(){for(const e of this.contextMenus)e!=null&&(e.style.display="none")}goToDepthIndex(e){this.virtualPixelOffsetY=e*this.voLogPlot.measure_.dprm_i_}getPrsObj(e){let t=this.voLogPlot.trackItems;for(let i=0;i<t.length;i++){let r=t[i].subVisualObjects.map(n=>n);if(r!=null&&r.length>0){for(let n=0;n<r.length;n++)if(r[n].objId_==e)return r[n]}}return null}setNewImage(e,t,i,r){this.logDataLoader.findLogPrsDataContainer(e,this).setSectionUrl(t,i,r)}addDataContainer(e){this._logPlotView==null&&(this._logPlotView=new qe(this),this.startup()),this.logDataLoader==null?this.logDataLoader=new kt(e,this):this.logDataLoader.addDataContainer(e,this),e.isTimeData?this.voLogPlot.setSectionsTime():this.voLogPlot.setSectionsIndex(),this.readLogPrsData(),this.layout(),this.invalidate()}updateDataContainer(e,t){let i=this.logDataLoader.findLogPrsDataContainer(e,this);i.update(t),i.clearSections(),t.isTimeData?this.voLogPlot.setSectionsTime():this.voLogPlot.setSectionsIndex(),this.layout(),this.readLogPrsData(),this.invalidate()}setDataLoadingStatus(e,t){let i=this.logDataLoader.findLogPrsDataContainer(e,this);i!=null&&(i.loadingData=t)}readLogPrsData(){for(let e=0;e<this.voLogPlot.trackItems.length;e++){let t=this.voLogPlot.trackItems[e].subVisualObjects.map(i=>i);if(t!=null&&t.length>0)for(let i=0;i<t.length;i++){let r=t[i].dataContainer;r!=null&&(r.loadingData||(this.plotMeasureType=="depth"?r.getData(this.voLogPlot.topIndex,this.voLogPlot.bottomIndex):r.getData(this.voLogPlot.topTime,this.voLogPlot.bottomTime)))}}}setNewData(e,t,i,r){let n=this.logDataLoader.findLogPrsDataContainer(e,this);if(!n)return;n.setSectionData(t,i,r);let a=this.getPrsObj(e).parentVisualObject,l=new C;this.voLogPlot.trackItems.indexOf(a)>=0&&(l=this._logPlotView.RectToScreen(a.bodyRect_)),this.invalidate(l)}updatePlotTrack(e,t){let i=this.voLogPlot.trackItems.find(r=>r.trackId_===e);i!=null&&(i.update(t),this.layout(),this.invalidate())}setRectClip(e,t){let i=new Path2D;i.rect(t.x,t.y,t.w,t.h),e.clip(i),i.closePath()}getPlotTracks(){let e=new Array;return e=this.voLogPlot.trackItems,e}getPlotTrackObjs(e){let t=this.voLogPlot.trackItems.find(i=>i.trackId_==e);return t!=null?t.subVisualObjects.map(i=>i):null}addFillArea(e,t){const i=this.voLogPlot.trackItems.find(r=>r.trackId_===e);if(!i){console.error(`Track with ID ${e} not found`);return}i.subVisualObjects.push(new Ee(this,i,t)),this.layout(),this.invalidate()}getFillAreas(){const e=[];for(const t of this.voLogPlot.trackItems)for(const i of t.subVisualObjects)i instanceof Ee&&e.push(i);return e}getPlotTrack(e){return this.voLogPlot.trackItems.find(t=>t.trackId_==e)}get virtualPixelOffsetY(){return this._virtualPixelOffsetY}set virtualPixelOffsetY(e){e=Math.max(e,this.voLogPlot.measure.startIndex_*this.voLogPlot.measure.dprm_i_),e=Math.min(e,Math.max(this.voLogPlot.measure.endIndex_-this._vScrollBar.pageDist_,this.voLogPlot.measure.startIndex_)*this.voLogPlot.measure.dprm_i_),this.virtualPixelOffsetY!=e&&(this._virtualPixelOffsetY=e,this.voLogPlot.readLogPrsData(),this._vScrollBar.redraw(this._vScrollBar.drawRect),this._logPlotView.scrollTo(this._logPlotView.origin.x,-e))}get virtualPixelOffsetTime(){return this._virtualPixelOffsetTime}set virtualPixelOffsetTime(e){e=Math.max(e,this.voLogPlot.measure.startTime_*this.voLogPlot.measure.dpMilSec_),e=Math.min(e,Math.max(this.voLogPlot.measure.endTime_-this._vScrollBar.pageDist_,this.voLogPlot.measure.startTime_)*this.voLogPlot.measure.dpMilSec_),this.virtualPixelOffsetTime!=e&&(this._virtualPixelOffsetTime=e,this.voLogPlot.readLogPrsData(),this._vScrollBar.redraw(this._vScrollBar.drawRect),this._logPlotView.scrollTo(this._logPlotView.origin.x,-e))}getPlotParams(){return this.voLogPlot.measure}updateRatio(e){this.voLogPlot.measure.timeScale?this.updateTimeRatio(e):this.updateDepthRatio(e),this.layout(),this.invalidate()}updateDepthRatio(e){let t=this.realY;this.voLogPlot.measure.ratio=e,this.virtualPixelOffsetY=t*this.voLogPlot.measure.dprm_i_}updateTimeRatio(e){let t=this.realTime;this.voLogPlot.measure.timeRatio=e,this.virtualPixelOffsetTime=t*this.voLogPlot.measure.dpMilSec_}get timeScale(){return this.voLogPlot.measure.timeScale}set timeScale(e){this.voLogPlot.measure.timeScale=e}switchTimeScale(e,t,i,r,n,a){if(this.voLogPlot.measure.isAutoDepth){if(this.voLogPlot.measure.timeScale){for(const l of this.voLogPlot.trackItems)l.type_===ee.Depth&&(l.trackName_=l.trackName_.replace("Depth","Time"),l.type_=ee.TimeScale);this.updateTimePlotParams(r,n,a)}else{for(const l of this.voLogPlot.trackItems)l.type_===ee.TimeScale&&(l.trackName_=l.trackName_.replace("Time","Depth"),l.type_=ee.Depth);this.updatePlotParams(e,t,i)}this.layout(),this.invalidate()}}updatePlotParams(e,t,i){let r=this.realY;this.voLogPlot.measure.ratio=i,this.voLogPlot.measure.startIndex_=e,this.voLogPlot.measure.endIndex_=t,this.virtualPixelOffsetY=r*this.voLogPlot.measure.dprm_i_}updateTimePlotParams(e,t,i){let r=this.realTime;this.voLogPlot.measure.timeRatio=i,this.voLogPlot.measure.startTime_=e,this.voLogPlot.measure.endTime_=t,this.virtualPixelOffsetTime=r*this.voLogPlot.measure.dpMilSec_}}var ie=(_=>(_[_.Vertical=0]="Vertical",_[_.Horizontal=1]="Horizontal",_))(ie||{});const Me=2;class Ft{constructor(s){d(this,"chName");d(this,"logId");d(this,"logName");d(this,"channelSetIndex");d(this,"dataDirection");d(this,"canvas");d(this,"sections",new Array);d(this,"getDataFunc");d(this,"rowFilterData",[]);d(this,"indexFilterData",[]);d(this,"logPrsObjId");d(this,"palettes");d(this,"startIndex");d(this,"endIndex");d(this,"startTime");d(this,"endTime");d(this,"loadingData",!1);d(this,"type");Object.assign(this,s),this.dataDirection=s.dataDirection?ie[s.dataDirection]:ie.Vertical}update(s){Object.assign(this,s)}get timeScalePlot(){return this.canvas!=null?this.canvas.plotMeasureType=="time":!1}get MinSectionTvd(){let s=1/0;return this.sections.forEach(e=>{let t=Math.min.apply(Math,e.logData.map(function(i){return i.tvd}));s=Math.min(s,t)}),s}get MaxSectionTvd(){let s=-1/0;return this.sections.forEach(e=>{let t=Math.max.apply(Math,e.logData.map(function(i){return i.tvd}));s=Math.max(s,t)}),s}get MinSectionTvt(){let s=1/0;return this.sections.forEach(e=>{let t=Math.min.apply(Math,e.logData.map(function(i){return i.tvt}));s=Math.min(s,t)}),s}get MaxSectionTvt(){let s=-1/0;return this.sections.forEach(e=>{let t=Math.max.apply(Math,e.logData.map(function(i){return i.tvt}));s=Math.max(s,t)}),s}get MinSectionVs(){let s=1/0;return this.sections.forEach(e=>{let t=Math.min.apply(Math,e.logData.map(function(i){return i.vs}));s=Math.min(s,t)}),s}get MaxSectionVs(){let s=-1/0;return this.sections.forEach(e=>{let t=Math.max.apply(Math,e.logData.map(function(i){return i.vs}));s=Math.max(s,t)}),s}get prsObj(){return this.canvas.getPrsObj(this.logPrsObjId)}clearSections(){this.sections=new Array}setSectionUrl(s,e,t){let i=this.timeScalePlot?this.sections.filter(r=>r.startTime>=s&&r.endTime<=e):this.sections.filter(r=>r.startIndex>=s&&r.endIndex<=e);for(let r=0;r<t.length;r++){let n=t[r],a=this.timeScalePlot?i.find(l=>l.startTime>=n.start&&l.endTime<=n.end):i.find(l=>l.startIndex>=n.start&&l.endIndex<=n.end);if(n!=null&&a!=null){a.imgUrl=n.url;const l=new Image;l.src=a.imgUrl,l.setAttribute("crossOrigin","Anonymous"),a.img=l,a.dataLoaded=!0,a.img.onload=h=>{this.canvas.invalidate()}}}}fetch_nearest_(s,e=!1,t=Me){return{ok:!1}}get timeThreshold(){return this.canvas.voLogPlot.measure.timeRatio*1e3/10}fetch_nearest_logplot(s){let e;if(this.canvas.voLogPlot.measure.timeScale){let i=this.sections.filter(r=>r.logData.find(n=>Math.abs(n.time-s)<=this.timeThreshold)!=null);if(i!=null&&i.length>0)for(e of i){let r=e.logData;if(r.length>0&&s>=0){let n=r.concat([]);n.sort(function(l,h){return Math.abs(l.time-s)-Math.abs(h.time-s)});let a=n[0];if(a!=null)return{ok:!0,index:a.time,value:a.y}}}}else{let i=this.sections.filter(r=>r.logData.find(n=>Math.abs(n.tvd-s)<=Me)!=null);if(i!=null&&i.length>0)for(e of i){let r=e.logData;if(r.length>0&&s>=0){let n=r.concat([]);n.sort(function(l,h){return Math.abs(l.tvd-s)-Math.abs(h.tvd-s)});let a=n[0];if(Math.abs(s-a.tvd)<Me&&a!=null)return{ok:!0,index:a.tvd,value:a.y}}}}return{ok:!1}}fetch_hLog(s,e,t){}fetch_multi_(s,e,t){let i=this.completeData.sort(function(n,a){return n.tvd-a.tvd}),r=[];if(i.filter(n=>n.tvd>=s&&n.tvd<=e).length>0){let n=i.filter(a=>a.tvd>=s&&a.tvd<=e);for(let a=0;a>1?a<n.length-1:a<n.length;a++){let l=n[a],h=n[a+1];if(a==0&&l.tvd>s){let o=i.indexOf(l);if(o-1>=0){let u=i[o-1];(u.tvd<s||u.tvd>e)&&r.push({tvd:u.tvd,y:u.y})}}if(r.push({tvd:l.tvd,y:l.y}),n.length==1){let o=i.indexOf(l),u=i[o+1];u&&(u.tvd<s||u.tvd>e)&&r.find(c=>c.tvd==u.tvd)==null&&r.push({tvd:u.tvd,y:u.y})}if(a==n.length-2&&h&&h.tvd<e){let o=i.indexOf(h);if(r.push({tvd:h.tvd,y:h.y}),o<i.length-1){let u=i[o+1];(u.tvd<s||u.tvd>e)&&r.push({tvd:u.tvd,y:u.y})}}}}else for(let n=0;n<i.length-1;n++){let a=i[n],l=i[n+1];(a.tvd<s&&l.tvd>e||a.tvd>e&&l.tvd<s)&&(r.find(h=>h.tvd==a.tvd)==null&&r.push({tvd:a.tvd,y:a.y}),r.find(h=>h.tvd==l.tvd)==null&&r.push({tvd:l.tvd,y:l.y}))}for(let n=0;n<r.length;n++){let a=r[n];t(a.tvd,a.y)}}getData_old(s,e){let t=!this.timeScalePlot,i=t?this.sections.filter(r=>r.startIndex>=this.startIndex&&r.startIndex<this.endIndex||r.endIndex>this.startIndex&&r.endIndex<=this.endIndex):this.sections.filter(r=>r.startTime>=this.startTime&&r.startTime<this.endTime||r.endTime>this.startTime&&r.endTime<=this.endTime);for(let r=0;r<i.length;r++){let n=i[r],a=t?n.startIndex:n.startTime,l=t?n.endIndex:n.endTime;(s>=a&&e<=l||a>=s&&a<e||l>s&&l<=e)&&(this.type=="curve"&&n.logData.length==0||this.type=="image"&&n.imgUrl==null||this.type=="activity"&&n.logData.length==0||this.type=="wave"&&n.logData.length==0||this.type=="trace"&&n.logData.length==0)&&!n.dataLoaded&&(this.loadingData=!0,this.getDataFunc!=null?this.getDataFunc(this.logPrsObjId,this.logId,this.type,this.channelSetIndex,this.chName,a,l,this.dataDirection):this.loadingData=!1)}}getData(s,e){const t=!this.timeScalePlot,i=this.getVisibleSections(s,e,t);for(const r of i)this.isSectionInRange(r,s,e,t)&&this.isSectionDataLoadable(r)&&(this.loadingData=!0,this.getDataFunc?this.getDataFunc(this.logPrsObjId,this.logId,this.type,this.channelSetIndex,this.chName,t?r.startIndex:r.startTime,t?r.endIndex:r.endTime,this.dataDirection):this.loadingData=!1)}getVisibleSections(s,e,t){return this.sections.filter(i=>{const r=t?i.startIndex:i.startTime,n=t?i.endIndex:i.endTime;return r>=s&&r<e||n>s&&n<=e})}isSectionInRange(s,e,t,i){const r=i?s.startIndex:s.startTime,n=i?s.endIndex:s.endTime;return e>=r&&t<=n||r>=e&&r<t||n>e&&n<=t}isSectionDataLoadable(s){if(this.timeScalePlot){if(this.prsObj.indexType_!==ce.Time)return!1}else if(this.prsObj.indexType_!==ce.Depth)return!1;let e;switch(this.type){case"curve":case"activity":case"wave":case"trace":case"NMRSpectrum":e=s.logData.length===0;break;case"image":e=s.imgUrl==null;break;default:e=!1;break}return e&&!s.dataLoaded}get completeData(){let s=[];for(let e=0;e<this.sections.length;e++)this.sections[e].logData.length>0&&(s=s.concat(this.sections[e].logData));return s}fetch_(s,e,t){const i=this.timeScalePlot?u=>u.time:u=>u.tvd,r=this.timeScalePlot?"time":"tvd";let n=[...this.completeData].sort((u,c)=>i(u)-i(c)),a=[],l=0,h=n.length-1;for(;l<=h&&i(n[l])<s;)l++;for(;h>=l&&i(n[h])>e;)h--;let o=n.slice(l,h+1);if(o.length>0){let u=o[0],c=o[o.length-1];if(i(u)>s&&l-1>=0){let f=n[l-1];i(f)<s&&o.splice(0,0,f)}if(i(c)<e&&h+1<n.length){let f=n[h+1];i(f)>e&&o.push(f)}a=o}else for(let u=0;u<n.length-1;u++){let c=n[u],f=n[u+1];(i(c)<s&&i(f)>e||i(c)>e&&i(f)<s)&&(a.some(g=>g[r]===i(c))||a.push({[r]:i(c),y:c.y}),a.some(g=>g[r]===i(f))||a.push({[r]:i(f),y:f.y}))}for(let u of a)t(u[r],u.y)}fetch_old(s,e,t){let i=this.timeScalePlot?this.completeData.sort(function(n,a){return n.time-a.time}):this.completeData.sort(function(n,a){return n.tvd-a.tvd}),r=[];if(this.timeScalePlot){if(i.filter(n=>n.time>=s&&n.time<=e).length>0){let n=i.filter(h=>h.time>=s&&h.time<=e),a=n[0],l=n[n.length-1];if(a.time>s&&i.indexOf(a)-1>=0){let h=i[i.indexOf(a)-1];h.time<s&&n.splice(0,0,h)}if(l.time<e&&i.indexOf(l)<i.length-1){let h=i[i.indexOf(l)+1];h.time>e&&n.push(h)}r=n}else for(let n=0;n<i.length-1;n++){let a=i[n],l=i[n+1];(a.time<s&&l.time>e||a.time>e&&l.time<s)&&(r.find(h=>h.time==a.time)==null&&r.push({time:a.time,y:a.y}),r.find(h=>h.time==l.time)==null&&r.push({time:l.time,y:l.y}))}for(let n=0;n<r.length;n++){let a=r[n];t(a.time,a.y)}}else{if(i.filter(n=>n.tvd>=s&&n.tvd<=e).length>0){let n=i.filter(a=>a.tvd>=s&&a.tvd<=e);for(let a=0;a>1?a<n.length-1:a<n.length;a++){let l=n[a],h=n[a+1],o=i.indexOf(l),u=i.indexOf(h);if(a==0&&l.tvd>s&&o-1>=0){let c=i[o-1];(c.tvd<s||c.tvd>e)&&r.push({tvd:c.tvd,y:c.y})}if(r.push({tvd:l.tvd,y:l.y}),o+1<u||n.length==1){let c=i[o+1];c&&(c.tvd<s||c.tvd>e)&&r.find(f=>f.tvd==c.tvd)==null&&r.push({tvd:c.tvd,y:c.y})}if(a==n.length-2&&h&&h.tvd<e&&(r.push({tvd:h.tvd,y:h.y}),u<i.length-1)){let c=i[u+1];(c.tvd<s||c.tvd>e)&&r.push({tvd:c.tvd,y:c.y})}}}else for(let n=0;n<i.length-1;n++){let a=i[n],l=i[n+1];(a.tvd<s&&l.tvd>e||a.tvd>e&&l.tvd<s)&&(r.find(h=>h.tvd==a.tvd)==null&&r.push({tvd:a.tvd,y:a.y}),r.find(h=>h.tvd==l.tvd)==null&&r.push({tvd:l.tvd,y:l.y}))}for(let n=0;n<r.length;n++){let a=r[n];t(a.tvd,a.y)}}}recalcRowFilterData(s){this.rowFilterData=[];let e=this.timeScalePlot?this.completeData.sort(function(i,r){return i.time-r.time}):this.completeData.sort(function(i,r){return i.tvd-r.tvd}),t=0;for(let i=0;i<e.length;i++)t++,t==1&&this.rowFilterData.push(e[i]),t>=s&&(t=0)}fetchByRow(s,e,t){let i=this.rowFilterData;if(i.length==0)return;let r=[];if(this.timeScalePlot){if(i.filter(n=>n.time>=s&&n.time<=e).length>0){let n=i.filter(h=>h.time>=s&&h.time<=e),a=n[0],l=n[n.length-1];if(a.time>s&&i.indexOf(a)-1>=0){let h=i[i.indexOf(a)-1];h.time<s&&n.splice(0,0,h)}if(l.time<e&&i.indexOf(l)<i.length-1){let h=i[i.indexOf(l)+1];h.time>e&&n.push(h)}r=n}else for(let n=0;n<i.length-1;n++){let a=i[n],l=i[n+1];(a.time<s&&l.time>e||a.time>e&&l.time<s)&&(r.find(h=>h.time==a.time)==null&&r.push({time:a.time,y:a.y}),r.find(h=>h.time==l.time)==null&&r.push({time:l.time,y:l.y}))}for(let n=0;n<r.length;n++){let a=r[n];t(a.time,a.y)}}else{if(i.filter(n=>n.tvd>=s&&n.tvd<=e).length>0){let n=i.filter(a=>a.tvd>=s&&a.tvd<=e);for(let a=0;a>1?a<n.length-1:a<n.length;a++){let l=n[a],h=n[a+1],o=i.indexOf(l),u=i.indexOf(h);if(a==0&&l.tvd>s&&o-1>=0){let c=i[o-1];(c.tvd<s||c.tvd>e)&&r.push({tvd:c.tvd,y:c.y})}if(r.push({tvd:l.tvd,y:l.y}),o+1<u||n.length==1){let c=i[o+1];c&&(c.tvd<s||c.tvd>e)&&r.find(f=>f.tvd==c.tvd)==null&&r.push({tvd:c.tvd,y:c.y})}if(a==n.length-2&&h&&h.tvd<e&&(r.push({tvd:h.tvd,y:h.y}),u<i.length-1)){let c=i[u+1];(c.tvd<s||c.tvd>e)&&r.push({tvd:c.tvd,y:c.y})}}}else for(let n=0;n<i.length-1;n++){let a=i[n],l=i[n+1];(a.tvd<s&&l.tvd>e||a.tvd>e&&l.tvd<s)&&(r.find(h=>h.tvd==a.tvd)==null&&r.push({tvd:a.tvd,y:a.y}),r.find(h=>h.tvd==l.tvd)==null&&r.push({tvd:l.tvd,y:l.y}))}for(let n=0;n<r.length;n++){let a=r[n];t(a.tvd,a.y)}}}recalcIndexFilterData_old(s){this.indexFilterData=[];let e=this.timeScalePlot?this.completeData.sort(function(i,r){return i.time-r.time}):this.completeData.sort(function(i,r){return i.tvd-r.tvd}),t=0;for(let i=0;i<e.length;i++)t==0&&this.indexFilterData.push(e[i]),t+=this.timeScalePlot?e[i].time:e[i].tvd,t>s&&(t=0)}recalcIndexFilterData(s){this.indexFilterData=[];let e=this.timeScalePlot?this.completeData.sort((i,r)=>i.time-r.time):this.completeData.sort((i,r)=>i.tvd-r.tvd);if(e.length===0)return;this.indexFilterData.push(e[0]);let t=e[0];for(let i=1;i<e.length;i++){const r=e[i];(this.timeScalePlot?r.time-t.time:r.tvd-t.tvd)>=s&&(this.indexFilterData.push(r),t=r)}}fetchByIndex(s,e,t){let i=this.indexFilterData;if(i.length==0)return;let r=[];if(this.timeScalePlot){if(i.filter(n=>n.time>=s&&n.time<=e).length>0){let n=i.filter(h=>h.time>=s&&h.time<=e),a=n[0],l=n[n.length-1];if(a.time>s&&i.indexOf(a)-1>=0){let h=i[i.indexOf(a)-1];h.time<s&&n.splice(0,0,h)}if(l.time<e&&i.indexOf(l)<i.length-1){let h=i[i.indexOf(l)+1];h.time>e&&n.push(h)}r=n;for(let h=0;h>1?h<n.length-1:h<n.length;h++){let o=n[h],u=n[h+1],c=i.indexOf(o),f=i.indexOf(u);if(h==0&&o.time>s&&c-1>=0){let g=i[c-1];(g.time<s||g.time>e)&&r.push({time:g.time,y:g.y})}if(r.push({time:o.time,y:o.y}),c+1<f||n.length==1){let g=i[c+1];g&&(g.time<s||g.time>e)&&r.find(m=>m.time==g.time)==null&&r.push({time:g.time,y:g.y})}if(h==n.length-2&&u&&u.time<e&&(r.push({time:u.time,y:u.y}),f<i.length-1)){let g=i[f+1];(g.time<s||g.time>e)&&r.push({time:g.time,y:g.y})}}}else for(let n=0;n<i.length-1;n++){let a=i[n],l=i[n+1];(a.time<s&&l.time>e||a.time>e&&l.time<s)&&(r.find(h=>h.time==a.time)==null&&r.push({time:a.time,y:a.y}),r.find(h=>h.time==l.time)==null&&r.push({time:l.time,y:l.y}))}for(let n=0;n<r.length;n++){let a=r[n];t(a.time,a.y)}}else{if(i.filter(n=>n.tvd>=s&&n.tvd<=e).length>0){let n=i.filter(a=>a.tvd>=s&&a.tvd<=e);for(let a=0;a>1?a<n.length-1:a<n.length;a++){let l=n[a],h=n[a+1],o=i.indexOf(l),u=i.indexOf(h);if(a==0&&l.tvd>s&&o-1>=0){let c=i[o-1];(c.tvd<s||c.tvd>e)&&r.push({tvd:c.tvd,y:c.y})}if(r.push({tvd:l.tvd,y:l.y}),o+1<u||n.length==1){let c=i[o+1];c&&(c.tvd<s||c.tvd>e)&&r.find(f=>f.tvd==c.tvd)==null&&r.push({tvd:c.tvd,y:c.y})}if(a==n.length-2&&h&&h.tvd<e&&(r.push({tvd:h.tvd,y:h.y}),u<i.length-1)){let c=i[u+1];(c.tvd<s||c.tvd>e)&&r.push({tvd:c.tvd,y:c.y})}}}else for(let n=0;n<i.length-1;n++){let a=i[n],l=i[n+1];(a.tvd<s&&l.tvd>e||a.tvd>e&&l.tvd<s)&&(r.find(h=>h.tvd==a.tvd)==null&&r.push({tvd:a.tvd,y:a.y}),r.find(h=>h.tvd==l.tvd)==null&&r.push({tvd:l.tvd,y:l.y}))}for(let n=0;n<r.length;n++){let a=r[n];t(a.tvd,a.y)}}}setSectionData(s,e,t){let i=this.timeScalePlot?this.sections.filter(r=>r.startTime>=s&&r.endTime<=e):this.sections.filter(r=>r.startIndex>=s&&r.endIndex<=e);for(let r=0;r<i.length;r++){let n=[];this.timeScalePlot?n=t.filter(a=>new Date(a.time).getTime()>=i[r].startTime&&new Date(a.time).getTime()<i[r].endTime):this.dataDirection==ie.Vertical?n=t.filter(a=>a.tvd>=i[r].startIndex&&a.tvd<i[r].endIndex):this.dataDirection==ie.Horizontal&&(n=t.filter(a=>a.vs>=i[r].startIndex&&a.vs<i[r].endIndex)),n&&n.length>0&&(this.timeScalePlot&&n.forEach(a=>{a.time=a.time!=null&&a.time!=""?new Date(a.time).getTime():null}),i[r].logData=n,i[r].dataLoaded=!0)}}}class Vt{constructor(s){d(this,"_container");d(this,"_canvas");d(this,"_ctx");d(this,"_devicePixelRatio");d(this,"_fillEffect",new Be);if(!(s.container instanceof HTMLDivElement))throw new Error("挂载容器必须为HTMLDivElement");this._container=s.container,this.initContainer(),this.initCanvas(this._container),this.update(s),this.draw()}initContainer(){Object.assign(this._container.style,{boxSizing:"border-box",inset:"0",overflow:"hidden"})}initCanvas(s){this._canvas=document.createElement("canvas"),this._ctx=this._canvas.getContext("2d"),this._devicePixelRatio=window.devicePixelRatio||1;const e=s.clientWidth,t=s.clientHeight;this._canvas.width=e*this._devicePixelRatio,this._canvas.height=t*this._devicePixelRatio,this._canvas.style.width=`${e}px`,this._canvas.style.height=`${t}px`,this._ctx&&this._ctx.scale(this._devicePixelRatio,this._devicePixelRatio),this._canvas.style.position="absolute",this._canvas.style.inset="0",this._canvas.style.background="rgba(0,0,0,1)",s.appendChild(this._canvas)}update(s){this._fillEffect.effectType=s.effectType??this._fillEffect.effectType,this._fillEffect.gradientMode=s.effectGradientMode??this._fillEffect.gradientMode,this._fillEffect.gradientTransform=s.effectGradientTransform??this._fillEffect.gradientTransform,this._fillEffect.setPictureUrl(s.pictureUrl),this._fillEffect.firstColor=s.firstColor??this._fillEffect.firstColor,this._fillEffect.fillTransparency=s.fillTransparency??this._fillEffect.fillTransparency,this._fillEffect.setScaleFactor(1)}draw(){this._ctx&&(this._ctx.save(),this._ctx.fillStyle=this._fillEffect.getFillStyle(this._ctx,0,0,this._container.clientWidth,this._container.clientHeight),this._ctx.fillRect(0,0,this._container.clientWidth,this._container.clientHeight),this._ctx.restore())}}typeof window<"u"&&(window.WebCanvasV2={TWCLogPlotV2:It,LogPrsDataContainerV2:Ft,FillEffectPreview:Vt})});
