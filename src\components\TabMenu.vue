<template>
  <div class="tabmenu">
    <el-tabs v-model="activeName">
      <el-tab-pane
        v-for="(item, index) in mergedTabData"
        :key="index"
        :label="item.label"
        :name="item.value"
      >
        <!-- <div class="pane-content">
          <div
            v-for="(v, i) in item.children"
            :key="i"
            class="content-item"
            @click="handleAction(v.value)"
          >
            {{ v.label }}
          </div>
        </div> -->
        <div class="pane-content">
          <template
            v-if="item.children?.length > 0"
            v-for="(v, i) in groupChildren(item.children)"
            :key="i"
          >
            <div class="content-item" v-if="v[0].single">
              <!-- <div @click="handleAction(v[0].value)">{{ v[0].label }}</div> -->

              <el-button :icon="v[0].icon" @click="handleAction(v[0].value)">
                {{ v[0].label }}
              </el-button>
            </div>
            <div class="content-item" v-else>
              <!-- <div
                style="margin-top: 4px"
                v-for="(group, j) in v"
                :key="j"
                @click="handleAction(group.value)"
              >
                {{ group.label }}
              </div> -->
              <el-button
                v-for="(group, j) in v"
                :key="j"
                :icon="group.icon"
                @click="handleAction(group.value)"
              >
                {{ group.label }}
              </el-button>
            </div>
            <el-divider v-if="v[0].single" direction="vertical" />
          </template>
        </div>
      </el-tab-pane>
    </el-tabs>
    <!-- <el-switch v-model="theme" @change="(val) => toggleTheme(val, $event)">
      <template #active-action>
        <el-icon><Sunny /></el-icon>
      </template>
      <template #inactive-action>
        <el-icon><Moon /></el-icon>
      </template>
    </el-switch> -->
  </div>
</template>

<script setup lang="ts">
import { Children } from "react";
import { ref, computed, onMounted } from "vue";
import { useResourceTreeStore } from "@/stores/resourceTreeStore";
const theme = ref(true);
// 添加 props 接收可见的 tabs 列表
const props = defineProps<{
  newTabs?: Array<{
    // 新增动态 tabs 参数
    label: string;
    value: string;
    types:string;
    children?: any[];
  }>;
}>();
// 修改 activeName 为计算属性
const activeName = computed({
  get() {
    // 优先显示动态 tabs 的第一个
    return (
      props.newTabs?.[0]?.value || mergedTabData.value[0]?.value || "first"
    );
  },
  set() {}, // 保持空实现避免警告
});
// 修改为合并后的 tab 数据
const mergedTabData = computed(() => {
  // 如果有newTabs，则只使用newTabs的数据
  if (props.newTabs && props.newTabs.length > 0) {
    return props.newTabs;
  }
  // 否则使用默认的tabData
  return tabData;
});
const tabData = [
  {
    label: "File",
    value: "first",
    types:"pythonFreeModule",
    children: [
      {
        label: "Create Project",
        value: "createProject",
        icon: "CirclePlus",
      },
      {
        label: "New File",
        value: "newFile",
        icon: "DocumentAdd",
      },
      {
        label: "New Folder",
        value: "newFolder",
        icon: "FolderAdd",
        single: "1",
      },
      {
        label: "Delete",
        value: "delete",
        icon: "Delete",
      },
      //{
        //label: "Refresh",
        //value: "refresh",
       // icon: "Refresh",
     // },
      {
        label: "Rename",
        value: "rename",
        icon: "Edit",
      },
      {
        label: "Add DataSet",
        value: "addDataSet",
        icon: "Coin",
      },
      {
        label: "Code Sample",
        value: "codeSample",
        icon: "Notebook",
      },
    ],
  },
  // {
  //   label: "Edit",
  //   value: "second",
  //   children: [
  //     {
  //       label: "Rename",
  //       value: "rename",
  //       icon: "Edit",
  //     },
  //   ],
  // },
  // {
  //   label: "View",
  //   value: "third",
  // },
  // {
  //   label: "Canvas",
  //   value: "fourth",
  // },
];
const toggleTheme = (val: boolean, e: Event) => {
  const themeValue = val ? "auto" : "dark";
  localStorage.setItem("theme", themeValue);
  document.documentElement.classList.toggle("dark", themeValue === "dark");
};
const handleAction = (value: string) => {
  // console.log(value);
    const store = useResourceTreeStore();
    store.callTreeAction(value);


};
// 添加分组方法
const groupChildren = (children: any[]) => {
  const groups = [];
  let currentGroup = [];
  for (const child of children) {
    if (child.single) {
      if (currentGroup.length > 0) {
        groups.push(currentGroup);
        currentGroup = [];
      }
      groups.push([child]);
    } else {
      currentGroup.push(child);
      if (currentGroup.length === 2) {
        groups.push(currentGroup);
        currentGroup = [];
      }
    }
  }
  if (currentGroup.length > 0) {
    groups.push(currentGroup);
  }
  return groups;
};
onMounted(() => {
  const savedTheme = localStorage.getItem("theme") || "auto";
  document.documentElement.classList.toggle("dark", savedTheme === "dark");
  theme.value = savedTheme === "auto";
});
</script>

<style lang="less">
.tabmenu {
  margin-bottom: 5px;
  position: relative;
  .el-tabs {
    .el-tabs__header {
      margin: 0;
      .el-tabs__nav-scroll {
        background: var(--header-tab-bg);
        .el-tabs__nav {
          margin-left: 200px;
          .el-tabs__item {
            color: var(--header-font-color);
            padding: 0 20px !important;
            height: 30px;
            font-size: 14px;
          }
          .el-tabs__item:focus-visible {
            box-shadow: none;
          }
          .is-active {
            // border-bottom: 0 !important;
            background: var(--header-tab-active-bg);
            color: var(--font-color-white);
            border: 0;
            border-radius: 10px 10px 0 0;
          }
        }
      }
    }
    .el-tabs__content {
      height: 70px;
      background: var(--header-tab-content-bg);
      .el-tab-pane {
        display: block; /* 修改为块级元素 */
        height: 100%;
        .pane-content {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          //margin-left: 50px;
          height: 100%;
          .content-item {
            font-size: 14px;
            color: var(--font-color-black);
            // line-height: 60px;
            cursor: pointer;
            margin: 0 20px;
            display: flex;
            flex-direction: column;
            align-items: baseline;
            .el-button {
              background: transparent;
              border: 0;
              height: 30px;
            }
          }
          .el-divider--vertical {
            height: 50px;
            border-left-color: var(--divider-bg);
          }
        }
      }
    }
    .el-tabs__nav-wrap:after {
      height: 0;
    }
    .el-tabs__active-bar {
      height: 0;
    }
  }
  .el-switch {
    position: absolute;
    top: 4px;
    right: 10px;
  }
}
</style>
