<template>
  <div class="tabmenu" ref="tabMenuRef">
    <el-tabs v-model="activeName" @tab-change="handleTab">
      <el-tab-pane
        v-for="(item, index) in tabData"
        :key="index"
        :label="item.label"
        :name="item.value"
      >
        <!-- <div class="pane-content">
          <div
            v-for="(v, i) in item.children"
            :key="i"
            class="content-item"
            @click="handleAction(v.value)"
          >
            {{ v.label }}
          </div>
        </div> -->
        <div class="pane-content">
          <template
            v-if="item.children?.length > 0"
            v-for="(v, i) in groupChildren(item.children)"
            :key="i"
          >
            <el-divider v-if="v[0].single" direction="vertical" />
            <div class="content-item" v-if="v[0].single">
              <el-button
                :icon="v[0].icon"
                @click="handleAction(v[0].value)"
                :class="{
                  'is-active': store.activeAction === v[0].value,
                }"
              >
                {{ v[0].label }}
              </el-button>
            </div>

            <div class="content-item" v-else>
              <el-button
                v-for="(group, j) in v"
                :key="j"
                :icon="group.icon"
                @click="handleAction(group.value)"
                :class="{
                  'is-active':
                    store.activeAction === group.value && store.isStartEdit,
                }"
              >
                {{ group.label }}
              </el-button>
            </div>
          </template>
        </div>
      </el-tab-pane>
    </el-tabs>
    <!-- <el-switch v-model="theme" @change="(val) => toggleTheme(val, $event)">
      <template #active-action>
        <el-icon><Sunny /></el-icon>
      </template>
      <template #inactive-action>
        <el-icon><Moon /></el-icon>
      </template>
    </el-switch> -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useEditCurveStore } from "@/stores/editCurveStore";
import { StartEditCurve } from "@/api/curve";
import { ElMessage } from "element-plus";
const store = useEditCurveStore();
const emit = defineEmits(["add-tab"]);
const props = defineProps({
  tabId: {
    type: String,
    default: "",
  },
});
const theme = ref(true);
// 修改 activeName 为计算属性
const activeName = ref("home");
const tabData = ref([
  {
    label: "Home",
    value: "home",
    children: [
      { label: "Curve Edit", value: "add_curveEdit", icon: "Edit" },
      {
        label: "ADM & DSHIFT",
        value: "add_admAndDshift",
        icon: "SwitchFilled",
      },
    ],
  },
]);
const curveEditTabs = {
  label: "Curve Edit",
  value: "curveEdit",
  children: [
    {
      label: "Start Edit",
      value: "startEdit",
      icon: "VideoPlay",
    },
    {
      label: "Draw Curve",
      value: 1,
      icon: "EditPen",
    },
    {
      label: "Draw By Point",
      value: 2,
      icon: "Pointer",
    },
    {
      label: "Constant Value",
      value: 3,
      icon: "Crop",
    },
    {
      label: "Vertical Shift",
      value: 4,
      icon: "Rank",
    },
    {
      label: "Smooth Curve Section",
      value: 5,
      icon: "Filter",
    },
    {
      label: "Text Edit",
      value: 6,
      icon: "Edit",
    },
    {
      label: "Save",
      value: "saveEdit",
      icon: "DocumentChecked",
    },
    {
      label: "Data Positioning",
      value: 7,
      icon: "Location",
      single: true,
    },
  ],
};
const admAndDshiftTabs = {
  label: "ADM & DSHIFT",
  value: "admAndDshift",
  children: [
    {
      label: "Add Depth Pairs",
      value: 8,
      icon: "CirclePlus",
    },
    {
      label: "Move Depth Pairs",
      value: 9,
      icon: "CirclePlus",
    },
  ],
};
const toggleTheme = (val, e) => {
  const themeValue = val ? "auto" : "dark";
  localStorage.setItem("theme", themeValue);
  document.documentElement.classList.toggle("dark", themeValue === "dark");
};
const handleAction = (value) => {
  store.setActiveAction(value);
  switch (value) {
    case 8:
      store.depthPairsInstance?.CreateDepthPair();
      break;
    case 9:
      store.depthPairsInstance?.SwitchDepthPairEditable();
      break;
    case 6:
      if (store.isStartEdit && store.curveEditInstance && props.tabId) {
        store.curveEditInstance[props.tabId].handleCurveEdit();
      }
      break;
    case "add_curveEdit":
      handleEditTabs(curveEditTabs);
      break;
    case "add_admAndDshift":
      handleEditTabs(admAndDshiftTabs);
      break;
    case "startEdit":
      handleStartEditCurve();
      break;
    case "saveEdit":
      if (store.isStartEdit) {
        handleSaveCurve();
      }
      break;
    default:
      // 可选的默认处理
      break;
  }
  // if (value === 8) {
  //   store.depthPairsInstance?.CreateDepthPair();
  // } else if (value === 9) {
  //   store.depthPairsInstance?.SwitchDepthPairEditable();
  // } else if (value === 6) {
  //   if (store.curveEditInstance && props.tabId) {
  //     store.curveEditInstance[props.tabId].handleCurveEdit();
  //   }
  // } else if (value === "add_curveEdit") {
  //   handleEditTabs(curveEditTabs);
  // } else if (value === "add_admAndDshift") {
  //   handleEditTabs(admAndDshiftTabs);
  // }
};
const handleEditTabs = (val = curveEditTabs) => {
  const exists = tabData.value.some((item) => item.value === val.value);
  if (!exists) {
    if (val.value === "curveEdit") {
      tabData.value.splice(1, 0, val);
    } else {
      tabData.value.push(val);
    }
    if (val.value === "admAndDshift") {
      emit("add-tab", "admAndDshift");
    }
  }
  activeName.value = val.value;
};
const handleStartEditCurve = () => {
  if (!store.isStartEdit) {
    //store.setIsStartEdit(true);
    const formData = new FormData();
    formData.append("id", store.curveId);
    StartEditCurve(formData).then((res) => {
      if (res.success) {
        store.setIsStartEdit(true);
      } else {
        ElMessage.error(res.message);
      }
    });
  } else {
    store.setIsStartEdit(false);
  }
};
const handleRemoveTab = (tabId) => {
  const index = tabData.value.findIndex((tab) => tab.value === tabId);
  if (index > -1) {
    tabData.value.splice(index, 1);
  }
  activeName.value = "home";
};
const handleSaveCurve = () => {
  store.curveEditInstance[props.tabId].saveCurve();
};
// 添加分组方法
const groupChildren = (children) => {
  const groups = [];
  let currentGroup = [];
  for (const child of children) {
    if (child.single) {
      if (currentGroup.length > 0) {
        groups.push(currentGroup);
        currentGroup = [];
      }
      groups.push([child]);
    } else {
      currentGroup.push(child);
      if (currentGroup.length === 2) {
        groups.push(currentGroup);
        currentGroup = [];
      }
    }
  }
  if (currentGroup.length > 0) {
    groups.push(currentGroup);
  }
  return groups;
};
const handleTab = (tab) => {
  store.setActiveAction("");
  // 添加调用逻辑
  emit("add-tab", tab);
};
defineExpose({
  setSelectedTab: (value) => {
    activeName.value = value;
  },
  handleEditTabs,
  handleRemoveTab,
});
onMounted(() => {
  const savedTheme = localStorage.getItem("theme") || "auto";
  document.documentElement.classList.toggle("dark", savedTheme === "dark");
  theme.value = savedTheme === "auto";
});
</script>

<style lang="less">
.tabmenu {
  margin-bottom: 5px;
  position: relative;
  .el-tabs {
    .el-tabs__header {
      margin: 0;
      .el-tabs__nav-scroll {
        background: var(--header-tab-bg);
        .el-tabs__nav {
          margin-left: 200px;
          .el-tabs__item {
            color: var(--header-font-color);
            padding: 0 20px !important;
            height: 30px;
            font-size: 14px;
          }
          .el-tabs__item:focus-visible {
            box-shadow: none;
          }
          .is-active {
            // border-bottom: 0 !important;
            background: var(--header-tab-active-bg);
            color: var(--font-color-white);
            border: 0;
            border-radius: 10px 10px 0 0;
          }
        }
      }
    }
    .el-tabs__content {
      height: 70px;
      background: var(--header-tab-content-bg);
      .el-tab-pane {
        display: block; /* 修改为块级元素 */
        height: 100%;
        .pane-content {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          //margin-left: 50px;
          height: 100%;
          .content-item {
            font-size: 14px;
            color: var(--font-color-black);
            // line-height: 60px;
            cursor: pointer;
            margin: 0 20px;
            display: flex;
            flex-direction: column;
            align-items: baseline;
            .el-button {
              background: transparent;
              border: 0;
              height: 30px;
              &.is-active {
                color: #409eff !important;
                font-weight: 600;
              }
            }
          }
          .el-divider--vertical {
            height: 50px;
            border-left-color: var(--divider-bg);
          }
        }
      }
    }
    .el-tabs__nav-wrap:after {
      height: 0;
    }
    .el-tabs__active-bar {
      height: 0;
    }
  }
  .el-switch {
    position: absolute;
    top: 4px;
    right: 10px;
  }
}
</style>
