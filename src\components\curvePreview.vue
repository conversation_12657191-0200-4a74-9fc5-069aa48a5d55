<template>
  <div>
    <div v-loading="loading" id="previewCurveDialog" class="previewCurveDialog">
      <!-- <select v-model="num" style="float: right; width: 150px;" @change="handleProbeChange">
        <option value="" disabled>Select Probe(default is 1)</option>
        <option v-for="(item, index) in probes" :key="index" :value="item.value">
          {{ item.label }}
        </option>
      </select> -->
      <d-splitter class="splitter-border" orientation="horizontal" splitBarSize="2px">
        <template v-slot:DSplitterPane>
          <d-splitter-pane collapseDirection="before" size="60%" minSize="200" @sizeChange="sizeChange">
            <div class="pane-content">
              <div ref="echartsContainer" id="echartLine"></div>
            </div>
          </d-splitter-pane>
          <d-splitter-pane minSize="300" size="40%">
            <div class="pane-content" @dragover.prevent @drop="onDrop">
              <s-table :columns="columns" :dataSource="dataSource" :pagination="false" :key="tableKey"
                :scroll="{ y: scrollHeight, x: scrollWidth }" ref="tableRef" bordered :rowHeight="25"
                @cellClick="handleTableSelect">
              </s-table>
            </div>
          </d-splitter-pane>
        </template>
      </d-splitter>
    </div>

    <!-- 右键菜单 -->
    <!-- <div v-if="contextMenu.visible"
      :style="{ position: 'fixed', top: `${contextMenu.y}px`, left: `${contextMenu.x}px`, zIndex: 9999 }"
      class="context-menu">
      <ul class="menu">
        <li v-if="contextMenu.column.dataIndex != 'no' && contextMenu.column.dataIndex != 'index'" @click="onMenuClick('delete')">Delete Column</li>
        <li v-if="contextMenu.column.dataIndex != 'no' && contextMenu.column.dataIndex != 'index'" @click="onMenuClick('deleteAll')">Delete All Columns</li>
        <li @click="onMenuClick('exportXlsx')">Export XLSX</li>
        <li @click="onMenuClick('exportTxt')">Export TXT</li>
        <li @click="onMenuClick('exportCsv')">Export CSV</li>
      </ul>
    </div> -->
  </div>
</template>

<script setup>
import { usePreviewStore } from "@/stores/preview.js";
import { ref, toRef, defineProps, onMounted, markRaw, watch, nextTick } from "vue";
import * as echarts from "echarts";
import {
  loadChannel, initialChannelDexie,
  loadChannelData, getCurrentChunkChannelData, getChunks, checkDexieChannelDataFinished
} from "@/utils/channelDexie.ts"
// import { loadChannelChunk } from "@/api/epaps/home/<USER>"
import '@surely-vue/table/dist/index.less';
import { hackLicenseKey } from '@skit/x.surely-vue-table';
// import Sortable from 'sortablejs'
// import * as XLSX from 'xlsx'

hackLicenseKey({ domain: [window.location.hostname] });

// 加载
const loading = ref(true)

// 分割器
// const orientation = ref('horizontal')
// const splitBarSize = '2px'
// const size = ref('50%')
// const minSize = ref('250')

function sizeChange(size) {
  curveChart.value.resize();
}

let dataChunk = 1.0 // 默认显示100%的曲线部分
let chunks = 1000 // 默认1000个数据为一个chunk，与channelDexie保持一致
// const { proxy } = getCurrentInstance();
const props = defineProps({
  projectId: {
    type: String,
    required: false
  },
  datasetId: {
    type: String,
    required: false
  },
  channelId: {
    type: String,
    required: false
  }
})

const previewStore = usePreviewStore()
const echartsContainer = ref(null)
const lineList = ref([])
const channel = ref(null)
const channelData = ref(null)
const yAxisLabelSpacing = ref(0)
const chunk = ref(0)
const sampleStart = ref(0)
const sampleEnd = ref(0)
const axis = ref(0)
const tableStart = ref(0)
const tableEnd = ref(9)
const datazoomStart = ref(90)
const datazoomEnd = ref(100)

const curveChart = ref(null)
const options = ref(null)
const num = ref(1)
const probes = ref([]);
const element = ref(0);
const dataSource = ref([])
const columns = ref([])
const tableRef = ref()
const scrollHeight = ref(478)
const scrollWidth = ref(0)

const tableKey = ref(0)
const indexList = ref([])
const indexValue = ref(0)
const initialIndex = ref({})
const activeIndex = ref({})

const queryParams = ref({
  projectId: "",
  datasetId: "",
  channelId: ""
});

const contextMenu = ref({
  visible: false,
  x: 0,
  y: 0,
  record: null,
  column: null
})

function handleRightClick(event, record, column) {
  // if (column.dataIndex != 'no' && column.dataIndex != 'index') {
  contextMenu.value = {
    visible: true,
    x: event.clientX,
    y: event.clientY,
    record: record,
    column
  }
  // }
}

function hideMenu() {
  contextMenu.value.visible = false
}

function onMenuClick(action) {
  if (action === 'delete') {
    const field = contextMenu.value.column.dataIndex
    if (!field) return

    // 如果恰好删除选中列，则清空echart
    if (element.value == Number(field.replaceAll('value', ''))) {
      element.value = -1
      options.value.series[0].data = []
      curveChart.value = markRaw(echarts.init(document.getElementById('echartLine')))
      curveChart.value.clear()
      curveChart.value.setOption(options.value)
    }
    // 1. 从 columns 中删除
    columns.value = columns.value.filter(col => col.dataIndex !== field)
    if (columns.value.length == 2) {
      // 为2意味着只剩下NO和index行，曲线数据已经全部删光
      columns.value = []
      dataSource.value = []
      indexList.value = []
      initialIndex.value = {}
      activeIndex.value = {}
      element.value = 0
      curveChart.value = markRaw(echarts.init(document.getElementById('echartLine')))
      curveChart.value.clear()
    }

    // 2. 从 dataSource 中删除每一行的对应字段
    dataSource.value = dataSource.value.map(row => {
      const newRow = { ...row }
      delete newRow[field]
      return newRow
    })

    const index = indexList.value.findIndex(item => item.dataIndex === field)
    if (index !== -1) {
      indexList.value.splice(index, 1)
    }

    let startIndex = Infinity
    let endIndex = -Infinity
    let deltIndex = Infinity
    let samples = -Infinity

    for (const item of indexList.value) {
      if (item.startIndex < startIndex) startIndex = item.startIndex
      if (item.endIndex > endIndex) endIndex = item.endIndex
      if (item.deltIndex < deltIndex) deltIndex = item.deltIndex
      if (item.samples > samples) samples = item.samples
    }
    const length = Math.max(Math.floor((endIndex - startIndex) / deltIndex) + 1, samples)

    let table = []
    // 先设置好合并后的index
    for (let i = 0; i < length; i++) {
      const index = (startIndex + i * deltIndex).toFixed(4)
      let data = {
        'key': i,
        'index': index,
      }
      table.push(data)
    }
    // 根据现有dataSource往合并后的table index里面填数据
    for (let i = 0; i < dataSource.value.length; i++) {
      const index = Math.round((dataSource.value[i]['index'] - startIndex) / deltIndex)
      for (let j = 0; j < columns.value.length - 2; j++) {
        if (dataSource.value[i][columns.value[j + 2].dataIndex]) {
          table[index][columns.value[j + 2].dataIndex] = Number(dataSource.value[i][columns.value[j + 2].dataIndex]).toFixed(4)
        }
      }
    }

    dataSource.value = table
  } else if (action === 'deleteAll') {
    element.value = 0
    columns.value = []
    dataSource.value = []
    indexList.value = []
    initialIndex.value = {}
    activeIndex.value = {}
    curveChart.value = markRaw(echarts.init(document.getElementById('echartLine')))
    curveChart.value.clear()
  } else if (action === 'exportXlsx') {
    exportTableAsXlsx(columns.value, dataSource.value)
  } else if (action === 'exportTxt') {
    exportTableAsTxt(columns.value, dataSource.value)
  } else if (action === 'exportCsv') {
    exportTableAsCSV(columns.value, dataSource.value)
  }
  scrollWidth.value = columns.value.length * 120 - 30
  tableKey.value++
  hideMenu()
}

async function onDrop(event) {
  const channelDataInfo = event.dataTransfer.getData('application/json');
  if (channelDataInfo) {
    const channelInfo = JSON.parse(channelDataInfo);
    const id = channelInfo.channelId;

    // 根据channelId得到曲线数据
    channel.value = await loadChannel(id)

    let samples = channel.value.samples
    sampleEnd.value = Math.floor(samples * dataChunk)
    await loadChannelData(id, sampleStart.value, sampleEnd.value, axis.value)
    channelData.value = getCurrentChunkChannelData()
    if (columns.value.length == 0) {
      queryParams.value.channelId = id
      getSingleCurvePreview()
    } else {
      resetTableData()
    }
  }
}

async function handleProbeChange() {
  element.value = 0
  if (num.value && axis.value != num.value - 1) {
    loading.value = true
    axis.value = num.value - 1
    await initialChannelDexie(queryParams.value.channelId)
    await loadChannelData(queryParams.value.channelId, sampleStart.value, sampleEnd.value, axis.value) // axis默认为0，读取第一个探头数据
    channelData.value = getCurrentChunkChannelData()
    setTableData()
    options.value.series[0].data = generateEchartData()
    curveChart.value = markRaw(echarts.init(document.getElementById('echartLine')))
    curveChart.value.clear()
    curveChart.value.setOption(options.value)
  }
  loading.value = false
}

async function getSingleCurvePreview() {
  dataSource.value = []
  columns.value = []
  loading.value = true

  channel.value = await loadChannel(queryParams.value.channelId)
  console.log(channel.value, 'vuelu')
  if (channel.value) {
    if (probes.value.length == 0) {
      for (let i = 0; i < channel.value.axis; i++) {
        probes.value.push({
          label: i + 1,
          value: i + 1
        })
      }
    }
    await initializePreviewData()
  }
  echartShow()
  loading.value = false
}

async function initializePreviewData() {
  chunks = getChunks() // 与channelDexie同步
  let samples = channel.value.samples
  // dataChunk = Math.min(chunks/samples, 1)
  tableStart.value = 0
  tableEnd.value = 9
  sampleStart.value = 0
  sampleEnd.value = 0
  datazoomStart.value = 100 * (1 - dataChunk)
  datazoomEnd.value = 100
  previewStore.setResize({ height: 600 })
  indexList.value = []
  await initialChannelDexie(queryParams.value.channelId)

  yAxisLabelSpacing.value = Math.ceil(dataChunk * samples / 10) //初始显示10%曲线的10个Y坐标
  sampleEnd.value = Math.floor(samples * dataChunk)
  chunk.value = Math.ceil(samples * dataChunk);

  let valueName = channel.value.name
  if (channel.value.unit) {
    valueName = valueName + "(" + channel.value.unit + ")"
  }
  let indexName = channel.value.indexType == 0 ? 'DEPTH' : 'TIME'
  if (channel.value.indexUnit) {
    indexName = indexName + "(" + channel.value.indexUnit + ")"
  }

  lineList.value = []
  lineList.value.push({
    name: indexName,
    unit: channel.value.indexUnit,
    alias: indexName
  });
  lineList.value.push({
    name: valueName,
    unit: channel.value.unit,
    alias: valueName
  })
  await loadChannelData(queryParams.value.channelId, sampleStart.value, sampleEnd.value, axis.value) // axis默认为0，读取第一个探头数据
  channelData.value = getCurrentChunkChannelData()
  setTableData()
}

function getFileName(fileExtension) {
  const now = new Date()
  const yyyy = now.getFullYear()
  const mm = String(now.getMonth() + 1).padStart(2, '0')
  const dd = String(now.getDate()).padStart(2, '0')
  const hh = String(now.getHours()).padStart(2, '0')
  const min = String(now.getMinutes()).padStart(2, '0')
  const nowStr = `${yyyy}${mm}${dd}_${hh}${min}`
  let fileName = columns.value[2].title + "_" + nowStr + fileExtension

  return fileName
}

function exportTableAsXlsx(columns, dataSource) {
  if (columns.length <= 2) {
    return
  }
  const excludeFields = ['no']

  // 1. 过滤导出的列
  const exportColumns = columns.filter(col =>
    col.dataIndex && !excludeFields.includes(col.dataIndex)
  )

  // 2. 构建 Excel 的数据（第一行为表头）
  const header = exportColumns.map(col => col.title)
  const rows = dataSource.map(row =>
    exportColumns.map(col => row[col.dataIndex])
  )

  const sheetData = [header, ...rows]

  // 3. 生成 worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(sheetData)

  // 4. 生成 workbook 并导出
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

  let fileName = getFileName(".xlsx")
  XLSX.writeFile(workbook, fileName)
}

function exportTableAsTxt(columns, dataSource) {
  let columnWidths = []
  let columnOrder = []
  let headers = []
  for (let i = 1; i < columns.length; i++) {
    columnOrder.push(columns[i].dataIndex)
    headers.push(columns[i].title)
    columnWidths.push(20)
  }
  const txtContent = formatFixedWidth(dataSource, columnWidths, columnOrder, headers);
  let fileName = getFileName(".txt")
  downloadTxtFile(fileName, txtContent);
}
function formatFixedWidth(data, columnWidths, columnOrder, headers) {
  if (!data || !data.length) return '';

  const pad = (text, width) => {
    text = text == null ? '' : text.toString();
    return text.padEnd(width, ' ');
  };

  // 表头（用 headers 而不是 columnOrder）
  const header = headers.map((label, idx) => pad(label, columnWidths[idx])).join('');

  // 行数据
  const rows = data.map(row =>
    columnOrder.map((key, idx) => pad(row[key], columnWidths[idx])).join('')
  );

  return [header, ...rows].join('\n');
}

function downloadTxtFile(filename, text) {
  const blob = new Blob([text], { type: 'text/plain;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.click();
}

function exportTableAsCSV(columns, dataSource) {
  let columnOrder = []
  let headers = []
  for (let i = 1; i < columns.length; i++) {
    columnOrder.push(columns[i].dataIndex)
    headers.push(columns[i].title)
  }
  const csv = convertToCSV(dataSource, columnOrder, headers);
  let fileName = getFileName(".csv")
  downloadCSVFile(fileName, csv);
}
function convertToCSV(data, columnOrder, headers) {
  if (!data || data.length === 0) return '';

  const escape = (val) => {
    if (val == null) return '';
    val = val.toString();
    if (val.includes(',') || val.includes('"') || val.includes('\n')) {
      val = `"${val.replace(/"/g, '""')}"`;
    }
    return val;
  };

  const headerLine = headers.join(',');
  const rows = data.map(row =>
    columnOrder.map(key => escape(row[key])).join(',')
  );

  return [headerLine, ...rows].join('\n');
}
function downloadCSVFile(filename, content) {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.click();
}

function resetTableData() {
  if (channel.value && channelData.value) {
    let valueName = channel.value.name
    if (channel.value.unit) {
      valueName = valueName + "(" + channel.value.unit + ")"
    }
    let indexName = channel.value.indexType == 0 ? 'DEPTH' : 'TIME'
    const oldIndexName = columns.value[1].title[0]
    // 若类型不同，则不做对比
    if (oldIndexName.includes('TIME') && indexName == 'DEPTH' || oldIndexName.includes('DEPTH') && indexName == 'TIME') {
      return false
    }
    const match = String(oldIndexName).match(/\(([^)]+)\)/);
    if (channel.value.indexUnit && match) {
      if (match[1].toUpperCase() != channel.value.indexUnit.toUpperCase()) {
        return false
      }
    }

    let axisDataPoints = channel.value.axisDataPoints ? channel.value.axisDataPoints : 1
    const maxDataPoints = Math.min(axisDataPoints, 30)
    let tempColumns = []
    for (let i = 0; i < maxDataPoints; i++) {
      let axisName = i == 0 ? "" : "(" + (i + 1) + ")"
      tempColumns.push({
        title: [valueName + axisName],
        dataIndex: 'value' + (indexValue.value + i),
        width: 120,
        // resizable: true,
        customCell: ({ record, column }) => ({
          onContextmenu: (event) => {
            event.preventDefault()
            handleRightClick(event, record, column)
          }
        }),
        customHeaderCell: ({ column }) => ({
          onContextmenu: (event) => {
            event.preventDefault()
            handleRightClick(event, null, { dataIndex: 'value' + (indexValue.value + i) })
          },
        }),
      })

      indexList.value.push({
        dataIndex: 'value' + (indexValue.value + i),
        startIndex: Math.min(channel.value.startIndex, channel.value.endIndex),
        endIndex: Math.max(channel.value.endIndex, channel.value.startIndex),
        deltIndex: Math.abs(channel.value.deltIndex),
        samples: channel.value.samples
      })
    }

    // 此顺序不可变更，必须在columns改变前recalcIndex
    recalcIndex(axisDataPoints, maxDataPoints)
    columns.value.push(...tempColumns)
    scrollWidth.value = columns.value.length * 120 - 30 // 第一列的序号只有90宽度

    tableKey.value++
  }
}

function recalcIndex(axisDataPoints, maxDataPoints) {
  let startIndex = Infinity
  let endIndex = -Infinity
  let deltIndex = Infinity
  let samples = -Infinity

  for (const item of indexList.value) {
    if (item.startIndex < startIndex) startIndex = item.startIndex
    if (item.endIndex > endIndex) endIndex = item.endIndex
    if (item.deltIndex < deltIndex) deltIndex = item.deltIndex
    if (item.samples > samples) samples = item.samples
  }
  const length = Math.max(Math.floor((endIndex - startIndex) / deltIndex) + 1, samples)

  activeIndex.value = {
    startIndex: startIndex,
    endIndex: endIndex,
    deltIndex: deltIndex
  }
  let table = []
  // 先设置好合并后的index
  for (let i = 0; i < length; i++) {
    const index = (startIndex + i * deltIndex).toFixed(4)
    let data = {
      'key': i,
      'index': index,
    }
    table.push(data)
  }
  // 根据现有dataSource往合并后的table index里面填数据
  for (let i = 0; i < dataSource.value.length; i++) {
    const index = Math.round((dataSource.value[i]['index'] - startIndex) / deltIndex)
    for (let j = 0; j < columns.value.length - 2; j++) {
      if (dataSource.value[i][columns.value[j + 2].dataIndex]) {
        // if (i == 8441) {
        //   console.log(i)
        //   console.log(dataSource.value[i][columns.value[j + 2].dataIndex])
        //   console.log(Number(dataSource.value[i][columns.value[j + 2].dataIndex]).toFixed(4))
        //   console.log(columns.value[j + 2].dataIndex)
        //   console.log(table[index][columns.value[j + 2].dataIndex])
        // }
        if (index < table.length) {
          table[index][columns.value[j + 2].dataIndex] = Number(dataSource.value[i][columns.value[j + 2].dataIndex]).toFixed(4)
        }
      }
    }
  }

  // 根据新曲线curveData往合并后的table index里面填数据
  for (let i = 0; i < channel.value.samples; i++) {
    const index = Math.round((channelData.value.indexData[i] - startIndex) / deltIndex)
    for (let j = 0; j < maxDataPoints; j++) {
      if (index < table.length) {
        table[index]['value' + (indexValue.value + j)] = Number(channelData.value.curveData[i * axisDataPoints + j]).toFixed(4)
      }
    }
  }

  indexValue.value += maxDataPoints
  dataSource.value = table
}

function setTableData() {
  indexValue.value = 0
  if (channel.value && channelData.value) {
    let valueName = channel.value.name
    if (channel.value.unit) {
      valueName = valueName + "(" + channel.value.unit + ")"
    }
    let indexName = channel.value.indexType == 0 ? 'DEPTH' : 'TIME'
    if (channel.value.indexUnit) {
      indexName = indexName + "(" + channel.value.indexUnit + ")"
    }

    let axisDataPoints = channel.value.axisDataPoints ? channel.value.axisDataPoints : 1
    let tempColumns = []
    tempColumns.push({
      title: 'NO.',
      dataIndex: 'no',
      type: 'no',
      width: 90,
      resizable: true,
      align: 'center',
      customRender: ({ index }) => index + 1,
      customCell: ({ record, column }) => ({
        onContextmenu: (event) => {
          event.preventDefault()
          handleRightClick(event, record, column)
        },
        style: {
          backgroundColor: '#f5f5f5' // 你想要的背景色
        }
      }),
      customHeaderCell: ({ column }) => ({
        onContextmenu: (event) => {
          event.preventDefault()
          handleRightClick(event, null, { dataIndex: 'no' })
        },
      }),
    })
    tempColumns.push({
      title: [indexName],
      dataIndex: 'index',
      width: 150,
      resizable: true,
      customCell: ({ record, column }) => ({
        onContextmenu: (event) => {
          event.preventDefault()
          handleRightClick(event, record, column)
        }
      }),
      customHeaderCell: ({ column }) => ({
        onContextmenu: (event) => {
          event.preventDefault()
          handleRightClick(event, null, { dataIndex: 'index' })
        },
      }),
    })
    const maxDataPoints = Math.min(axisDataPoints, 30)
    indexValue.value += maxDataPoints
    for (let i = 0; i < maxDataPoints; i++) {
      let axisName = i == 0 ? "" : "(" + (i + 1) + ")"
      tempColumns.push({
        title: [valueName + axisName],
        dataIndex: 'value' + i,
        width: 150,
        resizable: true,
        customCell: ({ record, column }) => ({
          onContextmenu: (event) => {
            event.preventDefault()
            handleRightClick(event, record, column)
          }
        }),
        customHeaderCell: ({ column }) => ({
          onContextmenu: (event) => {
            event.preventDefault()
            handleRightClick(event, null, { dataIndex: 'value' + i })
          },
        }),
      })

      indexList.value.push({
        dataIndex: 'value' + i,
        startIndex: Math.min(channel.value.startIndex, channel.value.endIndex),
        endIndex: Math.max(channel.value.startIndex, channel.value.endIndex),
        deltIndex: Math.abs(channel.value.deltIndex),
        samples: channel.value.samples
      })
    }

    initialIndex.value = {
      startIndex: channel.value.startIndex,
      endIndex: channel.value.endIndex,
      deltIndex: Math.abs(channel.value.deltIndex),
      samples: channel.value.samples
    }
    activeIndex.value = {
      startIndex: channel.value.startIndex,
      endIndex: channel.value.endIndex,
      deltIndex: Math.abs(channel.value.deltIndex),
      samples: channel.value.samples
    }
    scrollWidth.value = tempColumns.length * 120 - 30
    columns.value = tempColumns

    let table = []
    for (let i = 0; i < channel.value.samples; i++) {
      let data = {
        'key': i,
        'index': Number(channelData.value.indexData[i]).toFixed(4),
      }
      for (let j = 0; j < maxDataPoints; j++) {
        data['value' + j] = Number(channelData.value.curveData[i * axisDataPoints + j]).toFixed(4)
      }
      table.push(data)
    }

    // 检查 index 是否递减排序，如果是，则反转 table
    // if (
    //   table.length >= 2 &&
    //   table[0].index > table[1].index
    // ) {
    //   table.reverse()
    // }
    dataSource.value = table
  }
}

function echartShow() {
  curveChart.value = markRaw(echarts.init(document.getElementById('echartLine')))
  curveChart.value.clear()

  options.value = {
    tooltip: {
      trigger: 'axis',
      show: true, // 必须为 true 才能激活 axisPointer
      formatter: () => '', // 不显示内容
      axisPointer: {
        type: 'line', // 或 'shadow'
        lineStyle: {
          color: '#999',
          type: 'dashed',
        }
      }
    },
    grid: {
      left: "15px",
      right: "15px",
      bottom: "40px",
      top: "0%",
      containLabel: true,
    },
    yAxis: {
      type: "category",
      boundaryGap: false,
      data: channelData.value.indexData.slice().reverse(),
      scale: true,
      axisLabel: {
        formatter: function (value) {
          return parseInt(value) || ""
        },
        interval: function (arg0, arg1) {
          return arg0 % yAxisLabelSpacing.value === 0;
        }
      }
    },
    xAxis: {
      type: "value",
      scale: true
    },
    dataZoom: [
    {
      type: 'inside', // 内置型数据区域缩放组件
      orient: 'vertical', // 由于你的图表是垂直的（Y轴是分类轴）
      yAxisIndex: 0, // 控制Y轴缩放
      start: 0,
      end: 100,
      zoomOnMouseWheel: true, // 开启鼠标滚轮缩放
      preventDefaultMouseMove: false
    },
     // 滑动条缩放（可视化控件）
    {
      type: 'slider',
      orient: 'vertical',
      yAxisIndex: 0,
      start: 0,
      end: 100,
      width: 20,
      right: 5
    }
  ],
    animation: false,
    series: [{
      name: channel.value.alias,
      type: "line",
      sampling: function (frame) {
        let value = 0
        let valueCount = 0
        if ((frame[0] == -999 || frame[0] == -9999 || frame[0] == -999.25) &&
          (frame[frame.length - 1] == -999.00 || frame[frame.length - 1] == -9999 || frame[frame.length - 1] == -999.25)) {
          return '-'
        } else {
          for (let i = 0; i < frame.length - 1; i++) {
            if (frame[i] != -999 && frame[i] != -9999 && frame[i] != -999.25) {
              value += frame[i]
              valueCount++
            }
          }
        }

        if (valueCount > 0) {
          return value / valueCount
        } else {
          return '-'
        }
      },
      large: true,
      data: generateEchartData(),
      // 开启渐进式渲染
      progressive: 500,
      // 渲染阈值，大于此值则启动渐进渲染
      progressiveThreshold: 1000,
    }]
  };
  curveChart.value.setOption(options.value);
  curveChart.value.resize({ height: 580 });

  // curveChart.value.getZr().off('mousedown');
  //点击图表中的点，表格定位到相关数据
  curveChart.value.getZr().on('click', params => {
    const pointInPixel = [params.offsetX, params.offsetY];
    if (curveChart.value.containPixel('grid', pointInPixel)) {
      let xIndex = curveChart.value.convertFromPixel({ seriesIndex: 0 }, [params.offsetX, params.offsetY])[1];
      const deltIndex = Math.abs((activeIndex.value.endIndex - activeIndex.value.startIndex) / activeIndex.value.samples)
      const depth = initialIndex.value.endIndex - xIndex * deltIndex
      const rowIndex = Math.min(Math.floor((depth - activeIndex.value.startIndex) / deltIndex), activeIndex.value.samples - 1)
      tableRef.value.scrollTo({ rowKey: rowIndex }, 'auto')
    }
  });
};

function sanitizeSeriesData(data) {
  return data.map(val => {
    return (val == -999 || val == -9999 || val == -999.25) ? null : Number(val)
  })
}

function handleTableSelect(event, cellArgs) {
  if (cellArgs.column.dataIndex != 'index' && cellArgs.column.dataIndex != 'no') {
    let selectedElement = Number(cellArgs.column.dataIndex.replaceAll('value', ''))
    if (element.value != selectedElement) {
      element.value = selectedElement
      options.value.series[0].data = generateEchartData()
      curveChart.value = markRaw(echarts.init(document.getElementById('echartLine')))
      curveChart.value.clear()
      curveChart.value.setOption(options.value)
    }
  }
}

function generateEchartData() {
  let curve = []
  for (let i = 0; i < dataSource.value.length; i++) {
    if (dataSource.value[i]['value' + element.value]) {
      curve.push(dataSource.value[i]['value' + element.value])
    }
  }

  // for (let i = 0; i < channel.value.samples; i++) {
  //   curve.push(channelData.value.curveData[i * channel.value.axisDataPoints + element.value])
  // }
  return sanitizeSeriesData(curve).slice().reverse()
}

watch(
  () => previewStore.resize,
  (newVal, oldVal) => {
    if (curveChart.value) {
      curveChart.value.resize({ height: newVal.height - 20 })
      let pieces = (previewStore.resize.height) / 45
      scrollHeight.value = previewStore.resize.height - 122
      yAxisLabelSpacing.value = Math.ceil(channel.value.samples * (datazoomEnd.value - datazoomStart.value) / 100 / pieces)
    }
  }
);

watch(
  () => previewStore.view,
  (newVal, oldVal) => {
    if (newVal == false) {
      curveChart.value.dispose()
      previewStore.setResize({ height: 600 })
      loading.value = false
    } else {
      num.value = 1
      axis.value = 0
      element.value = 0
      probes.value = []
      queryParams.value.channelId = previewStore.channelId
      console.log('开始获取数据')
      getSingleCurvePreview()
    }
  },
  { deep: true }
)

onMounted(() => {
  document.addEventListener('click', hideMenu, true)
  const dialog = document.getElementById('previewCurveDialog');
  dialog.addEventListener('contextmenu', function (event) {
    event.preventDefault(); // 仅屏蔽 dialog 上的右键菜单
  });
  nextTick(() => {
    setupColumnSortable()
  })
});

watch(columns, () => {
  setTimeout(() => {
    setupColumnSortable()
  }, 50) // 延迟 50ms 确保 DOM 完成
}, { deep: true })

let sortableInstance = null
function setupColumnSortable() {
  const headerRow = document.querySelector('.surely-table-center-container')
  if (!headerRow) return

  // 销毁旧的 sortableInstance
  if (sortableInstance) {
    sortableInstance.destroy()
    sortableInstance = null
  }

//   sortableInstance = Sortable.create(headerRow, {
//     animation: 150,
//     onEnd: (evt) => {
//       const oldIndex = evt.oldIndex
//       const newIndex = evt.newIndex
//       // 禁止前两列被拖动或拖入前两列
//       if (oldIndex < 2 || newIndex < 2) return

//       // 重新排序列定义
//       const moved = columns.value.splice(oldIndex, 1)[0]
//       columns.value.splice(newIndex, 0, moved)

//       // 可选：强制刷新 s-table（防止卡顿）
//       tableKey.value++
//     },
//   })
}
</script>

<style>
.content-panel-item {
  border-right: 0px !important;
  border-left: 0px !important;
  border-top: 0px !important;
  border-bottom: 0px !important;
}

::-webkit-scrollbar {
  width: 10px !important;
}

.context-menu {
  position: fixed;
  z-index: 9999;
}

.context-menu .menu {
  background-color: white;
  border: 1px solid #ccc;
  padding: 4px 0;
  list-style: none;
  width: 140px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  margin: 0;
}

.context-menu .menu li {
  padding: 6px 12px;
  cursor: pointer;
}

.context-menu .menu li:hover {
  background-color: #eee;
}

.context-cell-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  padding: 0 8px;
  box-sizing: border-box;
  cursor: context-menu;
}
.previewCurveDialog {
  border-radius: 4px;
  border: 1px solid #ebebeb;
}
</style>