/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Collapse: typeof import('./src/components/Collapse.vue')['default']
    ContextMenu: typeof import('./src/components/ContextMenu.vue')['default']
    copy: typeof import('./src/components/TabMenu copy.vue')['default']
    CurvePreview: typeof import('./src/components/curvePreview.vue')['default']
    CurvePreviewDialog: typeof import('./src/components/curvePreviewDialog.vue')['default']
    DockLayout: typeof import('./src/components/DockLayout.vue')['default']
    Icon: typeof import('./src/components/Icon.vue')['default']
    IMGoutputResult: typeof import('./src/components/IMGoutputResult.vue')['default']
    MonacoEditor: typeof import('./src/components/MonacoEditor.vue')['default']
    OutputResult: typeof import('./src/components/OutputResult.vue')['default']
    Property: typeof import('./src/components/property.vue')['default']
    ResourceTree: typeof import('./src/components/ResourceTree.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TabMenu: typeof import('./src/components/TabMenu.vue')['default']
    'TabMenu copy': typeof import('./src/components/TabMenu copy.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanButtonGroup: typeof import('vant/es')['ButtonGroup']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanCollapse: typeof import('vant/es')['Collapse']
    VanCollapseItem: typeof import('vant/es')['CollapseItem']
    VanDialog: typeof import('vant/es')['Dialog']
    VanDropdownItem: typeof import('vant/es')['DropdownItem']
    VanDropdownMenu: typeof import('vant/es')['DropdownMenu']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanIcon: typeof import('vant/es')['Icon']
    VanList: typeof import('vant/es')['List']
    VanLoading: typeof import('vant/es')['Loading']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanSearch: typeof import('vant/es')['Search']
    VanTab: typeof import('vant/es')['Tab']
    VanTabbar: typeof import('vant/es')['Tabbar']
    VanTabbarItem: typeof import('vant/es')['TabbarItem']
    VanTable: typeof import('vant/es')['Table']
    VanTableColumn: typeof import('vant/es')['TableColumn']
    VanTabs: typeof import('vant/es')['Tabs']
    VanTag: typeof import('vant/es')['Tag']
    VantIcon: typeof import('vant/es')['tIcon']
  }
}
