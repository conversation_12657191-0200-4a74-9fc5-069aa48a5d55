<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { GLTFExporter } from 'three/examples/jsm/exporters/GLTFExporter.js';

// 定义网格信息接口，用于存储地形数据
export interface GridInfo {
    cols: number;        // 列数
    rows: number;        // 行数
    xMin: number;        // X轴最小值
    xMax: number;        // X轴最大值
    yMin: number;        // Y轴最小值
    yMax: number;        // Y轴最大值
    nullValue: number;   // 无效值标识
    rotation: number;    // 旋转角度
    xStep: number;       // X轴步长
    yStep: number;       // Y轴步长
    data: number[][];    // 高程数据数组
}

// 场景相关变量
const scene = new THREE.Scene();
scene.background = new THREE.Color(0x000000);

const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 10000);
camera.position.set(0, 0, 500);

let renderer: THREE.WebGLRenderer;
let controls: any; // OrbitControls 类型
let currentMesh: THREE.Mesh | null = null;

// 响应式状态
const heightScaleFactor = ref(0.2);
const viewMode = ref('3d');
const statsInfo = ref('');
const infoText = ref('ZMAP 文件 3D 可视化');
const props = defineProps({
  filePath: {
    type: String,
    default: ''
  }
});

// 高度标签
const maxHeightLabel = ref('最高: 0');
const height80Label = ref('80%: 0');
const height60Label = ref('60%: 0');
const height40Label = ref('40%: 0');
const height20Label = ref('20%: 0');
const minHeightLabel = ref('最低: 0');





// 初始化 Three.js 场景
function initThreeJS() {
    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    document.getElementById('threeContainer')?.appendChild(renderer.domElement);

    // 创建控制器
    controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;

    // 添加光源
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.7);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    // 开始动画循环
    animate();

    // 窗口大小调整处理
    window.addEventListener('resize', onWindowResize);
}

// 窗口大小调整处理函数
function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}


function animate() {
    requestAnimationFrame(animate);
    controls.update();

    // 动态调整等高线宽度：根据相机与模型中心的距离，按比例更新 uniform 值
    if (currentMesh) {
        const distance = camera.position.distanceTo(currentMesh.position);
        const refDistance = (currentMesh.userData.refDistance as number) || 1;
        const baseContourWidth = (currentMesh.userData.baseContourWidth as number) || 1;
        // 根据距离动态调整等高线宽度
        (currentMesh.material as THREE.ShaderMaterial).uniforms.contourWidth.value =
            baseContourWidth * (distance / refDistance) * 2.5;  // 添加一个缩放因子0.05来微调
    }

    renderer.render(scene, camera);
}

// ZMAP 文件解析函数
export function parseZMAPFile(content: string): GridInfo {
    const lines = content.split('\n');
    const cleanLines = lines.map(line => line.trim());

    let headerEndIndex: number | null = null;
    for (let i = 0; i < cleanLines.length; i++) {
        if (cleanLines[i] === '@') {
            headerEndIndex = i;
            break;
        }
    }
    if (headerEndIndex === null) {
        throw new Error("未找到 header 结束标记 '@'");
    }

    let gridInfoLine = '';
    let gridStepLine = '';
    let gridNullValueLine = '';
    for (let i = 0; i < headerEndIndex; i++) {
        if ((cleanLines[i] as string).startsWith('@')) {
            gridNullValueLine = cleanLines[i + 1];
            gridInfoLine = cleanLines[i + 2];
            gridStepLine = cleanLines[i + 3];
            break;
        }
    }
    if (!gridInfoLine) {
        throw new Error("未找到网格信息行");
    }
    if (!gridStepLine) {
        throw new Error("未找到网格间距信息行");
    }

    const gridNullValueInfoParts = gridNullValueLine.split(',').map(s => s.trim()).filter(s => s !== '');
    const gridInfoParts = gridInfoLine.split(',').map(s => s.trim()).filter(s => s !== '');
    const gridStepParts = gridStepLine.split(',').map(s => s.trim()).filter(s => s !== '');
    if (gridInfoParts.length < 6) {
        throw new Error("网格信息不足，请检查文件格式！");
    }

    const gridInfo: GridInfo = {
        cols: parseInt(gridInfoParts[0]),
        rows: parseInt(gridInfoParts[1]),
        xMin: parseFloat(gridInfoParts[2]),
        xMax: parseFloat(gridInfoParts[3]),
        yMin: parseFloat(gridInfoParts[4]),
        yMax: parseFloat(gridInfoParts[5]),
        nullValue: 0.1e31,
        rotation: 0,
        xStep: 0,
        yStep: 0,
        data: []
    };

    //这种是自动计算
    // gridInfo.xStep = (gridInfo.xMax - gridInfo.xMin) / (gridInfo.cols - 1);
    // gridInfo.yStep = (gridInfo.yMax - gridInfo.yMin) / (gridInfo.rows - 1);

    //这种是文件提取
    gridInfo.xStep = parseFloat(gridStepParts[1]);
    gridInfo.yStep = parseFloat(gridStepParts[2]);

    //缺省值
    gridInfo.nullValue = parseFloat(gridNullValueInfoParts[1] == "" ? gridNullValueInfoParts[2] : gridNullValueInfoParts[1]);

    const dataValues: number[] = [];
    for (let i = headerEndIndex + 1; i < cleanLines.length; i++) {
        const line = cleanLines[i];
        if (line === '') continue;
        const parts = line.split(/\s+/).filter(part => part !== '');
        for (const part of parts) {
            try {

                const value = parseFloat(part);

                dataValues.push(value);




            } catch (e) {
                console.warn("无法转换数据:", part);
            }
        }
    }

    const expectedCount = gridInfo.rows * gridInfo.cols;
    const actualCount = dataValues.length;
    console.log(`预期数据数量: ${expectedCount}, 实际数据数量: ${actualCount}`);
    if (actualCount !== expectedCount) {
        throw new Error(`数据数量与预期不符，预期 ${expectedCount}，实际 ${actualCount}`);
    }

    for (let r = 0; r < gridInfo.rows; r++) {
        gridInfo.data[r] = [];
        for (let c = 0; c < gridInfo.cols; c++) {
            const index = r * gridInfo.cols + c;
            gridInfo.data[r][c] = dataValues[index];
        }
    }

    return gridInfo;

}

//CPS3文件解析函数
export function parseCPS3File(content: string): GridInfo {
    const lines = content.split('\n');
    const cleanLines = lines.map(line => line.trim());

    const gridNullValueLine = cleanLines[0];
    const gridInfoLine = cleanLines[2];
    const gridRowAndColInfoLine = cleanLines[3];
    const gridStepLine = cleanLines[4];

    if (!gridInfoLine) {
        throw new Error("未找到网格信息行");
    }
    if (!gridStepLine) {
        throw new Error("未找到网格间距信息行");
    }
    if (!gridRowAndColInfoLine) {
        throw new Error("未找到网格行列信息行");
    }
    if (!gridNullValueLine) {
        throw new Error("未找到网格缺省值信息行");
    }

    const gridNullValueInfoParts = gridNullValueLine.split(/\s+/);
    const gridInfoParts = gridInfoLine.split(/\s+/);
    const gridRowAndColInfoParts = gridRowAndColInfoLine.split(/\s+/);
    const gridStepParts = gridStepLine.split(/\s+/);

    const gridInfo: GridInfo = {
        cols: parseInt(gridRowAndColInfoParts[1]),
        rows: parseInt(gridRowAndColInfoParts[2]),
        xMin: parseFloat(gridInfoParts[1]),
        xMax: parseFloat(gridInfoParts[2]),
        yMin: parseFloat(gridInfoParts[3]),
        yMax: parseFloat(gridInfoParts[4]),
        nullValue: parseFloat(gridNullValueInfoParts[5]),
        rotation: 0,
        xStep: 0,
        yStep: 0,
        data: []
    };

    gridInfo.xStep = parseFloat(gridStepParts[1]);
    gridInfo.yStep = parseFloat(gridStepParts[2]);

    const dataValues: number[] = [];
    for (let i = 6; i < cleanLines.length; i++) {
        const line = cleanLines[i];
        if (line === '') continue;
        const parts = line.split(/\s+/).filter(part => part !== '');
        for (const part of parts) {
            try {
                const value = parseFloat(part);
                dataValues.push(value);
            } catch (e) {
                console.warn("无法转换数据:", part);
            }
        }
    }

    const expectedCount = gridInfo.rows * gridInfo.cols;
    const actualCount = dataValues.length;
    console.log(`预期数据数量: ${expectedCount}, 实际数据数量: ${actualCount}`);
    if (actualCount !== expectedCount) {
        throw new Error(`数据数量与预期不符，预期 ${expectedCount}，实际 ${actualCount}`);
    }

    for (let r = 0; r < gridInfo.rows; r++) {
        gridInfo.data[r] = [];
        for (let c = 0; c < gridInfo.cols; c++) {
            const index = r * gridInfo.cols + c;
            gridInfo.data[r][c] = dataValues[index];
        }
    }

    return gridInfo;

}

// 创建3D地形网格
export function createTerrainMesh(gridInfo: GridInfo): THREE.Mesh {
    // 计算地形尺寸
    const height = gridInfo.xMax - gridInfo.xMin;
    const width = gridInfo.yMax - gridInfo.yMin;

    // 创建平面几何体
    const geometry = new THREE.PlaneGeometry(
        width,
        height,
        gridInfo.cols - 1,
        gridInfo.rows - 1
    );

    // 处理顶点数据
    const vertices = geometry.attributes.position.array;
    let minHeight = Infinity;
    let maxHeight = -Infinity;

    // 计算高度范围
    for (let r = 0; r < gridInfo.rows; r++) {
        for (let c = 0; c < gridInfo.cols; c++) {
            const value = gridInfo.data[r][c];
            if (value !== null && !isNaN(value) && Math.abs(value) < gridInfo.nullValue) {
                minHeight = Math.min(minHeight, value);
                maxHeight = Math.max(maxHeight, value);
            }
        }
    }

    // 更新统计信息
    statsInfo.value = `
    网格大小: ${gridInfo.rows} × ${gridInfo.cols}<br>
    X范围: ${gridInfo.xMin.toFixed(2)} - ${gridInfo.xMax.toFixed(2)}<br>
    Y范围: ${gridInfo.yMin.toFixed(2)} - ${gridInfo.yMax.toFixed(2)}<br>
    高度范围: ${minHeight.toFixed(2)} - ${maxHeight.toFixed(2)}<br>
    网格间距: ${gridInfo.xStep} × ${gridInfo.yStep}
    `;

    // 计算高度缩放
    const heightScale = Math.max(width, height) / (maxHeight - minHeight) * heightScaleFactor.value;
    const baseHeight = 0;

    // 创建索引数组存储有效三角形
    const indexArray = [];
    const validVertices = new Set();

    // 设置顶点高度并记录有效顶点
    for (let i = 0, j = 0; i < vertices.length; i += 3, j++) {
        const col = j % (gridInfo.cols);
        const row = Math.floor(j / (gridInfo.cols));
        const invertedRow = gridInfo.rows - 1 - row;

        if (invertedRow >= 0 && invertedRow < gridInfo.rows && col >= 0 && col < gridInfo.cols) {
            const value = gridInfo.data[invertedRow][col];
            if (value !== null && !isNaN(value) && Math.abs(value) < gridInfo.nullValue) {
                vertices[i + 2] = (value - minHeight) * heightScale + baseHeight;
                validVertices.add(j);
            } else {
                vertices[i + 2] = baseHeight;
            }
        }
    }

    // 创建三角形索引（只包含有效顶点）
    for (let row = 0; row < gridInfo.rows - 1; row++) {
        for (let col = 0; col < gridInfo.cols - 1; col++) {
            const a = row * gridInfo.cols + col;
            const b = a + 1;
            const c = a + gridInfo.cols;
            const d = c + 1;

            // 只有当相关顶点都有效时才创建三角形
            if (validVertices.has(a) && validVertices.has(b) && validVertices.has(c)) {
                indexArray.push(a, b, c);
            }
            if (validVertices.has(b) && validVertices.has(c) && validVertices.has(d)) {
                indexArray.push(b, d, c);
            }
        }
    }

    // 更新几何体
    geometry.setIndex(indexArray);
    geometry.attributes.position.needsUpdate = true;
    geometry.computeVertexNormals();

    // 设置等高线参数
    const contourInterval = (maxHeight - minHeight) / 20;
    const baseContourWidth = 0.2;

    // 创建着色器材质
    const material = new THREE.ShaderMaterial({
        uniforms: {
            contourInterval: { value: contourInterval },
            contourWidth: { value: baseContourWidth },
            minHeight: { value: minHeight },
            maxHeight: { value: maxHeight },
            heightScale: { value: heightScale },
            baseHeight: { value: baseHeight }
        },
        // 顶点着色器：计算高度值并传递给片元着色器
        vertexShader: `
            varying float vHeight;
            varying vec4 vPosition;
            uniform float minHeight;
            uniform float heightScale;
            uniform float baseHeight;
            
            void main() {
                vHeight = (position.z - baseHeight) / heightScale + minHeight;
                vPosition = modelViewMatrix * vec4(position, 1.0);
                gl_Position = projectionMatrix * vPosition;
            }
        `,
        // 片元着色器：根据高度值计算颜色和等高线
        fragmentShader: `
            uniform float contourInterval;
            uniform float contourWidth;
            uniform float minHeight;
            uniform float maxHeight;
            varying float vHeight;
            varying vec4 vPosition;
            
            void main() {
                // 计算标准化高度
                float normalizedHeight = (vHeight - minHeight) / (maxHeight - minHeight);
                float modHeight = mod(vHeight, contourInterval);
                
                // 计算等高线
                float distanceToContour = min(modHeight, contourInterval - modHeight);
                float contourFactor = smoothstep(0.0, contourWidth, distanceToContour);
                
                // 根据高度计算颜色
                vec3 color;
                if (normalizedHeight == 0.0) {                      
                    color = vec3(0.0, 0.0, 0.0);
                }
                else if (normalizedHeight < 0.2) {                      
                    color = vec3(0.0, normalizedHeight * 2.5, 0.5 + normalizedHeight * 2.5);
                } else if (normalizedHeight < 0.4) {
                    color = vec3(0.0, 0.5 + (normalizedHeight - 0.2) * 2.5, 1.0 - (normalizedHeight - 0.2) * 2.5);
                } else if (normalizedHeight < 0.6) {
                    color = vec3((normalizedHeight - 0.4) * 5.0, 1.0, 0.0);
                } else if (normalizedHeight < 0.8) {
                    color = vec3(1.0, 1.0 - (normalizedHeight - 0.6) * 2.5, 0.0);
                } else {
                    color = vec3(1.0, 0.5 + (normalizedHeight - 0.8) * 2.5, (normalizedHeight - 0.8) * 5.0);
                }
                
                // 混合等高线和地形颜色
                vec3 finalColor = mix(vec3(0.0, 0.0, 0.0), color, contourFactor);
                gl_FragColor = vec4(finalColor, 1.0);
            }
        `,
        side: THREE.DoubleSide
    });

    // 创建网格并设置位置
    const mesh = new THREE.Mesh(geometry, material);
    mesh.rotation.x = -Math.PI / 2;
    mesh.position.set(0, 0, 0);
    mesh.userData.baseContourWidth = baseContourWidth;

    return mesh;
}

// 处理文件数据
function processFileData(content: string, extension: string) {
    try {
        let gridInfo = null;
        if (extension == "zmap" || extension == "grd") {
            gridInfo = parseZMAPFile(content);
        }
        else if (extension == "") {
            gridInfo = parseCPS3File(content);
        }
        else {
            alert("文件格式不支持");
            return;
        }

        if (currentMesh) {
            scene.remove(currentMesh as unknown as THREE.Object3D);
            currentMesh.geometry.dispose();

            // 使用可选链 + 类型断言
            (currentMesh.material as THREE.Material)?.dispose();
        }

        // 创建新的地形网格
        currentMesh = createTerrainMesh(gridInfo);
        scene.add(currentMesh as unknown as THREE.Object3D);

        const width = gridInfo.xMax - gridInfo.xMin;
        const height = gridInfo.yMax - gridInfo.yMin;
        const maxDim = Math.max(width, height);

        // 保存一个参考距离到当前网格，后续用于动态调整等高线宽度
        currentMesh.userData.refDistance = maxDim;

        // 根据视图模式设置相机位置
        if (viewMode.value === 'top') {
            camera.position.set(0, 0, maxDim * 1.5);
            camera.lookAt(0, 0, 0);

        } else {
            camera.position.set(maxDim * 0.8, maxDim * 0.8, maxDim * 0.8);
            camera.lookAt(0, 0, 0);
        }
        camera.near = 0.1;
        camera.far = maxDim * 10;
        camera.updateProjectionMatrix();

        controls.target.set(0, 0, 0);
        controls.update();

        infoText.value = '已加载: ' + (gridInfo.rows * gridInfo.cols) + ' 个数据点';

        // 更新颜色卡标签
        let minHeight = Infinity;
        let maxHeight = -Infinity;

        for (let r = 0; r < gridInfo.rows; r++) {
            for (let c = 0; c < gridInfo.cols; c++) {
                const value = gridInfo.data[r][c];
                if (value !== null && !isNaN(value) && Math.abs(value) < gridInfo.nullValue) {
                    minHeight = Math.min(minHeight, value);
                    maxHeight = Math.max(maxHeight, value);
                }
            }
        }

        const heightRange = maxHeight - minHeight;
        maxHeightLabel.value = `最高: ${maxHeight.toFixed(1)}`;
        height80Label.value = `80%: ${(minHeight + heightRange * 0.8).toFixed(1)}`;
        height60Label.value = `60%: ${(minHeight + heightRange * 0.6).toFixed(1)}`;
        height40Label.value = `40%: ${(minHeight + heightRange * 0.4).toFixed(1)}`;
        height20Label.value = `20%: ${(minHeight + heightRange * 0.2).toFixed(1)}`;
        minHeightLabel.value = `最低: ${minHeight.toFixed(1)}`;

    } catch (error: any) {
        console.error('处理文件数据失败:', error);
        alert('处理文件数据失败: ' + error.message);
    }
}



// 处理文件上传
function handleFileUpload(event: Event) {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function (e) {
        const content = e.target?.result as string;
        const extension = getFileExtension(file.name);
        processFileData(content, extension);
    };
    reader.readAsText(file);
}


function getFileExtension(filename: string): string {
    const match = filename.match(/\.([^.]+)$/);
    return match ? match[1] : '';
}

// 监听高度缩放变化
watch(heightScaleFactor, () => {
    const fileInput = document.getElementById('zmapFile') as HTMLInputElement;
    if (currentMesh) {
        if (fileInput.files?.[0]) {
            const file = fileInput.files[0];
            const reader = new FileReader();
            reader.onload = function (e) {
                const content = e.target?.result as string;
                const extension = getFileExtension(file.name);
                processFileData(content, extension);
            };
            reader.readAsText(file);
        } else {
            //loadDefaultData();
        }
    }
});

// 监听视图模式变化
watch(viewMode, () => {
    if (!currentMesh) return;

    const width = (currentMesh.geometry as THREE.PlaneGeometry).parameters.width || 1000;
    const height = (currentMesh.geometry as THREE.PlaneGeometry).parameters.height || 1000;
    const maxDim = Math.max(width, height);

    if (viewMode.value === 'top') {
        camera.position.set(0, 0, maxDim * 1.5);
        camera.lookAt(0, 0, 0);
    } else {
        camera.position.set(maxDim * 0.8, maxDim * 0.8, maxDim * 0.8);
        camera.lookAt(0, 0, 0);
    }
    controls.update();
});

// 添加文件下载函数
async function downloadAndProcessFile(path: string) {
    try {
        const response = await fetch(path);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const content = await response.text();
        const extension = getFileExtension(path);
        processFileData(content, extension);
    } catch (error: any) {
        console.error('下载或处理文件失败:', error);
        alert('下载或处理文件失败: ' + error.message);
    }
}

// 监听文件路径变化
watch(() => props.filePath, (newPath) => {
    if (newPath) {
        downloadAndProcessFile(newPath);
    }
});

// 组件挂载时初始化
onMounted(() => {
    initThreeJS();
});

// 组件卸载时清理
onUnmounted(() => {
    window.removeEventListener('resize', onWindowResize);
    if (renderer) {
        renderer.dispose();
    }
    if (currentMesh) {
        scene.remove(currentMesh as unknown as THREE.Object3D);
        currentMesh.geometry.dispose();

        // 使用可选链 + 类型断言
        (currentMesh.material as THREE.Material)?.dispose();
    }
});
</script>

<template>
    <header>
        <div class="wrapper">
            <div id="info">{{ infoText }}</div>
        </div>
    </header>

    <main>
        <!-- Three.js 容器 -->
        <div id="threeContainer"></div>

        <!-- 文件输入控件 -->
        <div id="fileInput">
            <input type="file" id="zmapFile" accept="*" @change="handleFileUpload">
            <div>
                <label for="filePath">文件路径: </label>
                <input type="text" id="filePath" v-model="filePath" placeholder="输入文件URL">
            </div>
        </div>

        <!-- 控制面板 -->
        <div id="controls">
            <div>
                <label for="heightScale">高度缩放: </label>
                <input type="range" id="heightScale" min="0.05" max="1" step="0.05" v-model="heightScaleFactor">
                <span id="heightScaleValue">{{ heightScaleFactor }}</span>
            </div>
            <div>
                <label for="viewMode">视图模式: </label>
                <select id="viewMode" v-model="viewMode">
                    <option value="3d">3D 视图</option>
                    <option value="top">俯视图</option>
                </select>
            </div>
        </div>

        <!-- 统计信息 -->
        <div id="stats" v-html="statsInfo"></div>

        <!-- 颜色图例 -->
        <div id="colorLegend"></div>
        <div id="colorLabels">
            <div id="maxHeightLabel">{{ maxHeightLabel }}</div>
            <div id="height80Label">{{ height80Label }}</div>
            <div id="height60Label">{{ height60Label }}</div>
            <div id="height40Label">{{ height40Label }}</div>
            <div id="height20Label">{{ height20Label }}</div>
            <div id="minHeightLabel">{{ minHeightLabel }}</div>
        </div>
    </main>
</template>

<style scoped>
/* 基础样式 */
body {
    margin: 0;
    overflow: hidden;
    font-family: Arial, sans-serif;
}

header {
    line-height: 1.5;
}

main {
    position: relative;
    width: 100%;
    height: calc(100vh - 60px);
}

/* Three.js 容器 */
#threeContainer {
    width: 100%;
    height: 100%;
}

/* 信息面板 */
#info {
    position: absolute;
    top: 10px;
    left: 10px;
    color: white;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 10px;
    border-radius: 5px;
    pointer-events: none;
    z-index: 10;
}

/* 文件输入控件 */
#fileInput {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 5px;
    z-index: 10;
}

/* 统计信息 */
#stats {
    position: absolute;
    bottom: 10px;
    left: 10px;
    color: white;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 10px;
    border-radius: 5px;
    z-index: 10;
}

/* 控制面板 */
#controls {
    position: absolute;
    top: 60px;
    right: 10px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 5px;
    z-index: 10;
}

/* 颜色图例 */
#colorLegend {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 300px;
    background: linear-gradient(to top,
            rgb(0, 0, 128),
            /* 0% - 深蓝色 */
            rgb(0, 128, 255),
            /* 20% - 浅蓝色 */
            rgb(0, 255, 128),
            /* 40% - 青绿色 */
            rgb(128, 255, 0),
            /* 60% - 黄绿色 */
            rgb(255, 128, 0),
            /* 80% - 橙色 */
            rgb(255, 255, 128)
            /* 100% - 浅黄色 */
        );
    border-radius: 5px;
    border: 1px solid white;
    z-index: 10;
}

/* 颜色标签 */
#colorLabels {
    position: absolute;
    left: 45px;
    top: 50%;
    transform: translateY(-50%);
    height: 300px;
    color: white;
    font-size: 12px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 5px;
    border-radius: 5px;
    z-index: 10;
}

/* 响应式布局 */
@media (min-width: 1024px) {
    header {
        display: flex;
        place-items: center;
        padding-right: calc(var(--section-gap) / 2);
    }

    header .wrapper {
        display: flex;
        place-items: flex-start;
        flex-wrap: wrap;
    }
}
</style>
