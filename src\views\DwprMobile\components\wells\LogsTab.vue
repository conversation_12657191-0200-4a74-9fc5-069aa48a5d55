<template>
  <div class="logs-tab" v-if="currentType === LogsType.Logs">
    <div class="logs-tab-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh"
      pulling-text="Pull to refresh..."
      loosing-text="Release to refresh..."
      loading-text="Refreshing..."
      success-text="Refresh successful"
      >
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="No More"
          loading-text="Loading..."
          @load="onLoad"
        >
          <div class="loags-item" v-for="item in list" @click="handleClick(item)">
            <div class="item-left">
              <van-icon
                name="/src/icon/dwprMobile/logs-item-icon.svg"
                size="24"
              />
              <div class="item-left-content">
                <div class="item-left-content-title">LogName</div>
                <div class="item-left-content-value">Creator</div>
              </div>
            </div>
            <div class="item-right">
              <van-icon name="arrow" size="16" />
              <span>7-20</span>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
  <div class="curves" v-if="currentType === LogsType.Curves">
    <LogsCurves @goBack="handleGoBack" />
  </div>
  <div class="charts" v-if="currentType === LogsType.Charts">
    <LogsCharts @goBack="handleGoBack" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { showToast } from "vant";
import LogsCurves from './LogsCurves.vue'
import LogsCharts from './LogsCharts.vue'
const list = ref<any>([]);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
const onLoad = () => {
  setTimeout(() => {
    if (refreshing.value) {
      list.value = [];
      refreshing.value = false;
    }

    for (let i = 0; i < 10; i++) {
      list.value.push(list.value.length + 1);
    }
    loading.value = false;

    if (list.value.length >= 40) {
      finished.value = true;
    }
  }, 1000);
};

const onRefresh = () => {
  // 清空列表数据
  finished.value = false;

  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  onLoad();
};

enum LogsType {
  Logs = 'logs',
  Curves = 'curves',
  Charts = 'charts',
}

const currentType = ref<LogsType>(LogsType.Logs);
const handleClick = (item: any) => {
  currentType.value = LogsType.Curves;
};
const handleGoBack = (type: LogsType) => {
  currentType.value = type;
}
</script>

<style scoped lang="less">
.logs-tab {
  height: 100%;
  
  .logs-tab-list {
    height: 100%;
    overflow-y: auto;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }
  // background: #ffffff;
  // padding-bottom: 50px;
}
.loags-item {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  align-items: center;
  background: #ffffff;
  margin-top: 0.5rem;
  .item-left {
    display: flex;
    align-items: center;
    .item-left-content {
      padding-left: 0.5rem;
      .item-left-content-title {
        font-weight: 500;
        font-size: 1.125rem;
        color: #3d3d3d;
      }
      .item-left-content-value {
        font-weight: 400;
        font-size: 1rem;
        color: #737373;
      }
    }
  }
  .item-right {
    display: flex;
    align-items: center;
    span {
      padding-left: 1.5rem;
      font-weight: 400;
      font-size: 1.125rem;
      color: #666666;
    }
  }
}
</style>
