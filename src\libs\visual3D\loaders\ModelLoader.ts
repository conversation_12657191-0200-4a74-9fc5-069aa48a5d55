import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';

export class ModelLoader {
  private static instance: ModelLoader;
  private gltfLoader = new GLTFLoader();
  private objLoader = new OBJLoader();
  private cache = new Map<string, THREE.Group>();

  private constructor() {}
  
  static getInstance(): ModelLoader {
    if (!ModelLoader.instance) {
      ModelLoader.instance = new ModelLoader();
    }
    return ModelLoader.instance;
  }

  async loadGLB(url: string): Promise<THREE.Group> {
    if (this.cache.has(url)) {
      return this.cache.get(url)!.clone();
    }

    try {
      const gltf = await this.gltfLoader.loadAsync(url);
      const model = gltf.scene;
      model.traverse((child: any) => {
        if (child instanceof THREE.Mesh) {
          child.castShadow = true;
          child.receiveShadow = true;
        }
      });
      this.cache.set(url, model);
      return model.clone();
    } catch (error) {
      throw new Error(`Failed to load model: ${error}`);
    }
  }

  async loadOBJ(url: string): Promise<THREE.Group> {
    if (this.cache.has(url)) {
      return this.cache.get(url)!.clone();
    }

    try {
      const obj = await this.objLoader.loadAsync(url);
      
      this.cache.set(url, obj);
      return obj.clone();
    } catch (error) {
      throw new Error(`Failed to load model: ${error}`);
    }
  }
}