  <template>
    <div class="custom-palette-container">
      <!-- Pa<PERSON> -->
      <canvas 
        ref="paletteCanvas" 
        class="palette-canvas" 
        @dblclick="addNode"
        @click="handleClick"
        title="双击此处添加新节点"
      ></canvas>
      
      <!-- Nodes for dragging -->
      <div class="nodes-container">
        <div
          v-for="(node, index) in sortedNodes"
          :key="node.id || index"
          class="node"
          :style="{ left: `${node.position * canvasWidth}px` }"
          :class="{ 'end-node': index === 0 || index === sortedNodes.length - 1 }"
          @mousedown="startDragging($event, getOriginalIndex(node))"
          @dblclick.stop="editNodeColor(getOriginalIndex(node))"
          @contextmenu.prevent="showContextMenu($event, getOriginalIndex(node))"
        ></div>
      </div>

      <!-- 节点数值标签 -->
      <div class="node-labels">
        <div
          v-for="(node, index) in sortedNodes"
          :key="'label-' + (node.id || index)"
          class="node-label"
          :style="{ left: `${node.position * canvasWidth}px` }"
        >
          {{ getNodeValue(node.position).toFixed(1) }}
        </div>
      </div>

      <!-- 刻度线 -->
      <div class="scale-container">
        <div class="scale-line"></div>
        <!-- 主要刻度 -->
        <div
          v-for="tick in majorTicks"
          :key="'major-' + tick.value"
          class="scale-tick major-tick"
          :style="{ left: `${tick.position * canvasWidth}px` }"
        >
          <div class="tick-mark"></div>
          <div class="tick-label">{{ tick.value }}</div>
        </div>
        <!-- 次要刻度 -->
        <div
          v-for="tick in minorTicks"
          :key="'minor-' + tick.value"
          class="scale-tick minor-tick"
          :style="{ left: `${tick.position * canvasWidth}px` }"
        >
          <div class="tick-mark"></div>
        </div>
      </div>

      <!-- 添加节点按钮（临时测试） -->
      <div class="add-node-controls">
        <button @click="addNodeAtCenter" class="add-node-btn">
          ➕ 在中间添加节点
        </button>
        <span class="hint">或双击色标条添加节点</span>
      </div>

      <!-- Context Menu for Deleting Nodes -->
      <div v-if="showMenu" class="context-menu" :style="{ top: `${menuY}px`, left: `${menuX}px` }">
        <button @click="deleteNode">Delete</button>
      </div>
      
      <!-- Color Picker -->
      <input
        v-if="showColorPicker"
        type="color"
        v-model="selectedColor"
        class="color-picker"
        @input="updateNodeColor"
        ref="colorPicker"
      />
    </div>
  </template>
  
  <script>
  export default {
    name: 'CustomPalette',
    props: {
      initialPalette: {
        type: Array,
        default: () => [
          { position: 0, color: '#FF0000' }, // Left end (min value)
          { position: 1, color: '#0000FF' }, // Right end (max value)
        ],
      },
      minValue: {
        type: Number,
        default: 0,
      },
      maxValue: {
        type: Number,
        default: 100,
      },
    },
    data() {
      return {
        nodes: this.initialPalette.map((node, index) => ({ 
          ...node, 
          id: node.id || `initial-${index}-${Date.now()}` 
        })),
        canvasWidth: 300,
        canvasHeight: 50,
        draggingIndex: null,
        draggingNodeId: null,
        showMenu: false,
        menuX: 0,
        menuY: 0,
        menuNodeIndex: null,
        showColorPicker: false,
        selectedColor: '#000000',
        selectedNodeIndex: null,
      };
    },
    computed: {
      sortedNodes() {
        return [...this.nodes].sort((a, b) => a.position - b.position);
      },
      // 主要刻度（显示数值标签）
      majorTicks() {
        const ticks = [];
        const range = this.maxValue - this.minValue;
        const tickCount = Math.min(10, Math.max(3, Math.floor(range / 10) + 1));
        
        for (let i = 0; i <= tickCount; i++) {
          const value = this.minValue + (range * i) / tickCount;
          const position = i / tickCount;
          ticks.push({ value: Math.round(value * 10) / 10, position });
        }
        return ticks;
      },
      // 次要刻度（不显示数值标签）
      minorTicks() {
        const ticks = [];
        const range = this.maxValue - this.minValue;
        const majorTickCount = this.majorTicks.length - 1;
        const minorTicksPerMajor = 4; // 每个主刻度之间4个次刻度
        
        for (let i = 0; i < majorTickCount; i++) {
          for (let j = 1; j < minorTicksPerMajor; j++) {
            const majorStart = i / majorTickCount;
            const majorEnd = (i + 1) / majorTickCount;
            const position = majorStart + (majorEnd - majorStart) * j / minorTicksPerMajor;
            const value = this.minValue + range * position;
            ticks.push({ value: Math.round(value * 10) / 10, position });
          }
        }
        return ticks;
      }
    },
    mounted() {
      this.$nextTick(() => {
        this.resizeCanvas();
        this.drawPalette();
      });
      window.addEventListener('resize', this.resizeCanvas);
      window.addEventListener('mousemove', this.handleDrag);
      window.addEventListener('mouseup', this.stopDragging);
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.resizeCanvas);
      window.removeEventListener('mousemove', this.handleDrag);
      window.removeEventListener('mouseup', this.stopDragging);
    },
    methods: {
            drawPalette() {
        const canvas = this.$refs.paletteCanvas;
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        // 确保画布尺寸正确
        const rect = canvas.getBoundingClientRect();
        canvas.width = this.canvasWidth;
        canvas.height = this.canvasHeight;
        
        console.log('画布尺寸信息:', {
          canvasWidth: this.canvasWidth,
          canvasHeight: this.canvasHeight,
          actualWidth: canvas.width,
          actualHeight: canvas.height,
          cssWidth: rect.width,
          cssHeight: rect.height
        });

        // Create gradient
        const gradient = ctx.createLinearGradient(0, 0, this.canvasWidth, 0);
        this.nodes
          .sort((a, b) => a.position - b.position)
          .forEach((node) => {
            gradient.addColorStop(node.position, node.color);
          });

        // Draw palette
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
        ctx.strokeStyle = '#000';
        ctx.strokeRect(0, 0, this.canvasWidth, this.canvasHeight);

        this.$emit('palette-updated', this.nodes, this.mapValueToColor);
      },
      resizeCanvas() {
        const newWidth = this.$refs.paletteCanvas.parentElement.clientWidth || 300;
        if (newWidth !== this.canvasWidth) {
          this.canvasWidth = newWidth;
          this.drawPalette();
        }
      },
            addNode(event) {
        console.log('addNode 方法被调用'); // 调试信息
        
        const canvas = this.$refs.paletteCanvas;
        if (!canvas) {
          console.log('画布不存在');
          return;
        }
        
        const rect = canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const position = x / this.canvasWidth;
        
        console.log('点击信息:', {
          clientX: event.clientX,
          rectLeft: rect.left,
          x: x,
          canvasWidth: this.canvasWidth,
          position: position
        });

        // 放宽边界限制，允许在更靠近边缘的位置添加节点
        if (position <= 0.05 || position >= 0.95) {
          console.log('位置太靠近端点，跳过添加');
          return;
        }

        // Estimate color at position
        const color = this.interpolateColor(position);
        // Add unique ID to track nodes after sorting
        const newNode = { position, color, id: Date.now() + Math.random() };
        console.log('新节点:', newNode);
        
        this.nodes.push(newNode);
        this.nodes.sort((a, b) => a.position - b.position);
        this.drawPalette();
        
        console.log('节点添加完成，当前节点数:', this.nodes.length);
      },
      startDragging(event, index) {
        // Store the node ID instead of index to track after sorting
        this.draggingNodeId = this.nodes[index].id || index;
        this.draggingIndex = index;
        this.showMenu = false;
      },
      handleDrag(event) {
        if (this.draggingIndex === null) return;
  
        // Find current index by node ID if available
        if (this.draggingNodeId !== undefined) {
          const currentIndex = this.nodes.findIndex(node => node.id === this.draggingNodeId);
          if (currentIndex !== -1) {
            this.draggingIndex = currentIndex;
          }
        }
  
        const canvas = this.$refs.paletteCanvas;
        const rect = canvas.getBoundingClientRect();
        let x = event.clientX - rect.left;
        x = Math.max(0, Math.min(x, this.canvasWidth));
        let newPosition = x / this.canvasWidth;
  
        // Sort nodes by position to get correct order
        const sortedNodes = [...this.nodes].sort((a, b) => a.position - b.position);
        const currentNodeInSorted = sortedNodes.findIndex(node => 
          node.id === this.draggingNodeId || sortedNodes.indexOf(this.nodes[this.draggingIndex]) === sortedNodes.indexOf(node)
        );
  
        const isLeftEnd = currentNodeInSorted === 0;
        const isRightEnd = currentNodeInSorted === sortedNodes.length - 1;
  
        if (isLeftEnd) {
          newPosition = Math.min(newPosition, sortedNodes[1].position);
        } else if (isRightEnd) {
          newPosition = Math.max(newPosition, sortedNodes[sortedNodes.length - 2].position);
        } else {
          const prevNode = sortedNodes[currentNodeInSorted - 1];
          const nextNode = sortedNodes[currentNodeInSorted + 1];
          newPosition = Math.max(prevNode.position, Math.min(newPosition, nextNode.position));
        }
  
        this.nodes[this.draggingIndex].position = newPosition;
        this.drawPalette();
      },
      stopDragging() {
        this.draggingIndex = null;
        this.draggingNodeId = null;
      },
      showContextMenu(event, index) {
        if (index === 0 || index === this.nodes.length - 1) return; // Prevent deleting endpoints
        this.showMenu = true;
        this.menuX = event.clientX;
        this.menuY = event.clientY;
        this.menuNodeIndex = index;
      },
      deleteNode() {
        if (this.menuNodeIndex !== null && this.menuNodeIndex > 0 && this.menuNodeIndex < this.nodes.length - 1) {
          this.nodes.splice(this.menuNodeIndex, 1);
          this.drawPalette();
        }
        this.showMenu = false;
        this.menuNodeIndex = null;
      },
      editNodeColor(index) {
        this.selectedNodeIndex = index;
        this.selectedColor = this.nodes[index].color;
        this.showColorPicker = true;
        this.$nextTick(() => {
          this.$refs.colorPicker.focus();
        });
      },
      updateNodeColor() {
        if (this.selectedNodeIndex !== null) {
          this.nodes[this.selectedNodeIndex].color = this.selectedColor;
          this.drawPalette();
        }
      },
      interpolateColor(position) {
        const sortedNodes = [...this.nodes].sort((a, b) => a.position - b.position);
        let leftNode = sortedNodes[0];
        let rightNode = sortedNodes[sortedNodes.length - 1];
  
        for (let i = 0; i < sortedNodes.length - 1; i++) {
          if (position >= sortedNodes[i].position && position <= sortedNodes[i + 1].position) {
            leftNode = sortedNodes[i];
            rightNode = sortedNodes[i + 1];
            break;
          }
        }
  
        const factor =
          (position - leftNode.position) / (rightNode.position - leftNode.position);
        const leftRGB = this.hexToRGB(leftNode.color);
        const rightRGB = this.hexToRGB(rightNode.color);
        const r = Math.round(leftRGB.r + factor * (rightRGB.r - leftRGB.r));
        const g = Math.round(leftRGB.g + factor * (rightRGB.g - leftRGB.g));
        const b = Math.round(leftRGB.b + factor * (rightRGB.b - leftRGB.b));
        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
      },
      hexToRGB(hex) {
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        return { r, g, b };
      },
      mapValueToColor(value) {
        const normalized = (value - this.minValue) / (this.maxValue - this.minValue);
        return this.interpolateColor(normalized);
      },
      savePalette() {
        // Emit event to save palette with project data
        this.$emit('save-palette', this.nodes);
      },
      getOriginalIndex(node) {
        return this.nodes.findIndex(n => n.id === node.id || n === node);
      },
      // 测试单击事件
      handleClick(event) {
        console.log('画布被单击了:', event);
      },
      // 在中间添加节点（测试用）
      addNodeAtCenter() {
        const position = 0.5; // 中间位置
        const color = this.interpolateColor(position);
        const newNode = { position, color, id: Date.now() + Math.random() };
        console.log('手动添加节点:', newNode);
        
        this.nodes.push(newNode);
        this.nodes.sort((a, b) => a.position - b.position);
        this.drawPalette();
        
        console.log('节点添加完成，当前节点数:', this.nodes.length);
      },
      // 根据位置计算节点对应的数值
      getNodeValue(position) {
        return this.minValue + (this.maxValue - this.minValue) * position;
      },
      // 导出调色板为JSON格式
      exportPaletteAsJSON() {
        const paletteData = {
          version: '1.0',
          timestamp: new Date().toISOString(),
          palette: {
            nodeCount: this.nodes.length,
            minValue: this.minValue,
            maxValue: this.maxValue,
            nodes: this.nodes.map((node, index) => ({
              index: index,
              position: node.position,
              color: node.color,
              id: node.id
            }))
          }
        };
        return JSON.stringify(paletteData, null, 2);
      },
      // 从JSON数据加载调色板
      loadPaletteFromJSON(jsonData) {
        try {
          const data = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;
          if (data.palette && data.palette.nodes) {
            this.nodes = data.palette.nodes.map(node => ({
              position: node.position,
              color: node.color,
              id: node.id || `loaded-${node.index}-${Date.now()}`
            }));
            this.drawPalette();
            this.$emit('palette-loaded', this.nodes);
            return true;
          }
        } catch (error) {
          console.error('加载调色板失败:', error);
          return false;
        }
        return false;
      },
    },
  };
  </script>
  
  <style scoped>
  .custom-palette-container {
    position: relative;
    width: 100%;
    height: 120px;
  }
  
  .palette-canvas {
    width: 100%;
    height: 50px;
    border: 1px solid #000;
  }
  
  .nodes-container {
    position: absolute;
    top: 0;
    width: 100%;
    height: 50px;
    z-index: 20;
  }
  
  .node {
    position: absolute;    
    width: 10px;
    height: 10px;
    background: #fff;
    border: 2px solid #000;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    cursor: grab;
  }
  
  .node:active {
    cursor: grabbing;
  }
  
  .end-node {
    background: #ccc;
  }
  
  .context-menu {
    position: fixed;
    background: #fff;
    border: 1px solid #000;
    padding: 5px;
    z-index: 1000;
  }
  
  .context-menu button {
    display: block;
    width: 100%;
    text-align: left;
    padding: 5px;
  }
  
  .context-menu button:hover {
    background: #f0f0f0;
  }
  
  .color-picker {
    position: absolute;
    z-index: 1000;
  }

  /* 节点数值标签样式 */
  .node-labels {
    position: absolute;
    top: -25px;
    width: 100%;
    height: 20px;
    pointer-events: none;
    z-index: 10;
  }

  .node-label {
    position: absolute;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    white-space: nowrap;
  }

  /* 刻度样式 */
  .scale-container {
    position: absolute;
    top: 55px;
    width: 100%;
    height: 40px;
    pointer-events: none;
    z-index: 5;
  }

  .scale-line {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: #666;
  }

  .scale-tick {
    position: absolute;
    top: 0;
  }

  .major-tick .tick-mark {
    width: 1px;
    height: 8px;
    background: #333;
    margin-left: -0.5px;
  }

  .minor-tick .tick-mark {
    width: 1px;
    height: 4px;
    background: #999;
    margin-left: -0.5px;
  }

  .tick-label {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 11px;
    color: #666;
    white-space: nowrap;
  }

  .major-tick {
    transform: translateX(-50%);
  }

  .minor-tick {
    transform: translateX(-50%);
  }

  /* 添加节点控制按钮样式 */
  .add-node-controls {
    position: absolute;
    top: 100px;
    left: 0;
    right: 0;
    text-align: center;
    z-index: 15;
  }

  .add-node-btn {
    padding: 6px 12px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    margin-right: 10px;
  }

  .add-node-btn:hover {
    background-color: #0056b3;
  }

  .hint {
    font-size: 11px;
    color: #666;
    font-style: italic;
  }
  </style>