{"name": "vue3-veaury-react-rcdock", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "vue-tsc --build", "postinstall": "patch-package"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@skit/x.surely-vue-table": "^0.2.0", "@smallwei/avue": "^3.6.4", "@surely-vue/table": "^4.3.17", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vueuse/core": "^13.5.0", "vue-devui": "^1.6.30", "axios": "^1.7.9", "echarts": "^5.6.0", "element-plus": "^2.9.3", "js-cookie": "^3.0.5", "monaco-editor": "^0.34.0", "monaco-editor-webpack-plugin": "^7.0.1", "monaco-languageclient": "^3.0.1", "mqtt": "^5.13.3", "pinia": "^2.3.0", "rc-dock": "^3.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "three": "^0.174.0", "vant": "^4.9.20", "veaury": "^2.6.1", "vscode-ws-jsonrpc": "^1.0.2", "vue": "^3.4.15", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/node": "^22.10.2", "@types/smallwei__avue": "^3.0.5", "@types/three": "^0.174.0", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "less": "^4.2.2", "sass": "1.77.5", "lil-gui": "^0.20.0", "npm-run-all2": "^7.0.2", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "typescript": "~5.6.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.1", "vite-plugin-vue-devtools": "^7.6.8", "vue-tsc": "^2.1.10"}}