// src/vue.d.ts
import { ComponentPublicInstance } from 'vue';

declare module 'vue' {
  interface ComponentPublicInstance {
    $reactRef?: {
      dockMove: (tab: TabData, group: string, direction: string) => void;
      getLayout: () => LayoutData;
      loadLayout: (layout: LayoutData) => void;
    };
  }
}

// 补充rc-dock类型
declare module 'rc-dock' {
  interface DockLayout {
    dockMove: (tab: TabData, group: string, direction: string) => void;
    getLayout: () => LayoutData;
    loadLayout: (layout: LayoutData) => void;
  }
}