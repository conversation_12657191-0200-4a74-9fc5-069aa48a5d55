<!DOCTYPE html>
<html>
<head>
    <title>MQTT.js Web Client</title>
    <script src="https://unpkg.com/mqtt/dist/mqtt.min.js"></script>
</head>
<body>
    <h1>MQTT.js Web Client Test</h1>
    <div id="connection">
        <button onclick="connect()">连接</button>
        <button onclick="disconnect()">断开</button>
    </div>
    <div id="publish">
        <input type="text" id="pubTopic" placeholder="发布主题" value="test/topic">
        <input type="text" id="pubMessage" placeholder="消息内容" value="Hello MQTT.js">
        <button onclick="publish()">发布</button>
    </div>
    <div id="subscribe">
        <input type="text" id="subTopic" placeholder="订阅主题" value="test/topic">
        <button onclick="subscribe()">订阅</button>
    </div>
    <div id="messages" style="border: 1px solid #ccc; height: 400px; overflow-y: scroll; margin-top: 10px;"></div>

    <script>
        let client;
        
        function connect() {
            // 使用 WebSocket 连接
            client = mqtt.connect('ws://localhost:8083');
            
            client.on('connect', () => {
                addMessage('✅ 已连接到MQTT服务器');
            });
            
            client.on('error', (error) => {
                addMessage(`❌ 连接错误: ${error.message}`);
            });
            
            client.on('message', (topic, message) => {
                addMessage(`📥 [${topic}]: ${message.toString()}`);
            });
            
            client.on('disconnect', () => {
                addMessage('⚠️ 连接已断开');
            });
        }
        
        function disconnect() {
            if (client) {
                client.end();
                addMessage('👋 已断开连接');
            }
        }
        
        function publish() {
            const topic = document.getElementById('pubTopic').value;
            const message = document.getElementById('pubMessage').value;
            
            if (client && client.connected) {
                client.publish(topic, message);
                addMessage(`📤 [${topic}]: ${message}`);
            } else {
                addMessage('❌ 未连接到服务器');
            }
        }
        
        function subscribe() {
            const topic = document.getElementById('subTopic').value;
            
            if (client && client.connected) {
                client.subscribe(topic, (err) => {
                    if (!err) {
                        addMessage(`📢 已订阅: ${topic}`);
                    } else {
                        addMessage(`❌ 订阅失败: ${err.message}`);
                    }
                });
            } else {
                addMessage('❌ 未连接到服务器');
            }
        }
        
        function addMessage(msg) {
            const messagesDiv = document.getElementById('messages');
            const time = new Date().toLocaleTimeString();
            messagesDiv.innerHTML += `<div style="padding: 5px; border-bottom: 1px solid #eee;">
                <small style="color: #666;">${time}</small> ${msg}
            </div>`;
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
    </script>
</body>
</html>
