import { ref, nextTick } from 'vue'
import { usePreviewStore } from "@/stores/preview.js";
export const useCurvePreview = () => {
  const curvePreviewDialog = ref<boolean>(false)
  const curveTitle = ref<string>('')
  const usePreStore = usePreviewStore();
  const handleCurvePreviewDialogClose = () => {
    // 关闭弹框重置状态
    curvePreviewDialog.value = false
    usePreStore.setView(false);
    usePreStore.setPreviewCurve(false);
    usePreStore.setChannelId('');
    usePreStore.setNodeInfo({});
    usePreStore.setProjectId('');
  }
  const handleCurvePreviewDialogOpen = (title: string, data: any) => {
    curvePreviewDialog.value = true
    curveTitle.value = title
    nextTick(() => {
        // 设置必要的参数
        usePreStore.setChannelId(data.id);
        usePreStore.setNodeInfo(data);
        usePreStore.setProjectId(data.projectId || ''); // 使用组件的项目ID
        usePreStore.setView(true);
        usePreStore.setPreviewCurve(true);
      })
  }
  return {
    curvePreviewDialog,
    curveTitle,
    handleCurvePreviewDialogClose,
    handleCurvePreviewDialogOpen
  }
}