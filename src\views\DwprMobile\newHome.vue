<template>
    <div class="workplace-container">
     <!-- 主内容区域 -->
      <div class="content-wrapper" :class="{ 'with-tabbar': showTabbar }">
        <router-view />
      </div>
      <!-- 底部导航栏 -->
      <van-tabbar v-if="showTabbar" route>
        <van-tabbar-item replace to="/Dwpr/well">
          <template #icon="{ active }">
            <van-icon :name="active ? wellActiveIcon : wellIcon" />
          </template>
          Wells
        </van-tabbar-item>
        <van-tabbar-item replace to="/Dwpr/work">
          <template #icon="{ active }">
             <van-icon :name="active ? workActiveIcon : workIcon" />
          </template>
          Workbench
        </van-tabbar-item>
      </van-tabbar>
    </div>
  </template>
  
  <script setup lang="ts">
  import { computed } from 'vue'

  // 导入图片资源
  import wellIcon from '@/icon/dwprMobile/wells.svg'
  import wellActiveIcon from '@/icon/dwprMobile/wells-active.svg'
  import workIcon from '@/icon/dwprMobile/work.svg'
  import workActiveIcon from '@/icon/dwprMobile/work-active.svg'
  import { useShowTabbarStore } from '@/stores/useShowTabbar.ts'
  const showTabbarStore = useShowTabbarStore()
  const showTabbar = computed(() => showTabbarStore.showTabbar)

  </script>
  
  <style scoped lang="less">
  .workplace-container {
    height: 100vh;
    background-color: #EDEDED;
    display: flex;
    flex-direction: column;
  }
  // 自定义 tabbar 文字颜色
  :deep(.van-tabbar-item) {
    // 未激活状态的文字颜色
    --van-tabbar-item-text-color: #3D3D3D;
    // 激活状态的文字颜色
    --van-tabbar-item-active-color: #4472C4;
  }
  .content-wrapper {
    height: 100%;
    flex: 1;
  }
  .with-tabbar {
    padding-bottom: 50px; /* 为底部导航栏预留空间 */
  }
  </style>