<template>
  <div>
    <el-table
      :data="paginatedData"
      height="100%"
      style="width: 100%"
      @cell-click="cellClick"
    >
      <el-table-column
        v-for="(item, index) in tableHeader"
        :key="item.name"
        :label="item.alias"
        :prop="item.name"
        :width="item.width"
      >
        <template #default="scope">
          <el-input
            v-if="
              scope.row.editing &&
              scope.row.editing === true &&
              index === 1 &&
              store.isStartEdit
            "
            @blur="handleBlur(scope.row, scope.$index)"
            v-model="scope.row[item.name]"
          ></el-input>
          <span v-else>{{ scope.row[item.name] }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script setup>
import { onMounted, ref, computed, markRaw, onUnmounted } from "vue";
import { useEditCurveStore } from "@/stores/editCurveStore";
const emit = defineEmits(["curve-data"]);
const props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
  tableHeader: {
    type: Array,
    default: () => [],
  },
  startIndex: {
    type: Number,
    default: 0,
  },
  endIndex: {
    type: Number,
    default: 20,
  },
});
const store = useEditCurveStore();
const paginatedData = computed(() => {
  if (props.tableData) {
    return props.tableData.slice(props.startIndex, props.endIndex);
  } else {
    return props.tableData;
  }
});
//点击单元格
function cellClick(row, column, cell, event) {
  let field = props.tableHeader[0].name;
  props.tableData.forEach((item, index) => {
    if (item[field] === row[field]) {
      props.tableData[index].editing = true;
    }
  });
}
function handleBlur(row, index) {
  // 调用父组件方法
  // console.log(row);
  const globalIndex = props.startIndex + index; // 计算全局索引

  console.log(globalIndex);
  // 初始化最小值
  // if (store.minIndex === 0) {
  //   store.setMinIndex(globalIndex);
  // }
  // 初始化最大值
  // if (store.maxIndex === 0) {
  //   store.setMaxIndex(globalIndex);
  // }
  // 更新最小值
  if (globalIndex < store.minIndex) {
    store.setMinIndex(globalIndex);
  }
  // 更新最大值
  if (globalIndex > store.maxIndex) {
    store.setMaxIndex(globalIndex);
  }
  console.log(store.minIndex);
  console.log(store.maxIndex);
  emit("curve-data", row);
}
</script>
