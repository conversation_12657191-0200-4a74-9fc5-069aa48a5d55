<template>
  <div class="CurveEdit" v-loading="isLoading">
    <!-- <div v-if="!isEnpty" style="height: 100%">
      <el-switch
        v-model="radio"
        inline-prompt
        active-text="Vertical"
        inactive-text="Horizontal"
      />
      <curveVertical v-if="radio" :tabId="tabId" :curveData="curveData" />
      <curveHorizontal v-if="!radio" :tabId="tabId" :curveData="curveData" />
    </div>
    <div v-else>
      <el-empty description="No Data" />
    </div> -->
    <template v-if="curveData">
      <el-switch
        v-model="radio"
        inline-prompt
        active-text="Vertical"
        inactive-text="Horizontal"
      />
      <component
        :is="radio ? curveVertical : curveHorizontal"
        :tab-id="tabId"
        :curveData="curveData"
        @updateCurveData="handleUpdateCurveData"
      />
    </template>

    <el-empty v-else description="No Data" />
  </div>
</template>
<script setup>
import { watch, ref, onMounted, onUnmounted } from "vue";
import curveVertical from "./curveVertical.vue";
import curveHorizontal from "./curveHorizontal.vue";
import { useEditCurveStore } from "@/stores/editCurveStore";
import { CurvePreview } from "@/api/curve";
const emit = defineEmits(["curveTabClose"]);
const radio = ref(true);
const store = useEditCurveStore();
const curveData = ref(null);
const isLoading = ref(false);
const props = defineProps({
  tabId: {
    type: String,
    default: "",
  },
  info: {
    type: Object,
    default: () => ({}),
  },
});
let data = null;
watch(radio, () => {
  store.setActiveAction("startEdit");
  if (data) {
    curveData.value = data;
  }

  //fetchCurveData();
  //store.curveEditInstance = {}; // 清空曲线实例
});
const handleUpdateCurveData = (newData) => {
  //curveData.value = newData; // 更新曲线数据
  data = newData;
};
// function getCurvePreview() {
//   isLoading.value = true;
//   CurvePreview(props.info).then((res) => {
//     if (res.success) {
//       curveData.value = res.data;
//       isEnpty.value = false;
//     } else {
//       isEnpty.value = true;
//     }
//     isLoading.value = false;
//   });
// }
const fetchCurveData = async () => {
  try {
    isLoading.value = true;
    const response = await CurvePreview(props.info);

    if (response?.success) {
      curveData.value = response.data;
    } else {
      curveData.value = null;
    }
  } catch (error) {
    console.error("请求异常:", error);
    curveData.value = null;
  } finally {
    isLoading.value = false;
  }
};
onMounted(() => {
  fetchCurveData();
});
onUnmounted(() => {
  emit("curveTabClose", props.tabId);
});
</script>
<style lang="less">
.CurveEdit {
  width: 100%;
  height: 100%;
  // display: flex;
}
</style>
