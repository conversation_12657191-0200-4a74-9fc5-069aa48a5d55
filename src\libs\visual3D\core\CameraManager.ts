import * as THREE from 'three';

export type CameraType = 'perspective' | 'orthographic';

export class CameraManager {
  public camera: THREE.Camera;
  private type: CameraType;

  constructor(type: CameraType = 'perspective') {
    this.type = type;
    this.camera = this.createCamera(window.innerWidth / window.innerHeight);
    this.setupResizeHandler();
  }

  private createCamera(aspect: number): THREE.Camera {
    switch(this.type) {
      case 'perspective':
        return new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
      case 'orthographic':
        const frustumSize = 50;
        return new THREE.OrthographicCamera(
          frustumSize * aspect / -2,
          frustumSize * aspect / 2,
          frustumSize / 2,
          frustumSize / -2,
          0.1,
          1000
        );
      default:
        throw new Error('Invalid camera type');
    }
  }

  private setupResizeHandler(): void {
    window.addEventListener('resize', () => {
      const aspect = window.innerWidth / window.innerHeight;
      if (this.camera instanceof THREE.PerspectiveCamera) {
        this.camera.aspect = aspect;
      } else if (this.camera instanceof THREE.OrthographicCamera) {
        const frustumSize = 50;
        this.camera.left = -frustumSize * aspect / 2;
        this.camera.right = frustumSize * aspect / 2;
        this.camera.top = frustumSize / 2;
        this.camera.bottom = -frustumSize / 2;
      }
      
      if (this.camera instanceof THREE.PerspectiveCamera || this.camera instanceof THREE.OrthographicCamera) {
        this.camera.updateProjectionMatrix();
      }
    });
  }

  setPosition(x: number, y: number, z: number): void {
    this.camera.position.set(x, y, z);
  }

  // 获取器方法
  public getCameraType(): CameraType {
    return this.type;
  }
}