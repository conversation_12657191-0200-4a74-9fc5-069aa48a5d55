<template>
    <div class="workplace-container">
      <!-- 顶部导航栏 -->
      <van-nav-bar 
        title="工作台" 
        left-arrow 
        @click-left="onClickLeft"
        class="nav-bar"
      />



      <!-- 主要内容区域 -->
      <div class="content-section">

      <!-- 欢迎区域 -->
      <div class="card-container">
        <div class="card-header">
            <h3 class="card-title">
              <van-icon name="user-o" size="18" color="#1989fa" style="margin-right: 8px;" />
              个人信息
            </h3>
        </div>
        <div class="welcome-section">
            <div class="welcome-text">
              <van-icon name="smile-o" size="18" color="#1989fa" style="margin-right: 8px;" />
              欢迎回来，{{ userInfo.cName || '用户' }}
            </div>
            <div class="welcome-text" v-if="userInfo.lastLoginDate">
              <van-icon name="clock-o" size="14" color="#969799" style="margin-right: 6px;" />
              上次登录：{{ formatLoginDate(userInfo.lastLoginDate) }}
            </div>
        </div>
      </div>
    
        <!-- 工程列表卡片 -->
        <div class="card-container">
          <div class="card-header">
            <h3 class="card-title">
              <van-icon name="folder-o" size="18" color="#1989fa" style="margin-right: 8px;" />
              工程列表
            </h3>
          </div>
          <div class="project-list">
            <!-- 加载中状态 -->
            <div v-if="projectLoading" class="loading-container">
              <van-loading size="24px" vertical>加载中...</van-loading>
            </div>
            
            <!-- 工程列表 -->
            <div v-else-if="projectList.length > 0" class="list-content">
              <div 
                v-for="item in projectList" 
                :key="item.id"
                class="project-item"
                @click="onProjectClick(item)"
              >
                <div class="project-content">
                  <div class="project-title">{{ item.projectName }}</div>
                  <div class="project-info">
                    <div class="info-item">
                      <van-icon name="clock-o" size="12" color="#969799" />
                      <span>{{ item.createTime }}</span>
                    </div>
                    <div class="info-item" v-if="item.wellboreNumber">
                      <van-icon name="location-o" size="12" color="#969799" />
                      <span>{{ item.wellboreNumber }}</span>
                    </div>
                  </div>
                </div>
                <van-icon name="arrow" color="#969799" size="16" />
              </div>
            </div>
            
            <!-- 空状态 -->
            <div v-else class="empty-state">
              <van-icon name="folder-o" size="48" color="#ddd" />
              <div class="empty-text">暂无工程数据</div>
            </div>
          </div>
        </div>

        <!-- 消息列表卡片 -->
        <div class="card-container">
          <div class="card-header">
            <h3 class="card-title">
              <van-icon name="chat-o" size="18" color="#1989fa" style="margin-right: 8px;" />
              消息列表
            </h3>            
          </div>
          <div class="message-list">
            <div 
              v-for="(message, index) in messageList" 
              :key="index"
              class="message-item"
              @click="onMessageClick(message)"
            >
              <div class="message-icon">
                <van-icon 
                  :name="getMessageIcon(message.messageType, message.priority)" 
                  :color="getMessageIconColor(message.messageType, message.priority)"
                  size="20"
                />
              </div>
              <div class="message-content">
                <div class="message-title">
                  {{ message.title }}
                  <van-tag 
                    v-if="message.priority === 'High'" 
                    type="danger" 
                    size="mini"
                    style="margin-left: 8px;"
                  >
                    重要
                  </van-tag>
                </div>
                
                <div class="message-desc">{{ message.description }}</div>
                <div class="message-time">
                  <van-icon name="clock-o" size="12" color="#c8c9cc" />
                  <span style="margin-left: 4px;">{{ message.time }}</span>
                </div>
              </div>
              <div class="message-status" :class="{ unread: !message.isRead }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { showToast, showDialog } from 'vant'
  import axios from 'axios'
  
  const router = useRouter()
  const vWebApiUrl = window.location.protocol + "//" + window.location.host 
  // 工程列表数据
  const projectList = ref([])
  const projectLoading = ref(false)
  const loading = ref(false)
  
  // 用户信息
  const userInfo = ref({
    userId: '',
    cName: '',
    lastLoginDate: ''
  })

    // 获取用户信息
    const getUserInfo = async () => {
        try {
            const response = await axios.get(vWebApiUrl + '/api/user/user/GetCurrentUserBaseInfo')
            const result = response.data
            console.log('用户信息:', result)
            
            if (result.success && result.data) {
                userInfo.value = {
                    userId: result.data.userId,
                    cName: result.data.cName,
                    lastLoginDate: result.data.lastLoginDate
                }
            } else {
                showToast(result.message || '获取用户信息失败')
            }
        } catch (error) {
            console.error('获取用户信息错误:', error)
            showToast('获取用户信息失败')
        }
    }
    
         // 格式化登录时间
     const formatLoginDate = (dateString) => {
         if (!dateString) return ''
         const date = new Date(dateString)
         return date.toLocaleDateString('zh-CN', {
             year: 'numeric',
             month: '2-digit',
             day: '2-digit'
         }).replace(/\//g, '-')
     }

     // 获取消息列表
     const getMessageList = async () => {
         try {
             const response = await axios.get(`${vWebApiUrl}/api/stateSync/Message/GetUserMessages?page=1&size=100`)
             const result = response.data
             console.log('消息列表:', result)
             
             if (result.success && result.data && result.data.list) {
                 messageList.value = result.data.list.map(item => ({
                     id: item.id,
                     title: item.title,
                     description: item.content || '无内容',
                     time: formatMessageTime(item.createdOn),
                     isRead: false, // 默认未读，可以根据实际需求调整
                     priority: item.priority,
                     senderName: item.senderName,
                     messageType: item.messageType,
                     expiryTime: item.expiryTime
                 }))
             } else {
                 console.error('获取消息列表失败:', result.message)
             }
         } catch (error) {
             console.error('获取消息列表错误:', error)
             showToast('获取消息列表失败')
         }
     }

           // 格式化消息时间为yyyy-MM-dd格式
      const formatMessageTime = (dateString) => {
          if (!dateString) return ''
          
          const messageTime = new Date(dateString)
          return messageTime.toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit'
          }).replace(/\//g, '-')
      }

    // 获取应用中文名称
  const getAppChineseName = (englishName: string) => {
    const nameMap = {
      'preprocessing': '数据预处理',
      'visualization3d': '三维可视化',
      'geosteering': '地质导向',
      'fastLogPlot': '快速测井图',
      'pythonEditor': 'PythonApp',
      'multiWellCorrelation': '多井对比',
      'processingModule': '处理模块',
      'drillingMonitor': '随钻作业监控',
      'logLab': 'LogLab',
      'pythonProcessingModule': 'Python处理模块',
      'intelligentDataProcessing': '智能数据处理',
      'test': '测试',
      'demo': '测试应用'
    }
    return nameMap[englishName] || englishName
  }

      // 获取工程列表数据
    const getProjectList = async () => {
      projectLoading.value = true
      try {
        // 先获取应用列表，找到地质导向应用
        const appResponse = await axios.get(vWebApiUrl + '/api/project/project/GetHubApp')
        const appResult = appResponse.data
        
        if (!appResult.success || !appResult.data) {
          console.error('获取应用列表失败')
          return
        }
        
        // 查找地质导向应用
        const geoSteeringApp = appResult.data.find((item: any) => item.name === 'geosteering')
        
        if (!geoSteeringApp) {
          console.error('未找到地质导向应用')
          return
        }
        
        const geoSteeringAppId = geoSteeringApp.id
        console.log('找到地质导向应用ID:', geoSteeringAppId)
        
        // 使用获取到的地质导向应用ID获取工程列表
        const response = await axios.get(`${vWebApiUrl}/api/project/project/GetProjctList?appId=${geoSteeringAppId}`)
        const result = response.data
        
        if (result.success && result.data && result.data.rows) {
          projectList.value = result.data.rows.map(item => ({
            id: item.projectId,
            projectName: item.project[0]?.projectName || '未命名项目',
            createTime: item.project[0]?.createTime ? 
              new Date(item.project[0].createTime).toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
              }).replace(/\//g, '-') : '未知时间',
            wellboreNumber: item.wellboreNumber || '',
            appId: item.appId
          }))
        } else {
          console.error('获取工程列表失败:', result.message)
        }
      } catch (error) {
        console.error('获取工程列表错误:', error)
        showToast('获取工程列表失败')
      } finally {
        projectLoading.value = false
      }
    }

    // 获取应用列表
   const getHubApps = async () => {
     loading.value = true
     try {
       const response = await axios.get(vWebApiUrl + '/api/project/project/GetHubApp')       
       const result = response.data
       
       if (result.success && result.data) {
        // 确保包含地质导向，然后取其他应用组成6个
        const geoSteeringApp = result.data.find((item: any) => item.name === 'geosteering')
        const otherApps = result.data
          .filter((item: any) => item.name !== 'geosteering')
          .sort((a: any, b: any) => a.order - b.order)
        
        // 如果找到地质导向，优先添加，然后添加其他应用直到6个
        let finalApps = []
        if (geoSteeringApp) {
          finalApps.push(geoSteeringApp)
          finalApps = finalApps.concat(otherApps.slice(0, 5))
        } else {
          finalApps = otherApps.slice(0, 6)
        }
        
        gridItems.value = finalApps.map((item: any) => ({
          id: item.id,
          title: getAppChineseName(item.name),
          path: item.url,
          icon: item.icon,
          describe: item.describe
        }))
      } else {
        showToast('获取应用列表失败')
      }
    } catch (error) {
      console.error('获取应用列表错误:', error)
      showToast('网络请求失败')
    } finally {
      loading.value = false
    }
  }

  // 消息列表数据
  const messageList = ref([])
  
  // 返回按钮点击事件
  const onClickLeft = () => {
    router.back()
  }
  
    // 工程项点击事件
  const onProjectClick = (item: any) => {
    console.log('点击了项目:', item)
    showToast(`打开项目: ${item.projectName}`)
    
    // 跳转到地质导向应用，并传递项目ID
    const targetUrl = `${vWebApiUrl}/static/GeoSteering/GeoSteeringIndex.html?projectId=${item.id}&appId=${item.appId}`
    window.open(targetUrl, '_blank')
  }

  // 消息点击事件
  const onMessageClick = (message: any) => {
    // 显示消息详情对话框
    showMessageDetail(message)
    // 标记为已读
    message.isRead = true
  }

  // 显示消息详情
  const showMessageDetail = (message: any) => {
    const content = `
      <div style="text-align: left;font-size: 13px;line-height: 1.3;margin-top: -20%;">
        <div style="margin-bottom: 2px; color: #666;"><strong>发送人：</strong>${message.senderName || '系统'}</div>
        <div style="margin-bottom: 2px; color: #666;"><strong>时间：</strong>${message.time}</div>
        <div style="margin-bottom: 2px; color: #666;"><strong>类型：</strong>${getMessageTypeText(message.messageType)}</div>
        <div style="margin-bottom: 6px; color: #666;"><strong>优先级：</strong>${getPriorityText(message.priority)}</div>
        <div style="margin-top: -15%;">
          <div style="font-weight: 500; margin-bottom: 4px; color: #333; font-size: 12px;">内容：</div>
          <div style="background: #f8f9fa; padding: 6px 8px; border-radius: 4px; border-left: 3px solid #1989fa; font-size: 12px; line-height: 1.4;">
            ${message.description}
          </div>
        </div>
      </div>
    `
    
    // 使用vant的showDialog函数显示详情
    showDialog({
      title: message.title,
      message: content,
      allowHtml: true,
      confirmButtonText: '知道了'
    })
  }

  // 获取消息类型文本
  const getMessageTypeText = (type: string) => {
    const typeMap = {
      'Public': '公告',
      'Private': '私信',
      'System': '系统消息'
    }
    return typeMap[type] || type
  }

     // 获取优先级文本
   const getPriorityText = (priority: string) => {
     const priorityMap = {
       'High': '高',
       'Normal': '普通',
       'Low': '低'
     }
     return priorityMap[priority] || priority
   }

   // 获取消息图标
   const getMessageIcon = (messageType: string, priority: string) => {
     // 高优先级消息使用警告图标
     if (priority === 'High') {
       return 'warning-o'
     }
     
     // 根据消息类型返回不同图标
     const iconMap = {
       'Public': 'bullhorn-o',     // 公告 - 喇叭
       'Private': 'envelop-o',     // 私信 - 信封
       'System': 'setting-o'       // 系统消息 - 设置
     }
     return iconMap[messageType] || 'info-o'
   }

   // 获取消息图标颜色
   const getMessageIconColor = (messageType: string, priority: string) => {
     // 高优先级消息使用红色
     if (priority === 'High') {
       return '#ff4757'
     }
     
     // 根据消息类型返回不同颜色
     const colorMap = {
       'Public': '#1989fa',        // 公告 - 蓝色
       'Private': '#52c41a',       // 私信 - 绿色
       'System': '#faad14'         // 系统消息 - 橙色
     }
     return colorMap[messageType] || '#969799'
   }

  // 页面挂载时获取数据
  onMounted(() => {
    getUserInfo()
    getProjectList()
    getMessageList()
  })
  </script>
  
  <style scoped lang="less">
  .workplace-container {
    height: 100vh;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    
    .nav-bar {
      flex-shrink: 0;
    }

    .welcome-section {
      padding: 16px 20px;
      
      .welcome-text {
        font-size: 16px;
        font-weight: 500;
        color: #323233;
        line-height: 1.6;
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
          font-size: 14px;
          color: #969799;
          font-weight: 400;
        }
      }
    }
    
    .content-section {
      margin-top: 10px;
      flex: 1;
      padding: 0 16px 16px;
      overflow-y: auto;
      
      .card-container {
        background-color: #fff;
        border-radius: 12px;
        margin-bottom: 16px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        overflow: hidden;

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 20px 12px;
          border-bottom: 1px solid #f0f0f0;

          .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #323233;
            margin: 0;
          }

          .card-more {
            font-size: 14px;
            color: #1989fa;
            cursor: pointer;
            
            &:hover {
              opacity: 0.8;
            }
          }
        }

        .project-list {
          .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px 0;
          }

          .list-content {
            padding: 8px 0;
          }

          .project-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.3s ease;

            &:last-child {
              border-bottom: none;
            }

            &:hover {
              background-color: #f8f9fa;
            }

            &:active {
              background-color: #f0f1f5;
            }

            .project-content {
              flex: 1;

              .project-title {
                font-size: 16px;
                font-weight: 500;
                color: #323233;
                margin-bottom: 8px;
                line-height: 1.4;
              }

              .project-info {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .info-item {
                  display: flex;
                  align-items: center;
                  gap: 4px;
                  font-size: 13px;
                  color: #969799;
                }
              }
            }
          }

          .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            text-align: center;

            .empty-text {
              margin-top: 16px;
              font-size: 14px;
              color: #969799;
            }
          }
        }

        .message-list {
          .message-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.3s ease;

            &:last-child {
              border-bottom: none;
            }

            &:hover {
              background-color: #f8f9fa;
            }

            &:active {
              background-color: #f0f1f5;
            }

            .message-icon {
              margin-right: 12px;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background-color: rgba(25, 137, 250, 0.1);
            }

            .message-content {
              flex: 1;

              .message-title {
                font-size: 15px;
                font-weight: 500;
                color: #323233;
                margin-bottom: 4px;
                line-height: 1.4;
                display: flex;
                align-items: center;
              }

              .message-desc {
                font-size: 13px;
                color: #969799;
                margin-bottom: 6px;
                line-height: 1.4;
              }

              .message-time {
                font-size: 12px;
                color: #c8c9cc;
                display: flex;
                align-items: center;
              }
            }

            .message-status {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background-color: transparent;
              margin-left: 12px;

              &.unread {
                background-color: #ff4757;
              }
            }
          }
        }
      }
    }
  }

  /* 优化导航栏样式 */
  :deep(.van-nav-bar) {
    background-color: #1989fa;
  }
  
  :deep(.van-nav-bar__title) {
    color: #fff;
    font-weight: 500;
  }
  
  :deep(.van-nav-bar__arrow) {
    color: #fff;
  }
  
  // 响应式设计
  @media (max-width: 768px) {
    .workplace-container {
      .welcome-section {
        .welcome-text {
          font-size: 16px;
        }
      }
      
      .content-section {
        .card-container {
          .grid-container {
            gap: 10px;
            padding: 12px;
            
            .grid-item {
              padding: 12px;
              min-height: 60px;
              
              .grid-item-content {
                .grid-item-text {
                  font-size: 13px;
                }
              }
            }
          }

          .message-list {
            .message-item {
              padding: 14px 16px;

              .message-content {
                .message-title {
                  font-size: 14px;
                }

                .message-desc {
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }
  }
  </style>