export class TreeNode {
    constructor(name) {
        this.name = name;
        this.children = [];
        this.parent = null;
    }

    get level() {
        let level = 0;
        let parent = this.parent;
        while (parent) {
            level++;
            parent = parent.parent;
        }
        return level;
    }

    get hasChild() {
        return this.children.length > 0;
    }

    appendChild(node) {
        if (node instanceof TreeNode) {
            this.children.push(node);
            node.parent = this;
            return true;
        }
        return false;
    }

    removeChild(node) {
        let index = this.children.indexOf(node);
        if (index > -1) {
            this.children.splice(index, 1);
            return true;
        }
        return false;
    }

    clearChildren() {
        this.children = [];
    }

    findChild(predicate, deep = true) {
        if (this.hasChild) {
            for (let child of this.children) {
                if (predicate(child)) {
                    return child;
                }
                else if (deep) {
                    let node = child.findChild(predicate);
                    if (node) {
                        return node;
                    }
                }
            }
        }
        return null;
    }
}


export class ScopeTreeNode extends TreeNode {
    constructor(name, variablesReference, expensive) {
        super(name);
        this.variablesReference = variablesReference;
        this.expensive = expensive;
    }
}

export class VariableTreeNode extends TreeNode {
    constructor(name, value, type, evaluateName, variablesReference) {
        super(name);
        this.value = value;
        this.type = type;
        this.evaluateName = evaluateName;
        this.variablesReference = variablesReference;
    }
}