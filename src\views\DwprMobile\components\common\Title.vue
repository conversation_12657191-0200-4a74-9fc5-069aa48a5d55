<template>
    <div class="header">
        <div class="title">
            <van-icon v-if="showIcon" size="24" color="#fff" class="icon" name="arrow-left" @click="clickIcon" />
            <span>{{name}}</span>
        </div>
        <van-search
        v-if="showSearch"
            v-model="searchValue"
            placeholder="Search"
            input-align="center"
            @search="handleSearch"
            @click-left-icon="handleSearch"
            />
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const emits = defineEmits<{
    (e: 'clickIcon'): void
}>()
interface Props {
  name: string,
  showIcon?:boolean,
  showSearch?:boolean
}
const props = withDefaults(defineProps<Props>(), {
  name: 'Wells',
  showIcon: false,
  showSearch: false
})

const searchValue = ref<string>('')
const handleSearch = (val: string) => {
  console.log(val, 'search', searchValue.value)
}

const clickIcon = () => {
    emits('clickIcon')
}
</script>

<style scoped lang="scss">
.header {
    width: 100%;
    background: #4472C4;
}
.title {
    width: 100%;
    height: 2.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}
.title span {
    font-weight: 500;
    font-size: 1.5rem;
    color: #FFFFFF;
}
.title .icon {
    position: absolute;
    left: 1rem;
}
.van-search {
    background: #4472C4;
    :deep(.van-search__content) {
        border-radius: .5rem;
    }
}

</style>
