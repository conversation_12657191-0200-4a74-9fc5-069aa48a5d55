<template>
  <!-- 布局容器 -->
  <div class="layout-box">
    <!-- 工具栏区域 -->
    <TabMenu ref="tabMenuRef" @add-tab="handleAddTab" :tabId="curveTabId" />
    <!-- Dock布局容器 -->
    <div class="dock-container">
      <ReactDockLayout ref="dockRef" v-bind="dockProps" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { applyReactInVue, applyVueInReact } from "veaury";
import * as React from "react";
import { DockLayout } from "rc-dock";
import type { TabData, LayoutData, DockMode, PanelData } from "rc-dock";

// 引入自定义标签页内容组件
import Project from "./Project/index.vue";
import TabMenu from "./TabMenu.vue";
import CurveEdit from "./CurveEdit/index.vue";
import Property from "@/components/property.vue";
import AdmAndDshift from "./AdmAndDshift/index.vue";
// 引入rc-dock样式
import "rc-dock/dist/rc-dock.css";
// 状态管理
const dockRef = ref(); // Dock布局引用
let tabCounter = 1; // 标签页计数器
const ProjectRef = ref();
const outputResultRef = ref();
let curveTabId = ref();
const tabMenuRef = ref<InstanceType<typeof TabMenu> | null>(null);
const PropertyRef = ref();
interface TabItem {
  label: string;
  value: string;
  children?: Array<{
    label: string;
    value: string;
    icon?: string;
  }>;
}
interface FormOptionItem {
  label: string;
  value: any;
  type: string;
  group?: string; // 添加问号改为可选属性
  dicData?: Array<{ label: string; value: any }>;
  // ... 其他属性
}
const formOption = ref({
  formName: "form_group",
  options: [
    {
      label: "name",
      value: "jack",
      group: "config",
      type: "input",
    },
    {
      label: "age",
      value: 14,
      group: "config",
      type: "number",
    },
    {
      label: "sex",
      value: 0,
      group: "config",
      type: "select",
      dicData: [
        { label: "男", value: 0 },
        { label: "女", value: 1 },
      ],
    },
    {
      label: "color",
      value: "#000000",
      type: "color",
      group: "task",
    },
    {
      label: "date",
      value: "2025-04-25",
      type: "date",
      group: "config",
    } as FormOptionItem,
  ],
});
const newTabs = ref<TabItem[]>([]);
// 使用Veaury将Vue组件转换为React组件

const CurveEditContent = applyVueInReact(CurveEdit, {
  forwardRef: true, // 启用 ref 转发
  useReactWrapper: true,
});

const ProjectContent = applyVueInReact(Project, {
  forwardRef: true, // 启用 ref 转发
  useReactWrapper: true,
});
const AdmAndDshiftContent = applyVueInReact(AdmAndDshift, {
  forwardRef: true, // 启用 ref 转发
  useReactWrapper: true,
});
const PropertyContent = applyVueInReact(Property, {
  forwardRef: true, // 启用 ref 转发
  useReactWrapper: true,
});

/**
 * 添加新标签页
 * 创建新的标签页并添加到主布局区域
 */
// DockLayout.vue
/**
 * 添加新标签页
 * 创建新的标签页并添加到主布局区域
 */
function addNewTab(type: string, info: { id: string }, label: string) {
  const layout = dockRef.value?.__veauryReactRef__;
  if (!layout) {
    console.log("Layout instance not found");
    return;
  }
  // if (type === "1") {
  //   return;
  // }
  // if (switchToTab(eidtorId)) {
  //   console.log("标签页已经创建");
  //   return;
  // }
  const currentCounter = tabCounter;

  const newTab: TabData = {
    id: info.id,
    //title: label,
    title: React.createElement(
      "div",
      {
        style: { padding: "3px 14px", cursor: "pointer" },
        onClick: (e: React.MouseEvent) => {
          //e.stopPropagation();
          handleTabClick("curveEdit", info.id);
        },
      },
      label
    ),

    content: React.createElement(CurveEditContent, {
      index: currentCounter,
      title: label,
      tabId: info.id,
      info: info,
      id: curveTabId.value,
      // 事件处理
      onClick: (count: number) => {},

      onCurveTabClose: (tabId: string) => {
        if (ProjectRef.value) {
          ProjectRef.value.__veauryVueRef__.$refs.resourceTreeRef.setCheckedKeys(
            tabId,
            false
          );
        }
      },
    }),

    closable: true,
    group: "main",
    cached: true,
  };
  layout.dockMove(newTab, "main", "middle");
  handleVisibleTabs("curveEdit");
  curveTabId.value = info.id;
  tabCounter++;
}
function findActiveTabId(box: any): string | null {
  if (box.children) {
    for (const child of box.children) {
      if (child.activeId && child.group === "main") {
        return child.activeId;
      }
      if (child.children) {
        const result = findActiveTabId(child);
        if (result) return result;
      }
    }
  }
  return null;
}

function switchToTab(tabId: string) {
  const layout = dockRef.value?.__veauryReactRef__;
  if (!layout) {
    console.log("Layout instance not found");
    return;
  }

  // 查找指定的标签页
  const tab = layout.find(tabId);
  if (tab) {
    // 将标签页移动到当前激活的位置
    layout.updateTab(tab.id, tab);
    return true;
  }
  return false;
}
function removeTab(tabId: string) {
  const layout = dockRef.value?.__veauryReactRef__;
  if (!layout) {
    return;
  }
  const tab = layout.find(tabId);
  layout.dockMove(tab, "", "");
}
function changeCurrentNode(id: string) {
  if (ProjectRef.value) {
    ProjectRef.value.__veauryVueRef__.$refs.resourceTreeRef.setCheckNode(id);
    //ProjectRef.value.__veauryVueRef__.setCheckNode(id);
  } else {
    //console.error("ResourceTree 组件实例未找到");
  }
}

function updateOutput(text: string) {
  if (outputResultRef.value) {
    outputResultRef.value.__veauryVueRef__.updateOutput(text);
  } else {
    //console.error("ResourceTree 组件实例未找到");
  }
}

// 布局样式配置
const layoutStyle = computed(() => ({
  width: "100%",
  height: "calc(100vh - 40px)",
  background: "#1E1E1E",
  color: "#CCCCCC",
}));

/**
 * 分组配置
 * 定义不同分组的行为特性
 */
const groupConfig = {
  default: {
    floatable: true, // 是否可浮动
    maximizable: true, // 是否可最大化
    tabLocked: false, // 标签是否锁定
    newWindow: true, // 是否允许新窗口
    preferredFloatWidth: [200, 800],
    preferredFloatHeight: [200, 600],
  },
  main: {
    floatable: true,
    maximizable: true,
    tabLocked: false,
    newWindow: true,
    preferredFloatWidth: [400, 1000],
    preferredFloatHeight: [300, 800],
  },
};

// Dock组件属性配置
const dockProps = computed(() => ({
  defaultLayout: defaultLayout, // 默认布局
  style: layoutStyle.value,
  groups: groupConfig, // 分组配置
  dropMode: "default", // 拖放模式
  mode: "horizontal", // 布局模式
  maximizable: true, // 允许最大化
  floatable: true, // 允许浮动
  newWindow: true, // 允许新窗口
  disableDock: false, // 启用停靠
  draggable: true, // 允许拖拽
  resizable: true, // 允许调整大小
  onLayoutChange: (layout: LayoutData) => {
    // 定义一个递归函数来遍历布局数据
    const findActiveIds = (box: any) => {
      if (box.children) {
        box.children.forEach((child: any) => {
          if (child.activeId && child.group === "main") {
            changeCurrentNode(child.activeId);
          }
          if (child.children) {
            findActiveIds(child);
          }
        });
      }
    };

    // 从 dockbox 开始遍历
    if (layout.dockbox) {
      findActiveIds(layout.dockbox);
    }
  },
}));

// 默认内容组件
const DefaultContent = (text: string) => () =>
  React.createElement("div", null, text);

/**
 * 默认布局配置
 * 定义初始布局结构，包括：
 * - 左侧资源管理器
 * - 中间主编辑区
 * - 底部面板（问题、输出、终端）
 * - 右侧大纲视图
 */
const defaultLayout: LayoutData = {
  dockbox: {
    mode: "horizontal" as DockMode,
    children: [
      {
        mode: "vertical" as DockMode,
        size: 250,
        children: [
          {
            id: "left",
            group: "left",
            tabs: [
              {
                id: "explorer",
                title: "Project",
                content: React.createElement(ProjectContent, {
                  ref: (instance: any): void => {
                    // 保存React包装器的引用
                    ProjectRef.value = instance;
                  },
                  onClick: (
                    type: string,
                    info: { id: string },
                    label: string
                  ) => {
                    //判断是否是已经打开了的标签页
                    // if (!switchToTab(id)) {
                    //   addNewTab(text, id, label);
                    // }
                    if (!switchToTab(info.id) && type !== "1") {
                      tabMenuRef.value?.handleEditTabs();
                      addNewTab(type, info, label);

                      //addLeftTab();
                    }
                  },
                  onTabClose: (tabId: string) => {
                    removeTab(tabId);
                  },
                }),
                closable: false,
                cached: true,
                group: "left",
              },
            ],
          },
        ],
      },
      {
        mode: "vertical" as DockMode,
        size: 1000,
        children: [
          {
            id: "main",
            group: "main",
            tabs: [],
            panelLock: {
              panelStyle: "main",
              minWidth: 400,
              minHeight: 200,
            },
          },
        ],
      },
      // {
      //   mode: "vertical" as DockMode,
      //   size: 200,
      //   children: [
      //     {
      //       id: "right",
      //       group: "right",
      //       tabs: [
      //         {
      //           id: "property",
      //           title: "property",
      //           content: React.createElement(PropertyContent, {
      //             ref: (instance: any): void => {
      //               // 保存React包装器的引用
      //               PropertyRef.value = instance;
      //             },
      //             formOption: formOption.value,
      //             onUpdateAvueForm: (value, column, formName) => {
      //               //获取表单更新值
      //               console.log(value, column.prop, formName);
      //             },
      //           }),
      //           closable: false,
      //         },
      //       ],
      //     },
      //   ],
      // },
    ],
  },
};
//
const handleTabClick = (tabActiveId: string, eidtorId: string) => {
  if (eidtorId) {
    curveTabId.value = eidtorId;
  }

  handleVisibleTabs(tabActiveId);
};
//根据visibleTabs的值来判断是否显示对应的顶部标签页
const handleVisibleTabs = (val: string) => {
  if (tabMenuRef.value) {
    tabMenuRef.value.setSelectedTab(val);
  }
};
const handleAddTab = (tab: string) => {
  if (tab === "admAndDshift") {
    addAdmAndDshiftTab();
  } else if (tab === "curveEdit") {
    const layout = dockRef.value?.__veauryReactRef__;
    if (!layout) {
      console.log("Layout instance not found");
      return;
    }
    let arr = [];
    let box = layout.getLayout().dockbox;
    if (box.children) {
      for (const child of box.children) {
        if (child.activeId && child.group === "main") {
          for (const tab of child.tabs) {
            if (tab.id !== "admAndDshift") {
              arr.push(tab.id);
            }
          }
        }
      }
    }
    // console.log(layout.getLayout().dockbox);
    const tab = layout.find(arr[0]);
    layout.updateTab(arr[0], tab);
  }
};
const addAdmAndDshiftTab = () => {
  const layout = dockRef.value?.__veauryReactRef__;

  if (!layout) {
    console.log("Layout instance not found");
    return;
  }

  if (layout.find("wellLog")) {
    //console.log("标签页已经创建");
    return;
  }
  const newTab: TabData = {
    id: "admAndDshift",
    title: React.createElement(
      "div",
      {
        style: { padding: "3px 14px", cursor: "pointer" },
        onClick: (e: React.MouseEvent) => {
          //e.stopPropagation();
          handleTabClick("admAndDshift", "");
        },
      },
      "ADM & DSHIFT"
    ),
    content: React.createElement(AdmAndDshiftContent, {
      // 事件处理
      onClick: (count: number) => {},
      onAdmTabClose: () => {
        tabMenuRef.value?.handleRemoveTab("admAndDshift");
      },
    }),

    closable: true,
    group: "main",
    cached: true,
  };
  layout.dockMove(newTab, "main", "middle");
};

// 更新property组件配置
const initProperty = () => {
  if (PropertyRef.value) {
    formOption.value = {
      formName: "form",
      options: [
        {
          label: "name",
          value: "jack",

          type: "input",
        },
        {
          label: "age",
          value: 14,

          type: "number",
        },
        {
          label: "sex",
          value: 0,

          type: "select",
          dicData: [
            { label: "男", value: 0 },
            { label: "女", value: 1 },
          ],
        },
        {
          label: "color",
          value: "#000000",
          type: "color",
        },
        {
          label: "date",
          value: "2025-04-25",
          type: "date",
        },
      ],
    };
    PropertyRef.value.__veauryVueRef__.init(formOption.value);
  }
};
// const addLeftTab = () => {
//   const layout = dockRef.value?.__veauryReactRef__;

//   if (!layout) {
//     console.log("Layout instance not found");
//     return;
//   }

//   if (layout.find("wellLog")) {
//     //console.log("标签页已经创建");
//     return;
//   }
//   const newTab: TabData = {
//     id: "wellLog",
//     title: "well log",
//     content: React.createElement(newCollapse, {
//       // 事件处理
//       onClick: (count: number) => {},
//     }),

//     closable: false,
//     group: "left",
//     cached: true,
//   };
//   layout.dockMove(newTab, "explorer", "after-tab");
//   layout.updateTab("explorer");
// };
onMounted(() => {
  //addLeftTab();
});
// 使用Veaury将React组件转换为Vue组件
const ReactDockLayout = applyReactInVue(DockLayout);
</script>

<style lang="less">
/* 布局容器样式 */
.layout-box {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: unset !important;
  background: var(--theme-bg);
  .dock-layout {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: unset !important;
    background: var(--dock-divider-bg) !important;
  }
  .dock-layout > .dock-box {
    height: calc(100% - 105px);
  }

  /* 工具栏样式 */
  .toolbar {
    height: 40px;
    background: #2d2d2d;
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid #1e1e1e;
  }

  /* 获取编辑器内容按钮样式 */
  .get-value-btn {
    background: #0e639c;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    margin-left: 10px;
  }

  .get-value-btn:hover {
    background: #1177bb;
  }

  /* rc-dock主题变量 */
  :root {
    --dock-color: #cccccc;
    --dock-background: #1e1e1e;
    --dock-tab-color: #969696;
    --dock-tab-active-color: #ffffff;
    --dock-tab-background: #2d2d2d;
    --dock-tab-active-background: #1e1e1e;
    --dock-border-color: #252526;

    /* 指示器样式 */
    --dock-indicator-background: #0e639c;
    --dock-indicator-border: #1177bb;
    --dock-indicator-size: 40px;
    --dock-indicator-opacity: 0.9;
  }
  /* 全局隐藏所有面板的最大化按钮 */
  .dock-panel-max-btn {
    display: none !important;
  }
  .dock-divider {
    flex: 0 0 3px;
    background: var(--dock-divider-bg);
    transform: scaleY(1) !important;
  }
  .dock-panel {
    border: 0;
    background: var(--dock-pane-bg);
    border-radius: 10px 10px 0 0;
    // border-left: 0;
    // border-top: 0;
    // border-right: 0;
  }
  .dock-top .dock-bar {
    border-bottom: 0 !important;
    background: transparent;
    padding-left: 0;
  }
  .dock-tab-active {
    background: var(--left-tab-active-bg) !important;
    border-radius: 10px 10px 0 0;
  }
  .dock-top .dock-ink-bar {
    height: 0;
  }
  .dock-tab {
    border-bottom: 0;
    background: transparent;
    font-size: 12px;
  }
  .dock-tab > div {
    padding: 3px 14px;
  }
  .dock {
    //background: var(--dock-bg);
  }
  .dock-pane-cache {
    background: var(--theme-bg-white);
    border-radius: 0 10px 0 0;
    overflow: auto;
  }
  .dock-tab-active {
    transform: none;
    &:hover {
      transform: none;
    }
    .dock-tab-close-btn {
      display: block;
    }
  }
  .dock-tab:hover .dock-tab-close-btn,
  .dock-tab-close-btn:focus {
    color: var(--font-color-white);
  }
  .dock-tab-close-btn {
    background: var(--btn-close-bg);
    border-radius: 50%;
    transform: scale(0.8, 0.8);
    color: #fff;
    right: 10px;
    display: none;
    width: 18px;
    top: 1px;
  }
  .dock-nav {
    height: 24px;
  }
  .dock-nav-list {
    transform: none !important;
    position: static;
    .dock-tab {
      position: static;
    }
  }
  .dock-nav-operations {
    display: none;
  }
  .dock-panel.dock-style-main .dock-tab {
    background: var(--dock-tab-bg);
    border-radius: 10px 10px 0 0;
    & > div {
      padding: 0;
    }
  }
  .dock-panel.dock-style-main {
    .dock-tab-active {
      background: var(--theme-bg-white) !important;
      color: var(--font-color-black);
    }
    .dock-content-animated {
      background: var(--theme-bg-white);
      border-radius: 10px 10px 0 0;
    }
  }
  .dock-style-left .dock-bar {
    padding-left: 8px;
  }
}
</style>
