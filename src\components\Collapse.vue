<template>
  <div class="demo-collapse">
    <el-collapse v-model="activeNames">
      <el-collapse-item title="Basic Log" name="1">
        <el-button type="primary" @click="handle">track</el-button>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
const emit = defineEmits(["click"]);
const activeNames = ref(["1"]);
const handle = () => {
  emit("click", " ", "1", "track");
};
</script>

<style lang="less"></style>
