declare module 'veaury' {
  import type { Plugin } from 'vue'
  
  export interface VeauryOptions {
    react18?: {
      createRoot?: boolean
    }
  }

  const plugin: Plugin & {
    install: (app: any, options?: VeauryOptions) => void
  }

  export default plugin

  export function applyVueInReact(component: any, props?: any): any
  export function applyReactInVue(component: any, props?: any): any
  export function createReactWrapper(component: any): any
  export function createVueWrapper(component: any): any
}

// 全局 JSX 命名空间
declare global {
  namespace JSX {
    interface Element {
      [key: string]: any
    }
    interface ElementClass {
      $props: any
    }
    interface IntrinsicElements {
      [elem: string]: any
    }
  }
} 