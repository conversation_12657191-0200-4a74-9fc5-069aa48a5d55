<template>
  <div class="wells">
    <div class="wells-header">
      <Title name="Wells" :showIcon="false" showSearch />
    </div>
    <div class="wells-content">
       <van-pull-refresh v-model="refreshing" @refresh="onRefresh"
        pulling-text="Pull to refresh..."
        loosing-text="Release to refresh..."
        loading-text="Refreshing..."
        success-text="Refresh successful"
       >
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="No More"
            loading-text="Loading..."
            @load="onLoad"
          >
           <WellList :wellList="list" />
          </van-list>
        </van-pull-refresh>
    </div>
  </div>
</template>

<script setup lang="ts">
import Title from "@/views/DwprMobile/components/common/Title.vue";
import ListContainer from "@/views/DwprMobile/components/common/ListContainer.vue";
import WellList from "@/views/DwprMobile/components/wells/WellList.vue";
import { ref } from "vue";
const list = ref<any>([]);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);

const onLoad = () => {
  setTimeout(() => {
    if (refreshing.value) {
      list.value = [];
      refreshing.value = false;
    }

    for (let i = 0; i < 10; i++) {
      list.value.push( {
        id: Math.random().toString(36).substring(2),
        name: 'PY4-2-A',
        date: '7-23',
        status: 1,
        remark: '备注'
    },);
    }
    loading.value = false;

    if (list.value.length >= 40) {
      finished.value = true;
    }
  }, 1000);
};

const onRefresh = () => {
  // 清空列表数据
  finished.value = false;

  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  onLoad();
};
</script>

<style scoped lang="less">
.wells {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.wells-content {
    flex: 1;
    overflow-y: auto;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none;
    -ms-overflow-style: none;
    
    &::-webkit-scrollbar {
        display: none;
    }
}
</style>
