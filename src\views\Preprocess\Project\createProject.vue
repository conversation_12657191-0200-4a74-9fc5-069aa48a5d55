<template>
  <div>
    <el-dialog
      :title="title"
      v-model="dialogVisible"
      width="550px"
      :close-on-click-modal="false"
    >
      <el-form :model="form" ref="formRef" :rules="rules">
        <el-form-item
          label="name"
          prop="name"
          class="form-item"
          label-width="80px"
        >
          <el-input
            v-model="form.name"
            placeholder="Please enter project name"
            class="input-field"
          ></el-input>
        </el-form-item>
        <!-- 井 -->
        <el-form-item
          v-if="isEdit !== 1"
          label="Well"
          class="form-item"
          label-width="80px"
          prop="wellId"
        >
          <el-select
            v-model="form.wellId"
            placeholder="Please select well"
            filterable
          >
            <el-option
              v-for="well in wellList"
              :key="well.id"
              :label="well.wellName"
              :value="well.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Remark" class="form-item" label-width="80px">
          <el-input
            v-model="form.remark"
            placeholder="Please enter remark"
            class="input-field"
          ></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">Cancel</el-button>
          <el-button type="primary" @click="createProject(formRef)"
            >Ok</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, reactive } from "vue";
import { GetWellListAll, CreateProject, UpdateProject } from "@/api/project";
import { ElMessage } from "element-plus";
const emit = defineEmits(["project-created"]);
const form = reactive({
  name: "",
  wellId: "",
  remark: "",
});
const rules = reactive({
  wellId: [{ required: true, message: "请选择井", trigger: "change" }],
  name: [{ required: true, message: "请输入工程名称", trigger: "blur" }],
});
const projectId = ref("");
const title = ref("");
const isEdit = ref();
const wellList = ref([]);
const dialogVisible = ref(false);
const formRef = ref(null);
function openDialog(node, id, type) {
  dialogVisible.value = true;
  isEdit.value = type;
  if (type === 1) {
    title.value = "Edit Project";
    form.name = node.data.name;
    projectId.value = id;
  } else {
    title.value = "Create Project";
    getWellList();
  }
}
// 表单验证
const validateForm = async (formEl) => {
  if (!formEl) return false;
  return await formEl.validate();
};
// 统一表单提交入口
async function createProject(formEl) {
  if (!(await validateForm(formEl))) return;
  if (isEdit.value === 1) {
    await handleUpdate();
  } else {
    await handleCreate();
  }
}
// 创建工程
const handleCreate = async () => {
  const urlParams = new URLSearchParams(window.location.search);
  const formData = new FormData();
  formData.append("name", form.name);
  formData.append("wellId", form.wellId);
  formData.append("remark", form.remark);
  formData.append("appId", urlParams.get("appId"));
  try {
    const res = await CreateProject(formData);
    if (res.success) {
      emit("project-created", res.data);
      window.history.pushState({}, "", `?id=${res.data}`);
      ElMessage.success("创建成功");
      dialogVisible.value = false;
    }
  } catch (error) {
    ElMessage.error(error.message);
  }
};
// 编辑工程
const handleUpdate = async () => {
  try {
    const res = await UpdateProject({
      id: projectId.value,
      name: form.name,
      remark: form.remark,
    });
    if (res.success) {
      ElMessage.success("修改成功");
      emit("project-created", projectId.value);
      dialogVisible.value = false;
    }
  } catch (error) {
    ElMessage.error(error.message);
  }
};
function getWellList() {
  GetWellListAll().then((res) => {
    if (res.success) {
      wellList.value = res.data;
    } else {
      ElMessage({
        message: res.message,
        type: "error",
      });
    }
  });
}
defineExpose({
  openDialog,
});
</script>
<style lang="less" scoped></style>
