var history = [];

var seq = 0;
function getSeq() {
    return ++seq;
}

function get_dap_msg(obj) {
    history.push(obj);
    let body = JSON.stringify(obj);
    let length = sizeof(body);
    let header = 'Content-Length: ' + length + '\r\n\r\n';
    let msg = header + body;
    return msg;
}

function sizeof(str) {
    let total = 0;
    for (let i = 0, len = str.length; i < len; i++) {
        let charCode = str.charCodeAt(i);
        if (charCode <= 0x007f) {
            total += 1;
        } else if (charCode <= 0x07ff) {
            total += 2;
        } else if (charCode <= 0xffff) {
            total += 3;
        } else {
            total += 4;
        }
    }
    return total
}

export function findHistory(seq) {
    for (let i = 0; i < history.length; i++) {
        let obj = history[i];
        if (obj.seq == seq) {
            return obj;
        }
    }
    return null;
}

export function init_request() {
    let request = {
        seq: getSeq(),
        type: 'request',
        command: 'initialize',
        arguments: {
            clientName: 'monaco',
            adapterID: 'debugpy1.6.0',
            columnsStartAt1: true
        }
    }
    return get_dap_msg(request);
}

export function launch_request(noDebug, filepath) {
    let request = {
        seq: getSeq(),
        type: 'request',
        command: 'launch',
        arguments: {
            noDebug: noDebug,
            program: filepath
        }
    }
    return get_dap_msg(request);
}

export function set_breakpoints_request(filepath, breakpoints) {
    let request = {
        seq: getSeq(),
        type: 'request',
        command: 'setBreakpoints',
        arguments: {
            source: {
                path: filepath
            },
            breakpoints: breakpoints
        }
    }
    return get_dap_msg(request);
}

export function next_request(threadId) {
    let request = {
        seq: getSeq(),
        type: 'request',
        command: 'next',
        arguments: {
            threadId: threadId
        }
    }
    return get_dap_msg(request);
}

export function step_in_request(threadId) {
    let request = {
        seq: getSeq(),
        type: 'request',
        command: 'stepIn',
        arguments: {
            threadId: threadId
        }
    }
    return get_dap_msg(request);
}

export function step_out_request(threadId) {
    let request = {
        seq: getSeq(),
        type: 'request',
        command: 'stepOut',
        arguments: {
            threadId: threadId
        }
    }
    return get_dap_msg(request);
}

export function configure_done_request() {
    let request = {
        seq: getSeq(),
        type: 'request',
        command: 'configurationDone'
    }
    return get_dap_msg(request);
}

export function stack_trace_request(threadId, startFrame = 0, levels = 20) {
    let request = {
        seq: getSeq(),
        type: 'request',
        command: 'stackTrace',
        arguments: {
            threadId: threadId,
            startFrame: startFrame,
            levels: levels
        }
    }
    return get_dap_msg(request);
}

export function scopes_request(frameID) {
    let request = {
        seq: getSeq(),
        type: 'request',
        command: 'scopes',
        arguments: {
            frameId: frameID
        }
    }
    return get_dap_msg(request);
}

export function variables_request(variableReference) {
    let request = {
        seq: getSeq(),
        type: 'request',
        command: 'variables',
        arguments: {
            variablesReference: variableReference
        }
    }
    return get_dap_msg(request);
}

export function evaluate_request(expression, frameId, context) {
    let request = {
        seq: getSeq(),
        type: 'request',
        command: 'evaluate',
        arguments: {
            expression: expression,
            frameId: frameId,
            context: context
        }
    }
    return get_dap_msg(request);
}

export function breakpoint_obj(line) {
    let breakpoint = {
        line: line
    }
    return breakpoint;
}

export function read_dap_objs(msg) {    
    let rex = /{.*}/g;
    let info = msg.match(rex);
    if (info) {
        let objs = [];
        for (let i = 0; i < info.length; i++) {
            let obj = JSON.parse(info[i]);
            console.log(obj);
            objs.push(obj);
        }
        if (objs.length > 0) {
            return objs;
        }
    }
    return null;
}