<template>
<div class="workbench-card">
    <div class="workbench-card-title">
        <span>Statistics</span>
    </div>
    <div class="workbench-card-content">
        <div class="workbench-card-content-item" v-for="value in workbenchCardItems" :key="value.name">
            <van-icon :name="value.icon"  size="60" />
            <div class="workbench-card-number">{{value.number}}</div>
            <div class="workbench-card-name">{{value.name}}</div>
            <div class="line"></div>
        </div>
    </div>
</div>   
</template>

<script setup lang="ts">
import { ref } from 'vue'
interface WorkbenchCardItem {
    icon: string
    number: number
    name: string
}
const workbenchCardItems = ref<WorkbenchCardItem[]>([
    {
        icon: '/src/icon/dwprMobile/workbench-card-total.svg',
        number: 3000,
        name: 'Total Well Counts'
    },
    {
        icon: '/src/icon/dwprMobile/workbench-opt-wells.svg',
        number: 2906,
        name: 'Operating Wells'
    },
    {
        icon: '/src/icon/dwprMobile/workbench-card-project.svg',
        number: 1000,
        name: 'Projects'
    }
])

</script>

<style scoped lang="scss">
.workbench-card {
    height: 10.6875rem;
    width: 100%;
    background-color: #4472C4;
    border-radius: .375rem;
    padding: .625rem;
}
.workbench-card-content {
    display: flex;
    justify-content: space-between;
}
.workbench-card-title {
    font-weight: 500;
    font-size: 1.25rem;
    color: #FFFFFF;
}
.workbench-card-content-item {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.workbench-card-number {
    font-weight: 500;
    font-size: 1.5rem;
    color: #FFFFFF;
}
.workbench-card-name {
    font-weight: 500;
    font-size: .75rem;
    color: #FFFFFF;
}
.line {
    position: absolute;
    right: 0;
    top: 0;
    width: .0625rem;
    height: 6.25rem;
    background-color: #A7C7FF;
}
.workbench-card-content-item:last-child {
    .line {
        display: none;
    }
}
</style>
