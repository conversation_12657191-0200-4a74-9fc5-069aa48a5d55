import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import veauryVitePlugins from 'veaury/vite/index'

// https://vitejs.dev/config/
export default defineConfig({
  base:'/static/app/',
  plugins: [
    veauryVitePlugins({
      type: 'vue',
      // @ts-ignore
      configuration: {
        useVueInReact: true,
        useReactInVue: true
      }
    })
  ],
  resolve: {
    alias: {
      // @ts-ignore
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },  
})
