import request from '@/js/request.js'

// 曲线预览
export function curvePreview(data) {
  return request({
    url: '/loghub/preview/dataset',
    method: 'get',
    params: data,
  })
}

export function curvePreviewSingle(data) {
	return request({
	  url: '/loghub/preview/channel',
	  method: 'get',
	  params: data,
	})
}

export function getChannelToFrontDB(data) {
	return request({
		url: '/loghub/preview/channelToFrontDB',
		method: 'get',
		params: data,
	})
}

export function getSamples(data) {
	return request({
		url: '/loghub/preview/getSamples',
		method: 'get',
		params: data,
	})
}

export function loadChannelInfo(data) {
	return request({
		url: '/loghub/preview/loadChannelInfo',
		method: 'get',
		params: data,
	})
}

export function loadChannelChunk(data) {
	return request({
		url: '/loghub/preview/loadChannelChunk',
		method: 'get',
		params: data,
		timeout: 50000
	}) 
}