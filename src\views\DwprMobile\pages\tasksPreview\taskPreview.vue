<template>
  <div class="task-preview-container">
    <div class="nav">
        <van-nav-bar
            title="任务预览"
            left-text="返回"
            left-arrow
            @click-left="handleBack"
        />
    </div>
    <div class="content">
        <!-- 预览内容区域 -->
        <div class="preview-wrapper">
            <h3>预览内容</h3>
            <p>这里是预览内容区域，可以根据需要替换为实际的预览内容。</p>
        </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useShowTabbarStore } from '@/stores/useShowTabbar.ts'

const router = useRouter()
const { setShowTabbar } = useShowTabbarStore()

// 返回按钮处理
const handleBack = () => {
  router.back()
}

// 生命周期
onMounted(() => {
  setShowTabbar(false)
})
onUnmounted(() => {
  setShowTabbar(true)
})
</script>

<style scoped lang="less">
.task-preview-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    background-color: #f5f5f5;
    overflow: hidden;

    // 强制横屏显示
    @media screen and (orientation: portrait) {
        transform: rotate(90deg);
        transform-origin: center center;
        width: 100%;
        height: 100%;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9999;
    }
}

.nav {
    flex-shrink: 0;
    z-index: 100;

    :deep(.van-nav-bar) {
        background-color: #ffffff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        height: 46px;
    }

    :deep(.van-nav-bar__title) {
        font-size: 18px;
        font-weight: 600;
        color: #333333;
    }

    :deep(.van-nav-bar__left) {
        .van-icon {
            color: #333333;
            font-size: 20px;
        }

        .van-nav-bar__text {
            color: #333333;
            font-size: 16px;
        }
    }
}

.content {
    flex: 1;
    background-color: #ffffff;
    overflow: auto;
    padding: 20px;

    .preview-wrapper {
        text-align: center;
        padding: 40px 20px;

        h3 {
            font-size: 24px;
            color: #333333;
            margin-bottom: 16px;
            margin-top: 0;
        }

        p {
            font-size: 16px;
            color: #666666;
            line-height: 1.6;
            margin: 0;
        }
    }
}

// 移动端横屏适配
@media screen and (orientation: landscape) {
    .task-preview-container {
        .nav {
            :deep(.van-nav-bar__title) {
                max-width: calc(100% - 140px);
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .content {
            padding: 16px 20px;
        }
    }
}

// 小屏幕设备适配
@media screen and (max-width: 768px) {
    .nav {
        :deep(.van-nav-bar) {
            height: 44px;
        }

        :deep(.van-nav-bar__title) {
            font-size: 16px;
        }

        :deep(.van-nav-bar__left) {
            .van-icon {
                font-size: 18px;
            }

            .van-nav-bar__text {
                font-size: 14px;
            }
        }
    }

    .content {
        padding: 16px;
    }
}
</style>