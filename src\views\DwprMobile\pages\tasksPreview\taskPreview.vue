<template>
  <div class="task-preview-container">
    <!-- 导航栏 -->
    <div class="nav-bar">
      <!-- 返回按钮 -->
      <div class="nav-left" @click="handleBack">
        <svg class="back-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="nav-title">{{ title }}</span>
      </div>

      <!-- 设置按钮 -->
      <div class="nav-right" @click="handleSettings">
        <svg class="settings-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
          <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" stroke="currentColor" stroke-width="2"/>
        </svg>
      </div>
    </div>

    <!-- 预览内容区域 -->
    <div class="preview-content">
      <div class="content-wrapper">
        <!-- 这里放置预览内容 -->
        <slot name="preview-content">
          <div class="default-content">
            <h3>预览内容</h3>
            <p>这里是预览内容区域，可以根据需要替换为实际的预览内容。</p>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

// 定义 props
interface Props {
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '任务预览'
})

// 定义 emits
const emit = defineEmits<{
  back: []
  settings: []
}>()

const router = useRouter()

// 返回按钮处理
const handleBack = () => {
  emit('back')
  // 如果没有监听 back 事件，则默认路由返回
  router.back()
}

// 设置按钮处理
const handleSettings = () => {
  emit('settings')
  console.log('设置按钮被点击')
}
</script>

<style scoped lang="less">
.task-preview-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  overflow: hidden;

  // 强制横屏显示
  @media screen and (orientation: portrait) {
    transform: rotate(90deg);
    transform-origin: center center;
    width: 100vh;
    height: 100vw;
    position: fixed;
    top: 0;
    left: 0;
  }
}

.nav-bar {
  height: 60px;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
  flex-shrink: 0;

  .nav-left {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.7;
    }

    &:active {
      opacity: 0.5;
    }
  }

  .back-icon {
    width: 24px;
    height: 24px;
    color: #333333;
    margin-right: 12px;
  }

  .nav-title {
    font-size: 18px;
    font-weight: 600;
    color: #333333;
    user-select: none;
  }

  .nav-right {
    cursor: pointer;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.7;
    }

    &:active {
      opacity: 0.5;
    }
  }

  .settings-icon {
    width: 24px;
    height: 24px;
    color: #666666;
  }
}

.preview-content {
  flex: 1;
  overflow: auto;
  background-color: #ffffff;

  .content-wrapper {
    padding: 20px;
    min-height: 100%;
  }

  .default-content {
    text-align: center;
    padding: 40px 20px;

    h3 {
      font-size: 24px;
      color: #333333;
      margin-bottom: 16px;
    }

    p {
      font-size: 16px;
      color: #666666;
      line-height: 1.6;
    }
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .nav-bar {
    height: 50px;
    padding: 0 16px;

    .nav-title {
      font-size: 16px;
    }

    .back-icon,
    .settings-icon {
      width: 20px;
      height: 20px;
    }
  }

  .preview-content {
    .content-wrapper {
      padding: 16px;
    }

    .default-content {
      padding: 30px 16px;

      h3 {
        font-size: 20px;
      }

      p {
        font-size: 14px;
      }
    }
  }
}

// 横屏模式特殊样式
@media screen and (orientation: landscape) {
  .task-preview-container {
    .nav-bar {
      .nav-title {
        max-width: calc(100vw - 200px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>