import * as THREE from 'three';

export class ThreeUtils {
  static screenToWorld(
    coord: { x: number; y: number },
    camera: THREE.Camera,
    distance: number = 10
  ): THREE.Vector3 {
    const vec = new THREE.Vector3();
    vec.set(
      (coord.x / window.innerWidth) * 2 - 1,
      -(coord.y / window.innerHeight) * 2 + 1,
      0.5
    );
    vec.unproject(camera);
    return vec.sub(camera.position).normalize().multiplyScalar(distance);
  }

  static raycastFromScreen(
    coord: { x: number; y: number },
    camera: THREE.Camera,
    objects: THREE.Object3D[],
    renderer: THREE.WebGLRenderer
  ): THREE.Intersection[] {
    const raycaster = new THREE.Raycaster();
    const rect = renderer.domElement.getBoundingClientRect();
    const x = ((coord.x - rect.left) / rect.width) * 2 - 1;
    const y = -((coord.y - rect.top) / rect.height) * 2 + 1;
    raycaster.setFromCamera(new THREE.Vector2(x, y), camera);
    return raycaster.intersectObjects(objects, true);
  }

  static animate(
    callback: (delta: number) => void,
    fps: number = 60
  ): { start: () => void; stop: () => void } {
    let animateId: number;
    const interval = 1000 / fps;
    let then = Date.now();

    const loop = () => {
      animateId = requestAnimationFrame(loop);
      const now = Date.now();
      const delta = now - then;

      if (delta > interval) {
        then = now - (delta % interval);
        callback(delta / 1000);
      }
    };

    return {
      start: () => loop(),
      stop: () => cancelAnimationFrame(animateId)
    };
  }
}