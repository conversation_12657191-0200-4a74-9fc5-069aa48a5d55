<template>
    <div class="tasks-tab-card">
       <div class="title">
        Projects
       </div>
       <div class="content">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh"
        pulling-text="Pull to refresh..."
        loosing-text="Release to refresh..."
        loading-text="Refreshing..."
        success-text="Refresh successful"
        >
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="No More"
            loading-text="Loading..."
            @load="onLoad"
          >
            <div class="content-item" v-for="item in list" :key="item">
                <div class="content-item-left" @click="handleIconClick(item)">
                    <van-icon name="/src/icon/dwprMobile/tasks-card-icon.svg" :size="16" />
                    <span class="project-name">Project 1</span>
                    <van-icon name="arrow" color="#fff" :size="16" />
                </div>
                <div class="content-item-right">
                    <span>7-17</span>
                </div>
            </div>
          </van-list>
        </van-pull-refresh>
       </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
const list = ref<any>([]);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);

const onLoad = () => {
  setTimeout(() => {
    if (refreshing.value) {
      list.value = [];
      refreshing.value = false;
    }

    for (let i = 0; i < 10; i++) {
      list.value.push(list.value.length + 1);
    }
    loading.value = false;

    if (list.value.length >= 40) {
      finished.value = true;
    }
  }, 1000);
};

const onRefresh = () => {
  // 清空列表数据
  finished.value = false;

  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  onLoad();
};
const router = useRouter()
const handleIconClick = () => {
  router.push('/Dwpr/taskPreview')
}

</script>

<style scoped lang="less">
.tasks-tab-card {
    height: 12.6875rem;
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: .625rem;
    background: #4472C4;
    border-radius: 6px;
    .title {
        height: 1.875rem;
        font-weight: 500;
        font-size: 1.25rem;
        color: #FFFFFF; 
    }
    .content {
        flex: 1;
        overflow-y: auto;
        /* 隐藏滚动条但保持滚动功能 */
        scrollbar-width: none;
        -ms-overflow-style: none;
        
        &::-webkit-scrollbar {
            display: none;
        }
        .content-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: .75rem 0;
        }
        .content-item-left {
            display: flex;
            align-items: center;
            .project-name {
                font-weight: 400;
                font-size: 1.125rem;
                color: #FFFFFF;
            }
            .van-icon {
                margin: 0 .5rem;
            }

        }
        .content-item-right {
            font-weight: 400;
            font-size: 1rem;
            color: #FFFFFF;
        }
    }
    
}
</style>