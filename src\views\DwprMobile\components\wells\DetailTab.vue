<template>
  <div class="detail-tab">
    <div class="detial-tab-item" v-for="value in 8">
      <div class="item-icon">
        <van-icon name="/src/icon/dwprMobile/detail-icon.svg" :size="24" />
      </div>
      <div class="item-content">
        <div class="item-content-title">Name</div>
        <div class="item-content-value">PY4-2-A19H4</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { showToast } from 'vant'
// 生命周期
onMounted(() => {
})
</script>

<style scoped lang="less">
.detail-tab {
  height: 100%;
  background: #FFFFFF;
  padding-top: 1rem;
}
.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 1rem;
  padding-right: .6875rem;
}
.detial-tab-item {
  display: flex;
  .item-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: .75rem 1.5rem .75rem .75rem;
    border-bottom: 1px solid #E5E5E5;
    .item-content-title {
      font-weight: 400;
      font-size: 1.125rem;
      color: #737373;
    }
    .item-content-value {
      font-weight: 500;
      font-size: 1.125rem;
      color: #3D3D3D;
    }
  }
}
</style>