<template>
  <div class="tasks-content">
    <div class="tasks-content-header">Witsml Tasks</div>
    <div class="tasks-list">
      <van-pull-refresh
        v-model="refreshing"
        @refresh="onRefresh"
        pulling-text="Pull to refresh..."
        loosing-text="Release to refresh..."
        loading-text="Refreshing..."
        success-text="Refresh successful"
      >
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="No More"
          loading-text="Loading..."
          @load="onLoad"
        >
          <div class="tasks-item" v-for="item in list" :key="item">
            <div class="tasks-item-l">
              <van-icon
                name="/src/icon/dwprMobile/witsml-task-icon.svg"
                size="16"
              />
            </div>
            <div class="tasks-item-r">
              <div class="tasks-item-left-left">
                <div class="tasks-item-left-left-top">Wellname - LogName</div>
                <div class="tasks-item-left-left-bottom">
                  RTC4.0外网-Downloading 65%
                </div>
              </div>
              <div class="tasks-item-left-right">
                <van-icon
                  name="/src/icon/dwprMobile/witsml-tasks-succes-icon.svg"
                  size="24"
                />
                <span class="tasks-item-left-right-time">7-17</span>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
const list = ref<any>([]);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);

const onLoad = () => {
  setTimeout(() => {
    if (refreshing.value) {
      list.value = [];
      refreshing.value = false;
    }

    for (let i = 0; i < 10; i++) {
      list.value.push(list.value.length + 1);
    }
    loading.value = false;

    if (list.value.length >= 40) {
      finished.value = true;
    }
  }, 1000);
};

const onRefresh = () => {
  // 清空列表数据
  finished.value = false;

  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  onLoad();
};

</script>

<style scoped lang="less">
.tasks-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0.375rem 0.625rem 0 0.625rem;
    background-color: #fff;
    .tasks-content-header {
      font-weight: 500;
      font-size: 1.25rem;
      color: #4472c4;
    }
    .tasks-list {
      width: 100%;
      flex: 1;
      min-height: 0;
      overflow-y: auto;
      /* 隐藏滚动条但保持滚动功能 */
      scrollbar-width: none;
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none;
      }
      .tasks-item {
        width: 100%;
        display: flex;
        padding: 0.25rem 0;
        .tasks-item-l {
          padding-top: 0.1875rem;
          padding-right: 0.25rem;
        }
        .tasks-item-r {
          flex: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-bottom: 0.25rem;
          border-bottom: 1px dashed #e5e5e5;
          .tasks-item-left-left-top {
            font-weight: 400;
            font-size: 1rem;
            color: #3d3d3d;
          }
          .tasks-item-left-left-bottom {
            font-weight: 400;
            font-size: 0.75rem;
            color: #737373;
          }
          .tasks-item-left-right {
            display: flex;
            align-items: center;
            .tasks-item-left-right-time {
              font-weight: 400;
              font-size: 0.75rem;
              color: #bfbfbf;
              margin-left: 1rem;
            }
          }
        }
      }
    }
  }

</style>
