import { defineStore } from 'pinia';

export const useDataStore = defineStore('dataStore', {
    state: () => ({
        offsetChannelId: '',
        preDepthShift: 0,
        tableData: [],
        baseUnit: ''
    }),
    actions: {
        setOffsetChannelId(data) {
            this.offsetChannelId = data;
        },
        setPreDepthShift(data) {
            this.preDepthShift = data;
        },
        setTableData(data) {
            this.tableData = data
        },
        setBaseUnit(data) {
            this.baseUnit = data
        }
    },
});

