{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "src/types/*.d.ts","src/**/*.js"],
  "compilerOptions": {
    "composite": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },
    "types": ["node"],
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "allowJs": true,
    "skipLibCheck": true,
    "moduleResolution": "node" // 无法正常找到three.js的资源，所以设置为node
  },
}

