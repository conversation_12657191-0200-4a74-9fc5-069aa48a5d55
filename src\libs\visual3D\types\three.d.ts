import * as THREE from 'three';

declare module 'three' {
  interface Scene {
    customProps?: {
      environmentMap?: THREE.Texture;
      physicsWorld?: any; // 根据实际物理引擎类型定义
    };
  }

  interface Mesh {
    material: THREE.Material | THREE.Material[];
  }

  export class RendererManager {
    constructor(
      canvas: HTMLCanvasElement,
      config?: {
        antialias?: boolean;
        alpha?: boolean;
        shadowMap?: boolean;
      }
    );
  }

  export type PointerEventHandlers = {
    onClick?: (obj: Object3D) => void;
    onHoverStart?: (obj: Object3D) => void;
    onHoverEnd?: (obj: Object3D) => void;
    onDragStart?: (obj: Object3D) => void;
    onDrag?: (delta: Vector3, obj: Object3D) => void;
    onDragEnd?: () => void;
  };

  interface Material {
    dispose?: () => void;
  }
  
  interface Light {
    dispose?: () => void;
  }

  interface Object3D {
    // 如果确实需要通用dispose方法
    dispose?: () => void;
  }

  export class SceneManager {
    public scene: THREE.Scene;
    
    constructor();
    
    addObject(object: THREE.Object3D, trackResources?: boolean): this;
    removeObject(object: THREE.Object3D, dispose?: boolean): this;
    dispose(): void;
    
    trackDisposable<T extends { dispose: () => void }>(resource: T): T;
    
    on<T extends keyof SceneEventMap>(
      type: T,
      listener: (event: SceneEventMap[T]) => void
    ): this;
    
    off<T extends keyof SceneEventMap>(
      type: T,
      listener: (event: SceneEventMap[T]) => void
    ): this;
  }
}