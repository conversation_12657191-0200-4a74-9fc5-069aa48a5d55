import { defineStore } from 'pinia'
export const useEditCurveStore = defineStore('editCurve', {
  state: () => ({
    wellId: '',
    datasetId: '',
    curveId: '',
    activeAction: '',
    isStartEdit: false,
    minIndex: 0,//每次编辑的最小索引
    maxIndex: 0,//每次编辑的最大索引
    curveEditInstance: {},
    depthPairsInstance: null
  }),
  actions: {
    setWellId(val) {
      this.wellId = val
    },
    setDatasetId(val) {
      this.datasetId = val
    },
    setCurveId(val) {
      this.curveId = val
    },
    setActiveAction(type) {
      this.activeAction = type
    },
    setIsStartEdit(type) {
      this.isStartEdit = type
    },
    setMinIndex(val) {
      this.minIndex = val
    },
    setMaxIndex(val) {
      this.maxIndex = val
    },
    // 新增曲线编辑注册
    registerCurveEdit(tabId, instance) {
      if (instance && tabId) {
        this.curveEditInstance[tabId] = instance
      }
    },
    registerDepthPairs(instance) {
      this.depthPairsInstance = instance;
    }
  }
})