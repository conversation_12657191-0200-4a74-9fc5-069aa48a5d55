<template>
  <!-- 导入模型弹框 -->
  <el-dialog 
    title="Load the model" 
    v-model="visible" 
    width="700px" 
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="true"
    @close="handleDialogClose"
    destroy-on-close
  >
    <el-table
      ref="tableRef"
      :data="models"
      v-loading="tableLoading"
      style="width: 100%; height: 300px;"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择列 -->
      <el-table-column
        type="selection"
        width="55"
      />
      <!-- 模型名称 -->
      <el-table-column
        prop="name"
        label="Model Name"
        width="120"
      />
      <!-- 分类 -->
      <el-table-column
        prop="category"
        label="Category"
        width="100"
      />
      <!-- 备注 -->
      <el-table-column
        prop="remark"
        label="Remark"
        width="120"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ row.remark || '-' }}
        </template>
      </el-table-column>
      <!-- 创建时间 -->
      <el-table-column
        prop="createdOn"
        label="Created On"
        width="150"
      >
        <template #default="{ row }">
          {{ formatDate(row.createdOn) }}
        </template>
      </el-table-column>
      <!-- 所有者 -->
      <el-table-column
        prop="owner"
        label="Owner"
        width="100"
      >
        <template #default="{ row }">
          {{ row.owner || '-' }}
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 选择操作区域 -->
    <div class="selection-actions" v-if="models.length > 0">
      <span class="selection-info">
        Selected {{ selectedModels.length }} / {{ models.length }} Item
      </span>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button 
          @click="handleDialogClose"
        >
          Cancel
        </el-button>
        <el-button 
          type="primary" 
          :loading="isImporting"
          @click="handleConfirm"
        >
          {{ isImporting ? 'Importing...' : 'Confirm' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue';
import axios from "axios";
import { ElMessage } from "element-plus";

// 定义组件名称
defineOptions({
  name: 'ImportModelDialog'
});

// 定义 Props
const props = defineProps({
  // 控制弹框显示/隐藏
  modelValue: {
    type: Boolean,
    default: false
  },
  // 当前选中的节点
  currentNode: {
    type: Object,
    default: () => null
  },
  // 树数据，用于获取油田ID
  treeData: {
    type: Array,
    default: () => []
  },
  // API基础URL
  apiBaseUrl: {
    type: String,
    default: () => window.location.protocol + "//" + window.location.host + "/api"
  }
});

// 定义 Emits
const emit = defineEmits(['update:modelValue', 'model-imported', 'tree-save-required', 'addModel']);

// 响应式数据
const tableRef = ref(null);
const isImporting = ref(false);
const models = ref([]);
const selectedModels = ref([]);
const tableLoading = ref(false); //表格加载中
const selectedModelIds = ref([]);

// 计算属性：是否全选
const isAllSelected = computed(() => {
  return models.value.length > 0 && selectedModels.value.length === models.value.length;
});

// 计算属性：双向绑定弹框显示状态
const visible = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit('update:modelValue', value);
  }
});

// 监听弹框显示状态，显示时加载模型列表
watch(visible, (newVal) => {
  if (newVal) {
    loadModels();
  }
});

const setDefaultSelection = () => {
  if (!tableRef.value || !models.value.length) return;
  
  // 遍历所有模型，勾选已导入的模型
  models.value.forEach(model => {
    if (selectedModelIds.value.includes(model.id)) {
      tableRef.value.toggleRowSelection(model, true);
    }
  });
};

/**
 * 加载模型列表
 */
const loadModels = async () => {
  tableLoading.value = true;
  try {
    // 获取树结构第一个节点的id作为oilfieldId
    const oilfieldId = props.treeData[0]?.id;
    if (!oilfieldId) {
      throw new Error('未找到油田ID');
    }
    
    // 获取地层模型节点的children，提取id作为已导入的模型ID
    const modelNode = getStratigraphicModelChildren();
    selectedModelIds.value = modelNode.map(item => item.id);
    
    const response = await axios.get(`${props.apiBaseUrl}/visual3D/project/GetModelList?oilfieldId=${oilfieldId}`);
    if (response.data?.success) {
      models.value = response.data.data;
      // 等待DOM更新后设置默认选择
      await nextTick();
      setDefaultSelection();
    } else {
      throw new Error(response.data?.message || '获取模型列表失败');
    }
  } catch (error) {
    console.error('获取模型列表错误:', error);
    ElMessage.error(error.message || '获取模型列表失败');
  } finally {
    tableLoading.value = false;
  }
};

/**
 * 格式化日期
 */
const formatDate = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * 处理选择变化
 */
const handleSelectionChange = (selection) => {
  selectedModels.value = selection;
};

/**
 * 全选
 */
const selectAll = () => {
  tableRef.value?.toggleAllSelection();
};

/**
 * 清空选择
 */
const clearSelection = () => {
  tableRef.value?.clearSelection();
};

/**
 * 处理弹框关闭
 */
const handleDialogClose = () => {
  visible.value = false;
  selectedModels.value = [];
  tableRef.value?.clearSelection();
};

/**
 * 查找地层模型节点（moduleType = 1）并获取其children
 * @param {Array} treeData - 树形数据
 * @returns {Array} 地层模型节点的children数组，如果未找到则返回空数组
 */
const findStratigraphicModelChildren = (treeData) => {
  // 递归搜索函数
  const searchNode = (nodes) => {
    if (!Array.isArray(nodes)) return null;
    
    for (const node of nodes) {
      // 如果当前节点是地层模型节点（moduleType = 1）
      if (node.moduleType === 1) {
        return node.children || [];
      }
      
      // 如果当前节点有子节点，递归搜索
      if (node.children && node.children.length > 0) {
        const result = searchNode(node.children);
        if (result !== null) {
          return result;
        }
      }
    }
    
    return null;
  };
  
  const result = searchNode(treeData);
  return result || [];
};

/**
 * 获取地层模型节点的children（使用props.treeData）
 * @returns {Array} 地层模型节点的children数组
 */
const getStratigraphicModelChildren = () => {
  return findStratigraphicModelChildren(props.treeData);
};

/**
 * 处理确认导入
 */
const handleConfirm = async () => {
  if (isImporting.value) return;
  
  try {
    // 检查是否有选中的模型
    if (selectedModels.value.length === 0) {
      ElMessage.warning('请至少选择一个模型');
      return;
    }
    
    isImporting.value = true;
    
    // 确保当前节点有children数组
    if (!props.currentNode.children) {
      props.currentNode.children = [];
    }

    // 批量处理选中的模型
    const addedModels = [];
    const tempModels = []
    for (const selectedModel of selectedModels.value) {
      // // 检查是否已存在相同ID的节点
      // const existingNode = props.currentNode.children.find(child => child.id === selectedModel.id);
      // if (existingNode) {
      //   console.warn(`模型 ${selectedModel.name} 已存在，跳过添加`);
      //   continue;
      // }

      // 创建模型节点
      const modelNode = {
        id: selectedModel.id,
        label: selectedModel.name,
        moduleType: 9, // 模型节点类型
        children: []
      };
      // 添加新节点到当前节点
      tempModels.push(modelNode);
      // addedModels.push({
      //   modelId: modelNode.id,
      //   modelName: modelNode.label
      // });
    }

    emit('addModel', tempModels);

    // if (addedModels.length === 0) {
    //   ElMessage.warning('所选模型均已存在，无需重复添加');
    //   return;
    // }

    // 通知父组件保存树结构
    emit('tree-save-required');
    
    //ElMessage.success(`成功导入 ${addedModels.length} 个模型`);
    handleDialogClose();
    
  } catch (error) {
    console.error('导入模型错误:', error);
    ElMessage.error(error.message || '导入模型失败');
  } finally {
    isImporting.value = false;
  }
};
</script>

<style scoped>
.selection-actions {
  margin-top: 10px;
  padding: 5px;
  /* background-color: #f5f7fa; */
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.selection-info {
  margin-left: auto;
  font-size: 14px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}
</style>