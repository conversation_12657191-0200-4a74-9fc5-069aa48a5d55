<template>
  <div class="project-tree-container">
    <!-- 调试按钮 -->
    <div class="debug-toolbar">
      <el-button 
        type="primary" 
        size="small" 
        @click="saveTreeInfo"
        style="margin: 10px;"
      >
        Save
      </el-button>
    </div>
    
    <el-tree 
      :data="treeData" 
      :props="defaultProps" 
      node-key="id" 
      default-expand-all 
      highlight-current
      show-checkbox
      :expand-on-click-node="false" 
      ref="tree" 
      class="custom-tree" 
      @node-contextmenu="handleContextMenu"
      @node-click="handleNodeClick"
      @check="handleCheck"
      v-loading="isLoading"
    >
      <template #default="{ node, data }">
        <span class="custom-tree-node">
          <Icon :name="getIcon(data)" size="sm"/>
          <span>{{ node.label }}</span>
        </span>
      </template>
    </el-tree>

    <ContextMenu 
      :menuItems="customMenuItems" 
      ref="contextMenu"
      @menu-action="handleMenuAction"
    />

    <el-dialog 
      title="Create Project" 
      v-model="dialogVisible" 
      width="550px" 
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="!isConfirmDisabled_createProject"
      @close="handleDialogClose"
      destroy-on-close
    >
      <el-form 
        :model="form" 
        ref="formRef" 
        :rules="rules"
        :disabled="isConfirmDisabled_createProject"
        label-position="right"
      >
      <el-form-item label="Oil Field" prop="oilfieldId" class="form-item" label-width="120px">
          <el-select 
            v-model="form.oilfieldId" 
            placeholder="Please select oil field" 
            class="input-field"
            :loading="isLoadingOilfields"
            filterable
          >
            <el-option
              v-for="item in oilfields"
              :key="item.id"
              :label="item.cOilFieldName" 
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="Project Name" prop="projectName" class="form-item" label-width="120px">
          <el-input 
            v-model="form.projectName" 
            placeholder="Please enter project name" 
            class="input-field"
            :maxlength="50"            
            clearable
          >
            <template #suffix>
              <el-button 
                type="primary" 
                size="small" 
                @click="generateProjectName"
                style="margin-right: 5px; background-color: transparent; color: #909399; border-color: transparent;"
              >
                自动
              </el-button>
            </template>
          </el-input>
        </el-form-item> -->
        <el-form-item  label="Project Name" prop="projectName" class="form-item" label-width="120px">
        <el-input v-model="form.projectName" placeholder="Please enter project name" 
            class="input-field"
            :maxlength="50"            
            clearable>
            <template #append><el-button  @click="generateProjectName">自动</el-button></template>            
          </el-input>
        </el-form-item>

        <el-form-item label="Remark" prop="note" class="form-item" label-width="120px">
          <el-input 
            v-model="form.note" 
            type="textarea"
            :rows="3"
            placeholder="Please enter remark" 
            class="input-field"
            :maxlength="200"
            show-word-limit
            resize="none"
          ></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button 
            @click="handleDialogClose" 
            :disabled="isConfirmDisabled_createProject"
          >
            Cancel
          </el-button>
          <el-button 
            type="primary" 
            :loading="isConfirmDisabled_createProject"
            :disabled="isConfirmDisabled_createProject" 
            @click="createProject"
          >
            {{ isConfirmDisabled_createProject ? 'Creating...' : 'Confirm' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog 
      title="Rename Project" 
      v-model="renameDialogVisible" 
      width="550px" 
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="true"
      @close="handleRenameDialogClose"
      destroy-on-close
      :key="currentNode?.data?.id"
    >
      <el-form 
        :model="renameForm" 
        ref="renameFormRef" 
        :rules="renameRules"
        label-position="right"
      >
        <el-form-item label="New Name" prop="newName" class="form-item" label-width="120px">
          <el-input 
            v-model="renameForm.newName" 
            placeholder="Please enter new name" 
            class="input-field"
            :maxlength="50"
            show-word-limit
            clearable
          ></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button 
            @click="handleRenameDialogClose"
          >
            Cancel
          </el-button>
          <el-button 
            type="primary" 
            @click="handleRename"
          >
            Confirm
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入井弹框 -->
    <el-dialog 
      title="Import Well" 
      v-model="importWellDialogVisible" 
      width="550px" 
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="true"
      @close="handleImportWellDialogClose"
      destroy-on-close
    >
      <el-form 
        :model="importWellForm" 
        ref="importWellFormRef" 
        :rules="importWellRules"
        label-position="right"
      >
        <el-form-item label="Well" prop="wellId" class="form-item" label-width="120px">
          <el-select 
            v-model="importWellForm.wellId" 
            placeholder="Please select well" 
            class="input-field"
            :loading="isLoadingWells"
            filterable
          >
            <el-option
              v-for="item in wells"
              :key="item.id"
              :label="item.wellName" 
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button 
            @click="handleImportWellDialogClose"
          >
            Cancel
          </el-button>
          <el-button 
            type="primary" 
            :loading="isImportingWell"
            @click="handleImportWellConfirm"
          >
            {{ isImportingWell ? 'Importing...' : 'Import' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入井眼弹框 -->
    <el-dialog 
      title="Import Wellbore" 
      v-model="importWellboreDialogVisible" 
      width="550px" 
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="true"
      @close="handleImportWellboreDialogClose"
      destroy-on-close
    >
      <el-form 
        :model="importWellboreForm" 
        ref="importWellboreFormRef" 
        :rules="importWellboreRules"
        label-position="right"
      >
        <el-form-item label="Wellbore" prop="wellboreId" class="form-item" label-width="120px">
          <el-select 
            v-model="importWellboreForm.wellboreId" 
            placeholder="Please select wellbore" 
            class="input-field"
            :loading="isLoadingWellbores"
            filterable
          >
            <el-option
              v-for="item in wellbores"
              :key="item.id"
              :label="item.wellboreNumber" 
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button 
            @click="handleImportWellboreDialogClose"
          >
            Cancel
          </el-button>
          <el-button 
            type="primary" 
            :loading="isImportingWellbore"
            @click="handleImportWellboreConfirm"
          >
            {{ isImportingWellbore ? 'Importing...' : 'Import' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入曲线弹框 -->
    <el-dialog 
      title="Import Curve" 
      v-model="importCurveDialogVisible" 
      width="550px" 
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="true"
      @close="handleImportCurveDialogClose"
      destroy-on-close
    >
      <el-form 
        :model="importCurveForm" 
        ref="importCurveFormRef" 
        :rules="importCurveRules"
        label-position="right"
      >
        <el-form-item label="Dataset" prop="datasetId" class="form-item" label-width="120px">
          <el-select 
            v-model="importCurveForm.datasetId" 
            placeholder="Please select dataset" 
            class="input-field"
            :loading="isLoadingDatasets"
            filterable
            @change="handleDatasetChange"
          >
            <el-option
              v-for="item in datasets"
              :key="item.id"
              :label="item.name" 
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Curve" prop="curveId" class="form-item" label-width="120px">
          <el-select 
            v-model="importCurveForm.curveId" 
            placeholder="Please select curve" 
            class="input-field"
            :loading="isLoadingCurves"
            filterable
            :disabled="!importCurveForm.datasetId"
          >
            <el-option
              v-for="item in curves"
              :key="item.id"
              :label="item.name" 
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button 
            @click="handleImportCurveDialogClose"
          >
            Cancel
          </el-button>
          <el-button 
            type="primary" 
            :loading="isImportingCurve"
            @click="handleImportCurveConfirm"
          >
            {{ isImportingCurve ? 'Importing...' : 'Import' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入模型弹框 -->
    <ImportModelDialog
      v-model="importModelDialogVisible"
      :current-node="currentNode"
      :tree-data="treeData"
      :api-base-url="vWebApiUrl"
      @model-imported="handleModelImported"
      @tree-save-required="handleTreeSaveRequired"
      @addModel="handleAddModel"
    />

    <!-- 设置弹框 -->
    <el-dialog 
      title="设置" 
      v-model="settingDialogVisible" 
      width="550px" 
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="true"
      @close="handleSettingDialogClose"
      destroy-on-close
    >
      <el-form 
        :model="settingForm" 
        ref="settingFormRef" 
        label-position="right"
      >
        <el-form-item label="wellboreThickness" prop="wellboreThickness" class="form-item" label-width="120px">
          <el-input-number 
            v-model="settingForm.wellboreThickness" 
            :min="1" 
            :max="2000"
            :step="10"
            class="input-field"
          />
        </el-form-item>
        <el-form-item label="DisplayMode" prop="displayMode" class="form-item" label-width="120px" v-if="settingCurrentNode && (settingCurrentNode.moduleType === 7)">
          <el-select 
            v-model="settingForm.displayMode" 
            placeholder="Please select display mode" 
            class="input-field"
          >
            <el-option label="Normal" value="normal" />
            <el-option label="2D" value="2D" />
            <el-option label="3D" value="3D" />
          </el-select>
        </el-form-item>
        <el-form-item label="Min value" prop="min" class="form-item" label-width="120px" v-if="settingCurrentNode && (settingCurrentNode.moduleType === 7)">
          <el-input-number 
            v-model="settingForm.min" 
            :min="1"             
            :step="10"
            class="input-field"
          />
        </el-form-item>
        <el-form-item label="Max value" prop="max" class="form-item" label-width="120px" v-if="settingCurrentNode && (settingCurrentNode.moduleType === 7)">
          <el-input-number 
            v-model="settingForm.max" 
            :min="1"             
            :step="10"
            class="input-field"
          />
        </el-form-item>
        <el-form-item label="Min Calibrate" prop="minCalibrate" class="form-item" label-width="120px" v-if="settingCurrentNode && (settingCurrentNode.moduleType === 7)">
          <el-input-number 
            v-model="settingForm.minCalibrate" 
            :min="0"             
            :step="1"
            class="input-field"
          />
        </el-form-item>
        <el-form-item label="Max Calibrate" prop="maxCalibrate" class="form-item" label-width="120px" v-if="settingCurrentNode && (settingCurrentNode.moduleType === 7)">
          <el-input-number 
            v-model="settingForm.maxCalibrate" 
            :min="0"             
            :step="1"
            class="input-field"
          />
        </el-form-item>
        <el-form-item label="Min Color" prop="minColor" class="form-item" label-width="120px" v-if="settingCurrentNode && (settingCurrentNode.moduleType === 7)">
          <el-color-picker 
            v-model="settingForm.minColor" 
            class="input-field"
            show-alpha
          />
        </el-form-item>
        <el-form-item label="Max Color" prop="maxColor" class="form-item" label-width="120px" v-if="settingCurrentNode && (settingCurrentNode.moduleType === 7)">
          <el-color-picker 
            v-model="settingForm.maxColor" 
            class="input-field"
            show-alpha
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button 
            @click="handleSettingDialogClose"
          >
            取消
          </el-button>
          <el-button 
            type="primary" 
            @click="handleSettingConfirm"
          >
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 选择数据集弹框 -->
    <el-dialog 
      title="Choose Dataset" 
      v-model="chooseDatasetDialogVisible" 
      width="550px" 
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="true"
      @close="handleChooseDatasetDialogClose"
      destroy-on-close
    >
      <el-form 
        :model="chooseDatasetForm" 
        ref="chooseDatasetFormRef" 
        :rules="chooseDatasetRules"
        label-position="right"
      >
        <el-form-item label="Dataset" prop="datasetId" class="form-item" label-width="120px">
          <el-select 
            v-model="chooseDatasetForm.datasetId" 
            placeholder="Please select dataset" 
            class="input-field"
            :loading="isLoadingDatasetsForChoose"
            filterable
          >
            <el-option
              v-for="item in datasetsForChoose"
              :key="item.id"
              :label="item.name" 
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button 
            @click="handleChooseDatasetDialogClose"
          >
            Cancel
          </el-button>
          <el-button 
            type="primary" 
            :loading="isChoosingDataset"
            @click="handleChooseDatasetConfirm"
          >
            {{ isChoosingDataset ? 'Saving...' : 'Confirm' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 曲线预览弹框 -->
    <el-dialog
      :title=curveTitle
      draggable
      v-model="curvePreviewDialog"
      width="1000px"
      :close-on-click-modal="false"
      @close="handleCurvePreviewDialogClose">
        <curvePreview />
    </el-dialog>
  </div>
</template>

<script>
import axios from "axios";
import "element-plus/dist/index.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import ContextMenu from "@/components/ContextMenu.vue";
import { useResourceTreeStore } from "@/stores/resourceTreeStore";
import curvePreview from "@/components/curvePreview.vue";
import { usePreviewStore } from "@/stores/preview.js";
import Icon from "@/components/Icon.vue";
import ImportModelDialog from "./importModelDialog.vue";

export default {
  name: 'ProjectTree',
  components: {
    Icon,
    ContextMenu,
    curvePreview,
    ImportModelDialog,
    ...ElementPlusIconsVue
  },
  data() {
    return {
      customMenuItems: [
        {
          label: "New",
          action: "create",
          children: [
            { label: "Import Well", action: "importWell", moduleType: 0 },
            { label: "Import Model", action: "importModel", moduleType: 1 },
            { label: "Import Wellbore", action: "importWellbore", moduleType: 2 },
            { label: "Import Curve", action: "importCurve", moduleType: 4 },
          ],
        },        
        { label: "Rename", action: "rename" },
        { label: "Delete", action: "delete" },
        { label: "Refresh", action: "refresh" },
        
      ],
      defaultProps: {
        children: "children",
        label: "label",
      },
      form: {
        oilfieldId: "",
        projectName: "",
        note: "",
      },
      rules: {
        oilfieldId: [
          { required: true, message: "Please select oil field", trigger: "change" },
        ],
        projectName: [
          { required: true, message: "Please enter project name", trigger: "blur" },
          { min: 2, max: 50, message: "Length should be 2 to 50 characters", trigger: "blur" }
        ],
        note: [
          { max: 200, message: "Length should not exceed 200 characters", trigger: "blur" }
        ]
      },
      treeData: [],
      dialogVisible: false,
      isConfirmDisabled_createProject: false,
      renameDialogVisible: false,
      renameForm: {
        newName: '',
      },
      currentNode: null,
      isLoading: false,
      isLoadingOilfields: false,
      appId: "",
      id: "",
      oilfields: [],
      vWebApiUrl: window.location.protocol + "//" + window.location.host + "/api",
      renameRules: {
        newName: [
          { required: true, message: "请输入新名称", trigger: "blur" },
          { min: 2, max: 50, message: "长度应为2至50个字符", trigger: "blur" }
        ]
      },
      importWellDialogVisible: false,
      isImportingWell: false,
      isLoadingWells: false,
      wells: [],
      importWellForm: {
        wellId: ''
      },
      importWellRules: {
        wellId: [
          { required: true, message: "请选择井", trigger: "change" }
        ]
      },
      importWellboreDialogVisible: false,
      isImportingWellbore: false,
      isLoadingWellbores: false,
      wellbores: [],
      importWellboreForm: {
        wellboreId: ''
      },
      importWellboreRules: {
        wellboreId: [
          { required: true, message: "请选择井眼", trigger: "change" }
        ]
      },
      importCurveDialogVisible: false,
      isImportingCurve: false,
      isLoadingDatasets: false,
      isLoadingCurves: false,
      datasets: [],
      curves: [],
      importCurveForm: {
        datasetId: '',
        curveId: ''
      },
      importCurveRules: {
        datasetId: [
          { required: true, message: "请选择数据集", trigger: "change" }
        ],
        curveId: [
          { required: true, message: "请选择曲线", trigger: "change" }
        ]
      },
      checkedNodes: [], // 存储选中的节点
      checkedNodeIds: [], // 新增：存储选中节点的ID数组
      importModelDialogVisible: false,
      settingDialogVisible: false,
      settingForm: {
        wellboreThickness: null,
        displayMode: 'normal',
        min: null,
        max: null,
        minCalibrate: null,
        maxCalibrate: null,
        minColor: '#FF0000',
        maxColor: '#FF0000'
      },
      settingCurrentNode: null,
      settingCache: {}, // 新增：用于缓存每个节点的设置
      chooseDatasetDialogVisible: false,
      isChoosingDataset: false,
      isLoadingDatasetsForChoose: false,
      datasetsForChoose: [],
      chooseDatasetForm: {
        datasetId: ''
      },
      chooseDatasetRules: {
        datasetId: [
          { required: true, message: '请选择数据集', trigger: 'change' }
        ]
      },
      curveTitle:'',
      curvePreviewDialog:false
    }
  },
  methods: {
    handleNodeClick(data) {
      console.log("点击节点:", data);
      this.currentNode = data;
      this.$refs.tree.setCurrentKey(data.id);
    },
    handleAction(action) {
      switch (action) {
        case "createProject":
          window.open(
            window.location.protocol +
              "//" +
              window.location.host +
              "/static/app/#/3dviews/?appId=" +
              this.appId
          );
          break;
        case "importWell":
          if(this.currentNode.moduleType!==0){
            this.$message.error("请选择工程节点");
            return;
          }
          else{
            this.handleMenuAction('importWell',this.currentNode)
          }          
          break;
        case "importWellbore":
        if(this.currentNode.moduleType!==2){
            this.$message.error("请选择井节点");
            return;
          }
          else{
            this.handleMenuAction('importWellbore',this.currentNode)
          }          
          break;
        case "importCurve":
        if(this.currentNode.moduleType!==4){
            this.$message.error("请选择井眼节点");
            return;
          }
          else{
            this.handleMenuAction('importCurve',this.currentNode)
          }          
          break;          
        default:
          console.log(action + "操作...");
          break;
      }
    },
    getIcon(data) {
      const iconMap = {
        0: 'Folders.svg',//工程节点
        1: 'geo.svg',//地质模型总节点
        2: 'Well.png',//井节点
        4: 'Wellbore.png',//井眼节点
        5: 'data-set.png',//数据集节点
        6: 'SurveyData.png',//轨迹节点
        7: 'channel.png',//曲线节点
        8: 'Well.png',//井筒节点
        9: 'geo.svg',//地质模型节点
        10: '3dviewer.png',//插值成像节点
      };
      return iconMap[data.moduleType] || 'Folders.svg';
    },
    async saveTree(saveCheckedState = true) {
      try {
        const hashParams = new URLSearchParams(window.location.hash.split('?')[1]);
        const projectId = hashParams.get("id");
        
        if (!this.appId || !projectId) {
          throw new Error('缺少必要的参数');
        }

        let checkedKeys = [];
        let halfCheckedKeys = [];
        
        if (saveCheckedState) {
          // 获取当前选中的节点信息
          checkedKeys = this.$refs.tree ? this.$refs.tree.getCheckedKeys() : [];
          halfCheckedKeys = this.$refs.tree ? this.$refs.tree.getHalfCheckedKeys() : [];
        }
        
        console.log('保存时的选中状态:', {
          checkedKeys,
          halfCheckedKeys,
          checkedNodeIds: this.checkedNodeIds,
          saveCheckedState
        });

        // 创建包含选中信息的树数据副本
        const treeDataWithChecked = this.addCheckedInfoToTreeData(this.treeData, checkedKeys, halfCheckedKeys);

        return await axios.post(`${this.vWebApiUrl}/project/project/SaveTree`, {
          appId: this.appId,
          projectId: projectId,
          treeData: treeDataWithChecked
        });
      } catch (error) {
        console.error('保存树结构错误:', error);
        throw error;
      }
    },
    handleContextMenu(event, node) {
      event.preventDefault();
      this.currentNode = node;
      
      // 定义所有可能的菜单项配置
      const menuConfigs = {
        0: { // 工程节点
          
             label: "Add Well", action: "importWell", moduleType: 0 
          
        },
        1: { // 模型节点
          
             label: "Add Model", action: "importModel", moduleType: 1 
          
        },
        // 2: { // 井节点
          
        //      label: "Add Wellbore", action: "importWellbore", moduleType: 2 
          
        // },
        4: { // 井眼节点
          
             label: "Add Curve", action: "importCurve", moduleType: 4 
          
        },
        6: {
          //轨迹节点
           label: "Setting", action: "setting" , moduleType: 6
        },
        // 曲线节点
        7: { label: "Curve Preview", action: "curvePreview", moduleType: 7 },
        10: {
          //插值成像节点
           label: "Choose Dataset", action: "chooseDataset" , moduleType: 10
        }       
      };

      // 基础菜单项
      const baseMenuItems = [
        //{ label: "Rename", action: "rename" },
        { label: "Refresh", action: "refresh" }
      ];

      // 如果不是工程节点和模型节点，添加删除选项
      if (node.moduleType !== 0 && node.moduleType !== 1) {
        baseMenuItems.splice(1, 0, { label: "Delete", action: "delete" });
      }

      // 获取当前节点的moduleType
      const moduleType = node.moduleType;
      
      // 根据moduleType构建菜单项
      if (moduleType !== undefined && menuConfigs[moduleType]) {
          if(moduleType===7){
            this.customMenuItems = [
            menuConfigs[moduleType],    
            menuConfigs[6],          
            ...baseMenuItems
          ];

          }
          else{
              this.customMenuItems = [
              menuConfigs[moduleType],          
              ...baseMenuItems
            ];
          }

      } else {
        // 如果没有匹配的moduleType，只显示基础菜单项
        this.customMenuItems = baseMenuItems;
      }
      
      if (moduleType === 7) {
        // 获取井节点ID
        const wellNodeId = this.getWellNodeId(node);
         const newNode = {
          ...node,
          wellNodeId: wellNodeId,
          nodeDataId: node?.id
        }
        // 显示上下文菜单
        this.$refs.contextMenu.show(event, newNode);
      } else {
        // 显示上下文菜单
        this.$refs.contextMenu.show(event, node);
      } 
      
    },
    handleMenuAction(action, node) {
      // 调试信息，检查接收到的数据
      console.log('Menu action:', action, 'Node:', node);
      
      if (!node) {
        node = this.currentNode;
        console.log('Using currentNode as fallback:', node);
      }
      
      switch (action) {
        // case 'rename':
        //   this.showRenameDialog(node);
        //   break;
        case 'delete':
          this.handleDelete(node);
          break;
        case 'refresh':
          this.loadTreeData();
          break;
        case 'importWell':
        case 'importModel':
        case 'importWellbore':
        case 'importCurve':
          // 如果没有当前节点，使用根节点
          if (!node) {
            this.$message.error('请选择一个节点');
            return;
          }
          this.handleImport(action, node);
          break;
        case 'setting':
          this.showSettingDialog(node);
          break;
        case "curvePreview":
          this.showCurvePrivew(node);
          break;
        case 'chooseDataset':
          this.showChooseDatasetDialog(node);
          break;
      }
    },
    showRenameDialog(node) {
      if (!node) {
        console.error('No node provided for rename');
        this.$message.error('无法重命名：节点信息不完整');
        return;
      }
      
      console.log('Node to rename (full):', node);
      
      // 保存当前节点引用
      this.currentNode = node;
      
      // 取得节点标签
      let nodeLabel = '';
      if (typeof node.label === 'string') {
        nodeLabel = node.label;
      } else if (node.data && typeof node.data.label === 'string') {
        nodeLabel = node.data.label;
      }
      
      if (!nodeLabel) {
        console.warn('无法获取节点名称，使用空字符串');
      }
      
      // 设置表单初始值
      this.renameForm.newName = nodeLabel;
      
      // 打开对话框
      this.renameDialogVisible = true;
    },
    showCurvePrivew(node) {
      this.curveTitle = `${node.label} Preview`
      this.curvePreviewDialog = true;
      this.$nextTick(() => {
        const usePreStore = usePreviewStore();
        // 设置必要的参数
        usePreStore.setChannelId(node.id);
        usePreStore.setNodeInfo(node);
        usePreStore.setProjectId(this.projectId || ''); // 使用组件的项目ID
        usePreStore.setView(true);
        usePreStore.setPreviewCurve(true);
      })
    },
    handleCurvePreviewDialogClose() {
      // 关闭预览弹窗时，重置预览状态
      const usePreStore = usePreviewStore();
      usePreStore.setView(false);
      usePreStore.setPreviewCurve(false);
      usePreStore.setChannelId('');
      usePreStore.setNodeInfo({});
      usePreStore.setProjectId('');
    },
    async handleRename() {
      if (!this.currentNode || !this.renameForm.newName.trim()) {
        this.$message.warning('Please enter a valid name');
        return;
      }

      try {
        const node = this.currentNode;
        const newName = this.renameForm.newName.trim();
        
        // 保存树结构至数据库
        const response = await this.saveTree();
        
        if (response.data?.success) {
          const data = response.data.data;
          const currentPath = window.location.hash.split('?')[0];
          const newHash = `${currentPath}?id=${data.id}&appId=${this.appId}`;
          window.location.hash = newHash;
          
          // 重新加载树结构
          await this.reloadTree();
          
          this.$message.success('Node renamed successfully');
          this.renameDialogVisible = false;
        } else {
          throw new Error(response.data?.message || 'Failed to rename node');
        }
      } catch (error) {
        console.error('Error renaming node:', error);
        this.$message.error(error.message || 'Failed to rename node');
      }
    },
    async handleDelete(node) {
      if (!node) {
        console.error('No node provided for delete');
        this.$message.error('无法删除：节点信息不完整');
        return;
      }
      
      this.$confirm('此操作将永久删除该节点, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 从树结构中找到并移除该节点
          this.removeNodeFromTree(this.treeData, node.id);
          
          // 从选中节点ID数组中移除已删除的节点ID
          this.removeDeletedNodeFromChecked(node.id);
          
          // 保存树结构至数据库
          const response = await this.saveTree();
          
          if (response.data?.success) {
            // 重新加载树结构
            await this.reloadTree();
            this.$message.success('删除成功');
          } else {
            throw new Error(response.data?.message || '删除失败');
          }
        } catch (error) {
          console.error('删除节点错误:', error);
          this.$message.error(error.message || '删除失败');
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    removeNodeFromTree(nodes, id) {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].id === id) {
          nodes.splice(i, 1);
          return true;
        }
        if (nodes[i].children && nodes[i].children.length > 0) {
          if (this.removeNodeFromTree(nodes[i].children, id)) {
            return true;
          }
        }
      }
      return false;
    },
    // 新增方法：从选中数组中移除已删除的节点ID（包括子节点）
    removeDeletedNodeFromChecked(nodeId) {
      // 首先移除当前节点ID
      const index = this.checkedNodeIds.indexOf(nodeId);
      if (index > -1) {
        this.checkedNodeIds.splice(index, 1);
        console.log(`已从选中数组中移除节点ID: ${nodeId}`);
      }
      
      // 如果需要，还可以移除该节点的所有子节点ID
      // 这里可以根据实际需要决定是否实现子节点ID的清理逻辑
    },
    // 找到指定节点的父节点
    findParent(tree, id) {
      for (const node of tree) {
        if (node.children && node.children.length > 0) {
          const index = node.children.findIndex((child) => child.id === id);
          if (index !== -1) {
            return node;
          } else {
            const found = this.findParent(node.children, id);
            if (found) {
              return found;
            }
          }
        }
      }
      return null;
    },
    getWellNodeId(node) {
      let currentNodeId = node.id;
      let currentNode = node;
      
      // 如果当前节点就是井节点，直接返回
      if (currentNode.moduleType === 2) {
        return currentNode.nodeDataId || currentNode.id;
      }
      
      // 向上遍历所有父节点查找井节点
      while (currentNodeId) {
        const parent = this.findParent(this.treeData, currentNodeId);
        if (parent) {
          // 检查父节点是否是井节点
          if (parent.moduleType === 2) {
            return parent.nodeDataId || parent.id;
          }
          currentNodeId = parent.id;
        } else {
          break;
        }
      }
      
      return null; // 没有找到井节点
    },
    // 新增方法：清空所有选中状态
    clearAllCheckedState() {
      this.checkedNodeIds = [];
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedKeys([]);
      }
      console.log('已清空所有选中状态');
    },
    // 新增方法：获取当前选中的节点ID数组
    getCheckedNodeIds() {
      return [...this.checkedNodeIds];
    },
    async handleImport(action, node) {
      switch (action) {
        case 'importWell':
          this.showImportWellDialog(node);
          break;
        case 'importWellbore':
          this.showImportWellboreDialog(node);
          break;
        case 'importCurve':
          this.showImportCurveDialog(node);
          break;
        case 'importModel':
          this.showImportModelDialog(node);
          break;
        // ... other import cases ...
      }
    },
    async showImportWellDialog(node) {
      this.currentNode = node;
      this.importWellDialogVisible = true;
      await this.loadWells();
    },
    async loadWells() {
      this.isLoadingWells = true;
      try {
        // 获取树结构第一个节点的id作为oilfieldId
        const oilfieldId = this.treeData[0]?.id;
        if (!oilfieldId) {
          throw new Error('未找到油田ID');
        }
        const response = await axios.get(`${this.vWebApiUrl}/oil/oilwell/GetWellListAllByOilFieldId?oilFieldId=${oilfieldId}`);
        if (response.data?.success) {
          this.wells = response.data.data;
        } else {
          throw new Error(response.data?.message || '获取井列表失败');
        }
      } catch (error) {
        console.error('获取井列表错误:', error);
        this.$message.error(error.message || '获取井列表失败');
      } finally {
        this.isLoadingWells = false;
      }
    },
    handleImportWellDialogClose() {
      this.importWellDialogVisible = false;
      this.importWellForm.wellId = '';
      this.$refs.importWellFormRef?.resetFields();
    },
    async handleImportWellConfirm() {
      if (this.isImportingWell) return;
      
      try {
        await this.$refs.importWellFormRef.validate();
        this.isImportingWell = true;
        
        // 获取选中的井信息
        const selectedWell = this.wells.find(well => well.id === this.importWellForm.wellId);
        if (!selectedWell) {
          throw new Error('未找到选中的井信息');
        }
        // 获取该井下的所有井眼数据
        const wellboresResponse = await axios.get(`${this.vWebApiUrl}/oil/oilwellbore/GetWellboreByWellId?wellId=${selectedWell.id}`);
        if (!wellboresResponse.data?.success) {
          throw new Error(wellboresResponse.data?.message || '获取井眼数据失败');
        }

        // 为每个井眼创建子节点
        const wellboreNodes = wellboresResponse.data.data.map(wellbore => ({
          id: wellbore.id,
          label: wellbore.wellboreNumber,
          moduleType: 4, // 井眼节点类型
          children: [
            {
              id: crypto.randomUUID(),
              wellboreId:wellbore.id,
              label: '实际轨迹',
              moduleType: 6
            },
            {
              id: crypto.randomUUID(),
              wellboreId:wellbore.id,
              label: '设计轨迹',
              moduleType: 6
            },
            {
              id: crypto.randomUUID(),
              wellboreId:wellbore.id,
              label: '插值成像',
              moduleType: 10,
              datasetId:null
            }
          ]
        }));

        // 在当前节点下添加井节点
        const newNode = {
          id: selectedWell.id,
          label: selectedWell.wellName,
          moduleType: 2, // 井节点类型
          children: []
        };

        // 将井眼节点添加到井节点下
        newNode.children = wellboreNodes;



        // 确保当前节点有children数组
        if (!this.currentNode.children) {
          this.currentNode.children = [];
        }

        // 检查是否已存在相同ID的节点
        const existingNode = this.currentNode.children.find(child => child.id === newNode.id);
        if (existingNode) {
          throw new Error('该井已存在于当前节点下');
        }

        // 添加新节点
        this.currentNode.children.push(newNode);

        // 保存树结构，不保存选中状态
        const response = await this.saveTree(false);
        
        if (response.data?.success) {
          // 导入新节点后，不恢复选中状态，保持新节点未选中
          await this.reloadTreeWithoutRestoringChecked();
          this.$message.success('导入井成功');
          this.handleImportWellDialogClose();
        } else {
          throw new Error(response.data?.message || '导入井失败');
        }
      } catch (error) {
        console.error('导入井错误:', error);
        this.$message.error(error.message || '导入井失败');
      } finally {
        this.isImportingWell = false;
      }
    },
    handleRenameDialogClose() {
      this.renameDialogVisible = false;
      this.$refs.renameFormRef.resetFields();
    },
    showCreateProjectPrompt() {
      this.$msgbox({
        message: "No project found. Would you like to create a new one?",
        title: "Prompt",
        showCancelButton: true,
        closeOnClickModal: false,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      })
        .then(() => {
          this.dialogVisible = true;
        })
        .catch(() => {
          console.log("User cancelled");
        });
    },
    async getOilFieldList() {
      this.isLoadingOilfields = true;
      try { 
        const response = await axios.get(`${this.vWebApiUrl}/oil/oilfield/ListAll`);
        if (response.data?.success) {
          this.oilfields = response.data.data;
        } else {
          throw new Error(response.data?.message || 'Failed to get oil fields');
        }
      } catch (error) {
        console.error('Error getting oil fields:', error);
        this.$message.error(error.message || 'Failed to get oil fields');
      } finally {
        this.isLoadingOilfields = false;
      }
    },
    async createProject() {
      if (this.isConfirmDisabled_createProject) return;

      try {
        await this.$refs.formRef.validate();
        this.isConfirmDisabled_createProject = true;

        const { projectName, note, oilfieldId } = this.form;
        const hashParams = new URLSearchParams(window.location.hash.split('?')[1]);
        const appId = hashParams.get("appId");

        if (!appId) throw new Error('Missing required appId parameter');
        if (!projectName?.trim()) throw new Error('Project name cannot be empty');
        if (!oilfieldId) throw new Error('Please select oil field');

        const requestUrl = `${this.vWebApiUrl}/visual3D/project/CreateProject?appId=${encodeURIComponent(appId)}&projectName=${encodeURIComponent(projectName.trim())}&remark=${encodeURIComponent(note?.trim() || '')}&oilfieldId=${encodeURIComponent(oilfieldId)}`;

        const response = await axios.post(requestUrl);

        if (response.data?.success) {
          if (response.data.data?.profile) {
            this.treeData = JSON.parse(response.data.data.profile);
          }

          const currentPath = window.location.hash.split('?')[0];
          const newHash = `${currentPath}?id=${response.data.data.id}&appId=${appId}`;
          window.location.hash = newHash;

          this.dialogVisible = false;
          this.$refs.formRef.resetFields();
          this.$message.success('Project created successfully');
        } else {
          throw new Error(response.data?.message || 'Failed to create project');
        }
      } catch (error) {
        console.error('Error creating project:', error);
        this.$message.error(error.message || 'Failed to create project');
      } finally {
        this.isConfirmDisabled_createProject = false;
      }
    },
    handleDialogClose() {
      this.dialogVisible = false;
      this.$refs.formRef.resetFields();
    },
    async loadTreeData() {
      this.isLoading = true;
      try {
        const hashParams = new URLSearchParams(window.location.hash.split('?')[1]);
        const appId = hashParams.get("appId");
        const projectId = hashParams.get("id");

        if (!appId) throw new Error('Missing required appId parameter');
        if (!projectId) throw new Error('Missing required projectId parameter');        
        const response = axios.get(
          `${this.vWebApiUrl}/project/project/GetTree?appId=${encodeURIComponent(appId)}&projectId=${encodeURIComponent(projectId)}`
        ).then((response) => {
              if (response.data?.success) {
              try {
                const originalTreeData = JSON.parse(response.data.data.profile);
                
                // 从树数据中提取选中信息并获取纯净的树数据
                const { cleanTreeData, checkedKeys, checkedNodeIds } = this.extractCheckedInfoFromTreeData(originalTreeData);
                
                console.log('从treeData中提取的选中状态:', {
                  checkedKeys,
                  checkedNodeIds
                });
                
                // 设置纯净的树数据
                this.treeData = cleanTreeData;
                // 更新本地选中状态数组
                this.checkedNodeIds = checkedNodeIds;
                                    
                // 恢复树组件的选中状态
                if (checkedKeys.length > 0) {
                  console.log('checkedKeys',checkedKeys);
                  this.$refs.tree.setCheckedKeys(checkedKeys);
                  console.log('已恢复树组件选中状态');
                  
                  // 延迟触发选中节点的相关事件，确保3D视图能正确接收
                  // setTimeout(() => {
                  //   this.triggerCheckedEvents();
                  // }, 500);
                }
                if (this.treeData?.length > 0 ) {
                  this.$refs.tree.setCurrentKey(this.treeData[0].id);
                }
              } catch (error) {
                throw new Error('Invalid tree data format');
              }
            }
            else {
              throw new Error(response.data?.message || 'Failed to load tree data');
            }
        });


      } catch (error) {
        console.error('Error loading tree data:', error);
        this.$message.error(error.message || 'Failed to load tree data');
      } finally {
        this.isLoading = false;
      }
    },
    async reloadTree() {
      try {
        const hashParams = new URLSearchParams(window.location.hash.split('?')[1]);
        const projectId = hashParams.get("id");
        
        if (!this.appId || !projectId) {
          throw new Error('缺少必要的参数');
        }

        const response = await axios.get(
          `${this.vWebApiUrl}/project/project/GetTree?appId=${encodeURIComponent(this.appId)}&projectId=${encodeURIComponent(projectId)}`
        );

        if (response.data?.success) {
          const originalTreeData = JSON.parse(response.data.data.profile);
          
          // 从树数据中提取选中信息并获取纯净的树数据
          const { cleanTreeData, checkedKeys, checkedNodeIds } = this.extractCheckedInfoFromTreeData(originalTreeData);
          
          console.log('重新加载时从treeData中提取的选中状态:', {
            checkedKeys,
            checkedNodeIds
          });
          
          // 设置纯净的树数据
          this.treeData = cleanTreeData;
          // 更新本地选中状态数组
          this.checkedNodeIds = checkedNodeIds;
          
          await this.$nextTick();
          
          // 恢复树组件的选中状态
          if (checkedKeys.length > 0) {
            this.$refs.tree.setCheckedKeys(checkedKeys);
            console.log('重新加载时已恢复树组件选中状态');
          }
          
          if (this.treeData?.length > 0) {
            this.$refs.tree.setCurrentKey(this.treeData[0].id);
          }
        } else {
          throw new Error(response.data?.message || '加载树结构失败');
        }
      } catch (error) {
        console.error('重新加载树结构错误:', error);
        this.$message.error(error.message || '重新加载树结构失败');
      }
    },
    // 新增方法：重新加载树但保持当前选中状态（用于导入新节点后）
    async reloadTreeWithoutRestoringChecked() {
      try {
        const hashParams = new URLSearchParams(window.location.hash.split('?')[1]);
        const projectId = hashParams.get("id");
        
        if (!this.appId || !projectId) {
          throw new Error('缺少必要的参数');
        }

        // 保存当前的选中状态
        const currentCheckedKeys = this.$refs.tree ? this.$refs.tree.getCheckedKeys() : [];
        const currentHalfCheckedKeys = this.$refs.tree ? this.$refs.tree.getHalfCheckedKeys() : [];
        
        console.log('保存当前选中状态:', {
          currentCheckedKeys,
          currentHalfCheckedKeys,
          checkedNodeIds: this.checkedNodeIds
        });

        const response = await axios.get(
          `${this.vWebApiUrl}/project/project/GetTree?appId=${encodeURIComponent(this.appId)}&projectId=${encodeURIComponent(projectId)}`
        );

        if (response.data?.success) {
          const originalTreeData = JSON.parse(response.data.data.profile);
          
          // 从树数据中提取选中信息并获取纯净的树数据
          const { cleanTreeData, checkedKeys, checkedNodeIds } = this.extractCheckedInfoFromTreeData(originalTreeData);
          
          console.log('导入新节点后重新加载，保持当前选中状态:', {
            checkedKeys,
            checkedNodeIds
          });
          
          // 设置纯净的树数据
          this.treeData = cleanTreeData;
          
          await this.$nextTick();
          
          // 恢复之前保存的选中状态
          if (currentCheckedKeys.length > 0) {
            this.$refs.tree.setCheckedKeys(currentCheckedKeys);
            console.log('已恢复当前选中状态，选中的节点ID:', currentCheckedKeys);
            
            // 更新checkedNodeIds数组以保持一致性
            this.checkedNodeIds = [...currentCheckedKeys];
            console.log('已更新checkedNodeIds数组:', this.checkedNodeIds);
            
            // 验证恢复是否成功
            const restoredCheckedKeys = this.$refs.tree.getCheckedKeys();
            console.log('恢复后的选中状态验证:', {
              original: currentCheckedKeys,
              restored: restoredCheckedKeys,
              isEqual: JSON.stringify(currentCheckedKeys.sort()) === JSON.stringify(restoredCheckedKeys.sort())
            });
          } else {
            console.log('没有需要恢复的选中状态');
            // 清空checkedNodeIds数组
            this.checkedNodeIds = [];
          }
          
          if (this.treeData?.length > 0) {
            this.$refs.tree.setCurrentKey(this.treeData[0].id);
          }
        } else {
          throw new Error(response.data?.message || '加载树结构失败');
        }
      } catch (error) {
        console.error('重新加载树结构错误:', error);
        this.$message.error(error.message || '重新加载树结构失败');
      }
    },
    // 新增方法：将选中信息添加到树数据中
    addCheckedInfoToTreeData(treeData, checkedKeys, halfCheckedKeys) {
      // 创建深度拷贝以避免修改原始数据
      const clonedData = JSON.parse(JSON.stringify(treeData));
      
      // 在根级别添加选中信息的元数据
      const treeWithMeta = {
        _checkedInfo: {
          checkedKeys: checkedKeys,
          halfCheckedKeys: halfCheckedKeys,
          checkedNodeIds: this.checkedNodeIds,          
        },
        data: clonedData
      };
      
      console.log('添加选中信息后的树数据:', treeWithMeta);
      return treeWithMeta;
    },
    
    // 新增方法：从树数据中提取选中信息
    extractCheckedInfoFromTreeData(originalTreeData) {
      let cleanTreeData = originalTreeData;
      let checkedKeys = [];
      let checkedNodeIds = [];
      
      // 检查是否包含选中信息的元数据
      if (originalTreeData._checkedInfo && originalTreeData.data) {
        // 提取选中信息
        checkedKeys = originalTreeData._checkedInfo.checkedKeys || [];
        checkedNodeIds = originalTreeData._checkedInfo.checkedNodeIds || [];
        
        // 获取纯净的树数据
        cleanTreeData = originalTreeData.data;
        
        console.log('从元数据中提取选中信息:', {
          checkedKeys,
          checkedNodeIds,          
        });
      } else {
        // 兼容旧格式，如果没有元数据，使用原有的空数组
        console.log('未找到选中信息元数据，使用默认值');
      }
      
      return {
        cleanTreeData,
        checkedKeys,
        checkedNodeIds
      };
    },
    
    // 新增方法：触发选中状态下的事件（用于恢复时重新触发相关逻辑）
    triggerCheckedEvents() {
      if (this.$refs.tree) {
        const checkedNodes = this.$refs.tree.getCheckedNodes();
        console.log('触发已选中节点的事件:', checkedNodes);
        
        checkedNodes.forEach(node => {
          // 模拟checkbox选中事件，触发相关业务逻辑
          this.handleCheckForRestore(node, true);
        });
      }
    },
    // 新增方法：专门用于恢复时的check处理
    handleCheckForRestore(data, isChecked) {
      console.log('恢复选中状态时触发事件, data:', data, 'isChecked:', isChecked);
      
      // 根据不同的moduleType触发相应的事件
      if (data.moduleType === 6) {
        const selectedId = data.wellboreId;
        let curveType;
        if(data.label === "实际轨迹") {
          curveType = '1';
        }
        else if(data.label === "设计轨迹") {
          curveType = '0';
        }
        
        this.$emit('checkbox-choose', {
          id: selectedId,
          curveType: curveType,
          isChecked: isChecked,
          moduleType: data.moduleType,
          wellboreId: ""
        });
      }
      else if(data.moduleType === 9) {
        const selectedId = data.id;
        const curveType = '99';
        
        this.$emit('checkbox-choose', {
          id: selectedId,
          curveType: curveType,
          isChecked: isChecked,
          moduleType: data.moduleType,
          wellboreId: ""
        });
      }
      else if(data.moduleType === 7) {
        const selectedId = data.id;
        const wellboreId = data.wellboreId;
        const curveType = '99';

        this.$emit('checkbox-choose', {
          id: selectedId,
          curveType: curveType,
          isChecked: isChecked,
          moduleType: data.moduleType,
          wellboreId: wellboreId
        });
      }
      else if(data.moduleType === 10) {
        const selectedId = data.id;
        const wellboreId = data.wellboreId;
        const curveType = '99';        
        if(data.datasetId){
          this.$emit('checkbox-choose', {
            id: selectedId,
            curveType: curveType,
            isChecked: isChecked,
            moduleType: data.moduleType,
            wellboreId: wellboreId,
            datasetId: data.datasetId
          });
        }
      }
    },
    async showImportWellboreDialog(node) {
      this.currentNode = node;
      this.importWellboreDialogVisible = true;
      await this.loadWellbores();
    },
    async loadWellbores() {
      this.isLoadingWellbores = true;
      try {
        const response = await axios.get(`${this.vWebApiUrl}/oil/oilwellbore/GetWellboreByWellId?wellId=${this.currentNode.id}`);
        if (response.data?.success) {
          this.wellbores = response.data.data;
        } else {
          throw new Error(response.data?.message || '获取井眼列表失败');
        }
      } catch (error) {
        console.error('获取井眼列表错误:', error);
        this.$message.error(error.message || '获取井眼列表失败');
      } finally {
        this.isLoadingWellbores = false;
      }
    },
    handleImportWellboreDialogClose() {
      this.importWellboreDialogVisible = false;
      this.importWellboloadTreeDatareForm.wellboreId = '';
      this.$refs.importWellboreFormRef?.resetFields();
    },
    async handleImportWellboreConfirm() {
      if (this.isImportingWellbore) return;
      
      try {
        await this.$refs.importWellboreFormRef.validate();
        this.isImportingWellbore = true;
        
        // 获取选中的井眼信息
        const selectedWellbore = this.wellbores.find(wellbore => wellbore.id === this.importWellboreForm.wellboreId);
        if (!selectedWellbore) {
          throw new Error('未找到选中的井眼信息');
        }

        // 在当前节点下添加井眼节点
        const newNode = {
          id: selectedWellbore.id,
          label: selectedWellbore.wellboreNumber,
          moduleType: 4, // 井眼节点类型
          children: [
            {
              id: crypto.randomUUID(),
              label: '实际轨迹',
              moduleType: 6
            },
            {
              id: crypto.randomUUID(),
              label: '设计轨迹',
              moduleType: 6
            },
            {
              id: crypto.randomUUID(),
              label: '插值成像',
              moduleType: 10
            },
          ]
        };

        // 确保当前节点有children数组
        if (!this.currentNode.children) {
          this.currentNode.children = [];
        }

        // 检查是否已存在相同ID的节点
        const existingNode = this.currentNode.children.find(child => child.id === newNode.id);
        if (existingNode) {
          throw new Error('该井眼已存在于当前节点下');
        }

        // 添加新节点
        this.currentNode.children.push(newNode);

        // 保存树结构，不保存选中状态
        const response = await this.saveTree(false);
        
        if (response.data?.success) {
          // 导入新节点后，不恢复选中状态，保持新节点未选中
          await this.reloadTreeWithoutRestoringChecked();
          this.$message.success('导入井眼成功');
          this.handleImportWellboreDialogClose();
        } else {
          throw new Error(response.data?.message || '导入井眼失败');
        }
      } catch (error) {
        console.error('导入井眼错误:', error);
        this.$message.error(error.message || '导入井眼失败');
      } finally {
        this.isImportingWellbore = false;
      }
    },
    async showImportCurveDialog(node) {
      this.currentNode = node;
      this.importCurveDialogVisible = true;
      await this.loadDatasets();
    },
    async loadDatasets() {
      this.isLoadingDatasets = true;
      try {
        const response = await axios.get(`${this.vWebApiUrl}/oil/logdatafile/GetDatasetListByWellboreId?wellboreId=${this.currentNode.id}`);
        if (response.data?.success) {
          this.datasets = response.data.data;
        } else {
          throw new Error(response.data?.message || '获取数据集列表失败');
        }
      } catch (error) {
        console.error('获取数据集列表错误:', error);
        this.$message.error(error.message || '获取数据集列表失败');
      } finally {
        this.isLoadingDatasets = false;
      }
    },
    async handleDatasetChange(datasetId) {
      this.importCurveForm.curveId = '';
      if (datasetId) {
        await this.loadCurves(datasetId);
      } else {
        this.curves = [];
      }
    },
    async loadCurves(datasetId) {
      this.isLoadingCurves = true;
      try {
        const response = await axios.get(`${this.vWebApiUrl}/oil/logdatafile/GetChannelList?datasetId=${datasetId}&&wellboreId=${this.currentNode.id}`);
        if (response.data?.success) {
          this.curves = response.data.data;
        } else {
          throw new Error(response.data?.message || '获取曲线列表失败');
        }
      } catch (error) {
        console.error('获取曲线列表错误:', error);
        this.$message.error(error.message || '获取曲线列表失败');
      } finally {
        this.isLoadingCurves = false;
      }
    },
    handleImportCurveDialogClose() {
      this.importCurveDialogVisible = false;
      this.importCurveForm.datasetId = '';
      this.importCurveForm.curveId = '';
      this.curves = [];
      this.$refs.importCurveFormRef?.resetFields();
    },
    async handleImportCurveConfirm() {
      if (this.isImportingCurve) return;
      
      try {
        await this.$refs.importCurveFormRef.validate();
        this.isImportingCurve = true;
        
        // 获取选中的数据集和曲线信息
        const selectedDataset = this.datasets.find(dataset => dataset.id === this.importCurveForm.datasetId);
        const selectedCurve = this.curves.find(curve => curve.id === this.importCurveForm.curveId);
        
        if (!selectedDataset || !selectedCurve) {
          throw new Error('未找到选中的数据集或曲线信息');
        }


        // 创建曲线节点
        const curveNode = {
          id: selectedCurve.id,
          label: selectedCurve.name+'('+selectedDataset.name+')',
          moduleType: 7, // 曲线节点类型
          wellboreId:this.currentNode.id,
          datasetId:selectedDataset.id,
          children: []
        };


        // 添加曲线节点
        this.currentNode.children.push(curveNode);

        // 保存树结构，不保存选中状态
        const response = await this.saveTree(false);
        
        if (response.data?.success) {
          // 导入新节点后，不恢复选中状态，保持新节点未选中
          await this.reloadTreeWithoutRestoringChecked();
          this.$message.success('导入曲线成功');
          this.handleImportCurveDialogClose();
        } else {
          throw new Error(response.data?.message || '导入曲线失败');
        }
      } catch (error) {
        console.error('导入曲线错误:', error);
        this.$message.error(error.message || '导入曲线失败');
      } finally {
        this.isImportingCurve = false;
      }
    },
    generateProjectName() {
      const oilfield = this.oilfields.find(field => field.id === this.form.oilfieldId);
      const oilfieldName = oilfield ? oilfield.cOilFieldName : '';
      const randomText = Math.random().toString(36).substring(2, 8);
      this.form.projectName = `三维可视化_${oilfieldName}_${randomText}`;
    },
    handleCheck(data, checked) {
      console.log('ProjectTree handleCheck被调用, data:', data);
      console.log('data.moduleType:', data.moduleType);
      console.log('checked对象:', { checkedNodes: checked.checkedNodes.length, checkedKeys: checked.checkedKeys.length });
      console.log('checked.checkedNodes是否包含data:', checked.checkedNodes.includes(data));
      
      // 检查节点是否在选中状态
      const isChecked = checked.checkedNodes.includes(data);
      
      // 更新选中节点ID数组
      if (isChecked) {
        // 如果节点被选中且不在数组中，则添加
        if (!this.checkedNodeIds.includes(data.id)) {
          this.checkedNodeIds.push(data.id);
        }
      } else {
        // 如果节点未被选中，则从数组中移除
        const index = this.checkedNodeIds.indexOf(data.id);
        if (index > -1) {
          this.checkedNodeIds.splice(index, 1);
        }
      }
      
      console.log('当前选中的节点ID数组:', this.checkedNodeIds);
      
      // 只处理moduleType为6的节点（轨迹节点）
      if (data.moduleType === 6) {
        const selectedId = data.wellboreId; // 获取井眼ID
        let curveType;
        if(data.label === "实际轨迹") {
          curveType = '1';
        }
        else if(data.label === "设计轨迹") {
          curveType = '0';
        }
        
        console.log('轨迹节点选中状态:', isChecked);      

       // 发送事件，包含选中状态
       this.$emit('checkbox-choose', {
          id: selectedId,
          curveType: curveType,
          isChecked: isChecked,
          moduleType: data.moduleType,
          wellboreId:""
        });
      }
      else if(data.moduleType === 9) {
        // 地层模型节点
        const selectedId = data.id; // 获取模型ID
        const curveType = '99';
        
        console.log('地层模型节点, data:', data);
        console.log('地层模型ID:', selectedId);
        console.log('地层模型选中状态:', isChecked);
        console.log('发送地层模型事件:', {
          id: selectedId,
          curveType: curveType,
          isChecked: isChecked,
          moduleType: data.moduleType
        });      

        // 发送事件，包含选中状态
       this.$emit('checkbox-choose', {
          id: selectedId,
          curveType: curveType,
          isChecked: isChecked,
          moduleType: data.moduleType,
          wellboreId:""
        });
      }
      else if(data.moduleType === 7) {
        // 曲线节点
        const selectedId = data.id; // 获取曲线ID
        const wellboreId = data.wellboreId;
        const curveType = '99';

        console.log('曲线节点, data:', data);
        console.log('曲线ID:', selectedId);
        console.log('曲线节点选中状态:', isChecked);

        // 发送事件，包含选中状态
       this.$emit('checkbox-choose', {
          id: selectedId,
          curveType: curveType,
          isChecked: isChecked,
          moduleType: data.moduleType,
          wellboreId:wellboreId
        });
      }
      else if(data.moduleType === 10) {
        // 插值成像节点
        const selectedId = data.id; // 获取插值成像ID
        const wellboreId = data.wellboreId;
        const curveType = '99';        
        if(data.datasetId){
          // 发送事件，包含选中状态
          this.$emit('checkbox-choose', {
            id: selectedId,
            curveType: curveType,
            isChecked: isChecked,
            moduleType: data.moduleType,
            wellboreId:wellboreId,
            datasetId:data.datasetId
          });
        }
        else{
          alert('Please choose a dataset first');
          return;
        }

      }
    },
    /**
     * 显示导入模型弹框
     */
    showImportModelDialog(node) {
      this.currentNode = node;
      this.importModelDialogVisible = true;
    },

    /**
     * 处理模型导入成功事件
     */
    async handleModelImported(data) {
      console.log(data, 'data============')
      // 保存树结构
      const response = await this.saveTree();
      
      if (response.data?.success) {
        await this.reloadTree();
        // 触发3D视图更新
        this.$emit('model-imported', data);
      } else {
        throw new Error(response.data?.message || '保存树结构失败');
      }
    },
    handleAddModel(tempModels) {
      this.currentNode.children = [];
      this.currentNode.children.push(...tempModels);
    },

    /**
     * 处理树结构保存请求
     */
    async handleTreeSaveRequired() {
      try {
        const response = await this.saveTree();
        if (response.data?.success) {
          await this.reloadTree();
        } else {
          throw new Error(response.data?.message || '保存树结构失败');
        }
      } catch (error) {
        console.error('保存树结构错误:', error);
        this.$message.error(error.message || '保存树结构失败');
      }
    },
    async showSettingDialog(node) {
      const nodeId = node.id;
      console.log('start this.settingCache:', this.settingCache);
      // 优先从缓存读取
      if (this.settingCache[nodeId]) {
        const cache = this.settingCache[nodeId];
        this.settingForm.wellboreThickness = cache.wellboreThickness;
        this.settingForm.min = cache.min;
        this.settingForm.max = cache.max;        
        this.settingCurrentNode = node;
        this.settingForm.displayMode = cache.displayMode;        
        this.settingForm.color = cache.color;
        this.settingForm.minCalibrate = cache.minCalibrate;
        this.settingForm.maxCalibrate = cache.maxCalibrate;
        this.settingForm.minColor = cache.minColor;
        this.settingForm.maxColor = cache.maxColor;
      }
      else{
        //获取当前节点井眼井径 以及当前曲线的最大最小值
        const wellboreId = node.wellboreId;      
        const requestUrl = `${this.vWebApiUrl}/visual3D/project/GetWellboreInfo?wellboreId=${wellboreId}`;
        const response = await axios.post(requestUrl);
        if (response.data?.success) {
          this.settingForm.wellboreThickness = response.data.data.caliper;
        }
        
        const requestUrl2 = `${this.vWebApiUrl}/visual3D/project/GetChannelData?wellboreId=${wellboreId}&channelId=${node.id}`;
        const response2 = await axios.post(requestUrl2);
        if (response2.data?.success) {
          const points=response2.data.data.map(point =>({
            modelData: point.modelData
          }));
          console.log('points:', points);
          this.settingForm.min = Math.min(...points.map(point => point.modelData));
          this.settingForm.max = Math.max(...points.map(point => point.modelData));
        }
        
        // 新增：请求后写入缓存
        this.settingCache[nodeId] = {
          wellboreThickness: this.settingForm.wellboreThickness,
          min: this.settingForm.min,
          max: this.settingForm.max,
          displayMode: this.settingForm.displayMode,
          color: this.settingForm.color,
          minCalibrate: this.settingForm.minCalibrate,
          maxCalibrate: this.settingForm.maxCalibrate,
          minColor: this.settingForm.minColor,
          maxColor: this.settingForm.maxColor
        };      
        this.settingCurrentNode = node;
      }


      //this.settingDialogVisible = true;
      console.log('end this.settingCache:', this.settingCache);
      this.$emit('setting-change', this.settingCurrentNode, this.settingForm.wellboreThickness, this.settingForm.displayMode, this.settingForm.min, this.settingForm.max,this.settingForm.color ,this.settingForm.minCalibrate, this.settingForm.maxCalibrate, this.settingForm.minColor, this.settingForm.maxColor);
    },
    //弃用
    handleSettingDialogClose() {
      this.settingDialogVisible = false;
    },
    //弃用
    handleSettingConfirm() {      
      // 发送设置变更事件    
      this.$emit('setting-change', this.settingCurrentNode, this.settingForm.wellboreThickness, this.settingForm.displayMode, this.settingForm.min, this.settingForm.max, this.settingForm.color,this.settingForm.minCalibrate, this.settingForm.maxCalibrate, this.settingForm.minColor, this.settingForm.maxColor);
      // 新增：同步更新缓存
      if (this.settingCurrentNode) {
        this.settingCache[this.settingCurrentNode.id] = {
          wellboreThickness: this.settingForm.wellboreThickness,
          min: this.settingForm.min,
          max: this.settingForm.max,
          displayMode: this.settingForm.displayMode,
          minCalibrate: this.settingForm.minCalibrate,
          maxCalibrate: this.settingForm.maxCalibrate,
          minColor: this.settingForm.minColor,
          maxColor: this.settingForm.maxColor
        };
      }
      this.handleSettingDialogClose();
    },
    updateSettingCache(formOption){
      console.log('进入updateSettingCache');        
      // 从formOption中获取节点ID
      const nodeId = formOption.formNode.id;
      
      // 从options数组中获取各个值
      const options = formOption.options;
      const wellboreThickness = options.find(opt => opt.label === '粗细')?.value;
      const displayMode = options.find(opt => opt.label === '展示方式')?.value;
      const min = options.find(opt => opt.label === 'Min Value')?.value;
      const max = options.find(opt => opt.label === 'Max Value')?.value;
      const color = options.find(opt => opt.label === '颜色')?.value;
      const minCalibrate = options.find(opt => opt.label === 'Min Calibrate')?.value;
      const maxCalibrate = options.find(opt => opt.label === 'Max Calibrate')?.value;
      const minColor = options.find(opt => opt.label === 'Min Color')?.value;
      const maxColor = options.find(opt => opt.label === 'Max Color')?.value;
      
      console.log('formOption.options:', formOption.options);
      // 更新缓存
      this.settingCache[nodeId] = {
        wellboreThickness: wellboreThickness,
        min: min,
        max: max,
        displayMode: displayMode,
        color: color,
        minCalibrate: minCalibrate,
        maxCalibrate: maxCalibrate,
        minColor: minColor,
        maxColor: maxColor
      };
      
      // 发送设置变更事件
      this.$emit('setting-change', formOption.formNode, wellboreThickness, displayMode, min, max, color, minCalibrate, maxCalibrate, minColor, maxColor);
    },
    async showChooseDatasetDialog(node) {
      this.currentNode = node;
      this.chooseDatasetDialogVisible = true;
      
      // 如果节点已有datasetId，设置为选中状态
      if (node.datasetId) {
        this.chooseDatasetForm.datasetId = node.datasetId;
      }
      
      await this.loadDatasetsForChoose();
    },
    async loadDatasetsForChoose() {
      this.isLoadingDatasetsForChoose = true;
      try {
        // 需要找到对应的井眼ID
        const wellboreId = this.currentNode.wellboreId;
        const response = await axios.get(`${this.vWebApiUrl}/oil/logdatafile/GetDatasetListByWellboreId?wellboreId=${wellboreId}`);
        if (response.data?.success) {
          this.datasetsForChoose = response.data.data;
        } else {
          throw new Error(response.data?.message || '获取数据集列表失败');
        }
      } catch (error) {
        console.error('获取数据集列表错误:', error);
        this.$message.error(error.message || '获取数据集列表失败');
      } finally {
        this.isLoadingDatasetsForChoose = false;
      }
    },
    handleChooseDatasetDialogClose() {
      this.chooseDatasetDialogVisible = false;
      this.chooseDatasetForm.datasetId = '';
      this.$refs.chooseDatasetFormRef?.resetFields();
    },
    async handleChooseDatasetConfirm() {
      if (this.isChoosingDataset) return;
      
      try {
        await this.$refs.chooseDatasetFormRef.validate();
        this.isChoosingDataset = true;
        
        // 获取选中的数据集信息
        const selectedDataset = this.datasetsForChoose.find(dataset => dataset.id === this.chooseDatasetForm.datasetId);
        if (!selectedDataset) {
          throw new Error('未找到选中的数据集信息');
        }
        
        // 为当前节点添加datasetId属性
        this.currentNode.datasetId = this.chooseDatasetForm.datasetId;
        console.log('this.currentNode:', this.currentNode.datasetId);
        // 更新节点的label为'插值成像(数据集名称)'
        this.currentNode.label = `插值成像(${selectedDataset.name})`;
        
        // 保存树结构，不保存选中状态
        const response = await this.saveTree(false);
        
        if (response.data?.success) {
          // 导入新节点后，不恢复选中状态，保持新节点未选中
          await this.reloadTreeWithoutRestoringChecked();
          this.$message.success('选择数据集成功');
          this.handleChooseDatasetDialogClose();
        } else {
          throw new Error(response.data?.message || '保存失败');
        }
      } catch (error) {
        console.error('选择数据集错误:', error);
        this.$message.error(error.message || '选择数据集失败');
      } finally {
        this.isChoosingDataset = false;
      }
    },
    // 新增方法：打印树信息
    logTreeInfo() {
      console.log('=== El-Tree 信息 ===');
      console.log('1. 树数据 (treeData):', this.treeData);
      console.log('2. 当前选中节点 (currentNode):', this.currentNode);
      console.log('3. 选中的节点ID数组 (checkedNodeIds):', this.checkedNodeIds);
      console.log('4. 树组件实例:', this.$refs.tree);
      
      if (this.$refs.tree) {
        console.log('5. 当前选中的节点key:', this.$refs.tree.getCurrentKey());
        console.log('6. 当前选中的节点数据:', this.$refs.tree.getCurrentNode());
        console.log('7. 选中的节点keys:', this.$refs.tree.getCheckedKeys());
        console.log('8. 选中的节点数据:', this.$refs.tree.getCheckedNodes());
        console.log('9. 半选中的节点keys:', this.$refs.tree.getHalfCheckedKeys());
        console.log('10. 半选中的节点数据:', this.$refs.tree.getHalfCheckedNodes());
      }
      
      console.log('11. 设置缓存 (settingCache):', this.settingCache);
      console.log('12. 应用ID (appId):', this.appId);
      console.log('13. 项目ID (id):', this.id);
      console.log('14. 是否正在加载 (isLoading):', this.isLoading);
      
      // 测试保存格式
      const checkedKeys = this.$refs.tree ? this.$refs.tree.getCheckedKeys() : [];
      const halfCheckedKeys = this.$refs.tree ? this.$refs.tree.getHalfCheckedKeys() : [];
      const treeDataWithChecked = this.addCheckedInfoToTreeData(this.treeData, checkedKeys, halfCheckedKeys);
      console.log('15. 保存格式预览:', treeDataWithChecked);
      
      console.log('=== 信息输出完毕 ===');
    },
    // 保存树信息
    async saveTreeInfo() {
      try {
        const response = await this.saveTree();
        const hashParams = new URLSearchParams(window.location.hash.split('?')[1]);        
        const id = hashParams.get("id");
        if (response.data?.success) {
          this.$message.success('保存成功');
          await this.reloadTree();
          //保存场景信息
          this.$emit('save-scene', id);
        } else {
          throw new Error(response.data?.message || '保存失败');
        }
      } catch (error) {
        console.error('保存树结构错误:', error);
        this.$message.error(error.message || '保存树结构失败');
      }
    },
  },
  mounted() {
    const hashParams = new URLSearchParams(window.location.hash.split('?')[1]);
    this.appId = hashParams.get("appId");
    this.id = hashParams.get("id");
    
    this.getOilFieldList();
    
    
    if (!this.id) {
      this.showCreateProjectPrompt();
    } else {
      this.loadTreeData();      
    }

    const store = useResourceTreeStore();
    store.registerTreeInstance(this);
  }
}
</script>

<style scoped>
.project-tree-container {
  height: 100%;
  position: relative;
}

.custom-tree {
  margin: 10px;
  height: calc(100% - 70px); /* 调整高度为按钮留出空间 */
  overflow: auto;
}

.debug-toolbar {
  border-bottom: 1px solid #ebeef5;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.form-item {
  margin-bottom: 20px;
}

.input-field {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(.el-tree-node__content:hover) {
  background-color: var(--el-tree-node-hover-bg-color);
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: var(--el-tree-node-hover-bg-color);
}

.project-name-input {
  display: flex;
  gap: 10px;
  align-items: center;
}

.project-name-input .input-field {
  flex: 1;
}

.upload-demo {
  width: 100%;
}

.el-upload-dragger {
  width: 100%;
}

.el-upload__tip {
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}
</style>