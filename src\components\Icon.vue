<template>
  <span 
    class="icon-wrapper"
    :class="[
      `icon-size-${size}`,
      { 'icon-clickable': clickable }
    ]"
    :style="iconStyle"
    @click="handleClick"
  >
    <!-- SVG 图标 -->
    <img 
      v-if="isSvg"
      :src="iconSrc"
      :alt="alt"
      class="icon-svg"
      :style="{ filter: svgFilter }"
    />
    
    <!-- PNG/其他格式图标 -->
    <img 
      v-else-if="isPng"
      :src="iconSrc"
      :alt="alt"
      class="icon-image"
    />
    
    <!-- 图标字体 (如果需要支持) -->
    <i 
      v-else-if="isIconFont"
      :class="iconClass"
    ></i>
    
    <!-- 插槽支持自定义内容 -->
    <slot v-else></slot>
  </span>
</template>

<script setup lang="ts">
import { computed, type CSSProperties } from 'vue'

interface Props {
  /** 图标名称或路径 */
  name?: string
  /** 图标大小 */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | number
  /** 图标颜色 (仅对SVG有效) */
  color?: string
  /** 旋转角度 */
  rotate?: number
  /** 是否可点击 */
  clickable?: boolean
  /** 图标类名 (用于图标字体) */
  iconClass?: string
  /** alt文本 */
  alt?: string
  /** 自定义样式 */
  customStyle?: CSSProperties
}

// 设置默认props
const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  clickable: false,
  alt: 'icon'
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

// 判断图标类型
const isSvg = computed(() => {
  return props.name?.endsWith('.svg')
})

const isPng = computed(() => {
  return props.name?.match(/\.(png|jpg|jpeg|gif|webp)$/i)
})

const isIconFont = computed(() => {
  return props.iconClass && !props.name
})

// 获取图标路径
const iconSrc = computed(() => {
  if (!props.name) return ''
  
  // 如果是完整路径，直接使用
  if (props.name.startsWith('http') || props.name.startsWith('/')) {
    return props.name
  }
  
  // 否则从 icon 目录获取
  return new URL(`../icon/tree/${props.name}`, import.meta.url).href
})

// SVG 颜色滤镜
const svgFilter = computed(() => {
  if (!props.color || !isSvg.value) return undefined
  
  // 简单的颜色转换，你可以根据需要扩展
  const colorMap: Record<string, string> = {
    'white': 'brightness(0) invert(1)',
    'black': 'brightness(0)',
    'primary': 'hue-rotate(210deg) saturate(2)',
    'success': 'hue-rotate(120deg) saturate(2)',
    'warning': 'hue-rotate(45deg) saturate(2)',
    'danger': 'hue-rotate(0deg) saturate(2)'
  }
  
  return colorMap[props.color] || `hue-rotate(${props.color})`
})

// 图标样式
const iconStyle = computed((): CSSProperties => {
  const style: CSSProperties = {
    ...props.customStyle
  }
  
  // 自定义尺寸
  if (typeof props.size === 'number') {
    style.width = `${props.size}px`
    style.height = `${props.size}px`
  }
  
  // 旋转
  if (props.rotate) {
    style.transform = `rotate(${props.rotate}deg)`
  }
  
  return style
})

// 点击处理
const handleClick = (event: MouseEvent) => {
  if (props.clickable) {
    emit('click', event)
  }
}
</script>

<style scoped>
.icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  transition: all 0.2s ease;
}

.icon-clickable {
  cursor: pointer;
}

.icon-clickable:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

.icon-svg,
.icon-image {
  display: block;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 预设尺寸 */
.icon-size-xs {
  width: 12px;
  height: 12px;
}

.icon-size-sm {
  width: 16px;
  height: 16px;
}

.icon-size-md {
  width: 20px;
  height: 20px;
}

.icon-size-lg {
  width: 24px;
  height: 24px;
}

.icon-size-xl {
  width: 32px;
  height: 32px;
}
</style>