import * as THREE from 'three';
import { ThreeUtils } from '../utils/ThreeUtils';
import type { InteractiveObject } from '../utils/TypeHelpers';

type PointerEventHandlers = {
  onClick?: (obj: THREE.Object3D) => void;
  onHoverStart?: (obj: THREE.Object3D) => void;
  onHoverEnd?: (obj: THREE.Object3D) => void;
  onDragStart?: (obj: THREE.Object3D) => void;
  onDrag?: (delta: THREE.Vector3, obj: THREE.Object3D) => void; // 添加第二个参数
  onDragEnd?: () => void;
};

export class PointerController {
  private hoveredObject: THREE.Object3D | null = null;
  private draggedObject: THREE.Object3D | null = null;
  private startPosition = new THREE.Vector3();
  private camera: THREE.Camera;
  private renderer: THREE.WebGLRenderer;
  private isDragging = false;

  constructor(
    private scene: THREE.Scene,
    camera: THREE.Camera,
    renderer: THREE.WebGLRenderer,
    private handlers: PointerEventHandlers = {}
  ) {
    this.camera = camera;
    this.renderer = renderer;
    this.initListeners();
  }

  private initListeners() {
    const dom = this.renderer.domElement;
    dom.addEventListener('click', this.handleClick);
    dom.addEventListener('mousemove', this.handleMove);
    dom.addEventListener('mousedown', this.handleDragStart);
    dom.addEventListener('mouseup', this.handleDragEnd);
    dom.addEventListener('touchstart', this.handleDragStart, { passive: false });
    dom.addEventListener('touchend', this.handleDragEnd);
    dom.addEventListener('touchmove', this.handleTouchMove, { passive: false });
  }

  private handleClick = (event: MouseEvent) => {
    if (this.isDragging) return;
    const intersects = this.getIntersects(event);
    if (intersects.length > 0) {
      const obj = intersects[0].object;
      this.handlers.onClick?.(obj);
      (obj as InteractiveObject)?.userData?.onClick?.();
    }
  };

  private handleMove = (event: MouseEvent) => {
    const intersects = this.getIntersects(event);
    const currentObj = intersects[0]?.object;

    if (currentObj !== this.hoveredObject) {
      this.hoveredObject && this.handleHoverEnd(this.hoveredObject);
      currentObj && this.handleHoverStart(currentObj);
      this.hoveredObject = currentObj || null;
    }
  };

  private handleHoverStart(obj: THREE.Object3D) {
    this.handlers.onHoverStart?.(obj);
    (obj as InteractiveObject)?.userData?.onHover?.(true);
  }

  private handleHoverEnd(obj: THREE.Object3D) {
    this.handlers.onHoverEnd?.(obj);
    (obj as InteractiveObject)?.userData?.onHover?.(false);
  }

  private handleDragStart = (event: MouseEvent | TouchEvent) => {
    event.preventDefault();
    const clientX = this.getClientX(event);
    const clientY = this.getClientY(event);
    
    const intersects = this.getIntersects({ clientX, clientY });
    if (intersects.length > 0) {
      this.isDragging = true;
      this.draggedObject = intersects[0].object;
      this.startPosition.copy(intersects[0].point);
      this.handlers.onDragStart?.(this.draggedObject);
    }
  };

  private handleDrag = (event: MouseEvent | TouchEvent) => {
    if (!this.draggedObject || !this.isDragging) return;
  
    event.preventDefault();
    const clientX = this.getClientX(event);
    const clientY = this.getClientY(event);
    
    const currentPosition = ThreeUtils.screenToWorld(
      { x: clientX, y: clientY },
      this.camera
    );
    const delta = currentPosition.sub(this.startPosition);
    
    // 传递两个参数
    this.handlers.onDrag?.(delta, this.draggedObject);
  };

  private handleDragEnd = () => {
    if (this.draggedObject) {
      this.isDragging = false;
      this.handlers.onDragEnd?.();
      this.draggedObject = null;
    }
  };

  private handleTouchMove = (event: TouchEvent) => {
    event.preventDefault();
    if (event.touches.length === 1) {
      this.handleDrag(event);
    }
  };

  private getClientX(event: MouseEvent | TouchEvent): number {
    return event instanceof MouseEvent ? event.clientX : event.touches[0].clientX;
  }

  private getClientY(event: MouseEvent | TouchEvent): number {
    return event instanceof MouseEvent ? event.clientY : event.touches[0].clientY;
  }

  private getIntersects(event: { clientX: number; clientY: number }) {
    return ThreeUtils.raycastFromScreen(
      { x: event.clientX, y: event.clientY },
      this.camera,
      this.scene.children,
      this.renderer
    );
  }

  dispose() {
    const dom = this.renderer.domElement;
    dom.removeEventListener('click', this.handleClick);
    dom.removeEventListener('mousemove', this.handleMove);
    dom.removeEventListener('mousedown', this.handleDragStart);
    dom.removeEventListener('mouseup', this.handleDragEnd);
    dom.removeEventListener('touchstart', this.handleDragStart);
    dom.removeEventListener('touchend', this.handleDragEnd);
    dom.removeEventListener('touchmove', this.handleTouchMove);
  }
}