<template>
    <div class="wells-detail">
       <!-- 固定头部 -->
       <div class="header">
         <Title :name="wellsName" showIcon @clickIcon="clickIcon" />
       </div>
       
       <!-- 固定标签栏 -->
       <div class="tabs-wrapper">
        <van-tabs v-model:active="active" animated>
            <van-tab v-for="item in tablist" :title="item.title" :key="item.key" />
        </van-tabs>
       </div>
       
       <!-- 可滚动内容区域 -->
       <div class="content">
         <component 
           :is="currentComponent" 
           v-if="currentComponent"
         />
       </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, defineAsyncComponent } from 'vue'
import Title from '@/views/DwprMobile/components/common/Title.vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const id = route.query.id

const wellsName = route.query.name as string || ''
const router = useRouter()

const clickIcon = () => {
    router.back()
}

// Tab 枚举定义
enum Tab {
    TASKS = 'Tasks',
    DETAIL = 'Detail',
    TRAJECTORY = 'Trajectory',
    LOGS = 'Logs',
}

// Tab 列表配置
const tablist = [
    {
        title: Tab.TASKS,
        key: Tab.TASKS,
    },
    {
        title: Tab.DETAIL,
        key: Tab.DETAIL,
    },
    {
        title: Tab.TRAJECTORY,
        key: Tab.TRAJECTORY,
    },
    {
        title: Tab.LOGS,
        key: Tab.LOGS,
    },
]

// 动态组件映射
const componentMap = {
    [Tab.TASKS]: defineAsyncComponent(() => import('@/views/DwprMobile/components/wells/TasksTab.vue')),
    [Tab.DETAIL]: defineAsyncComponent(() => import('@/views/DwprMobile/components/wells/DetailTab.vue')),
    [Tab.TRAJECTORY]: defineAsyncComponent(() => import('@/views/DwprMobile/components/wells/TrajectoryTab.vue')),
    [Tab.LOGS]: defineAsyncComponent(() => import('@/views/DwprMobile/components/wells/LogsTab.vue'))
}

// 当前激活的 tab 索引
const active = ref<number>(0)

// 当前组件计算属性
const currentComponent = computed(() => {
    const currentTab = tablist[active.value]
    return currentTab ? componentMap[currentTab.key as Tab] : null
})




</script>

<style scoped lang="less">
.wells-detail {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    
    .header {
        flex-shrink: 0;
        position: sticky;
        top: 0;
        background: #4472C4;
        z-index: 100;
    }
    
    .tabs-wrapper {
        flex-shrink: 0;
        position: sticky;
        top: 2.75rem; // 根据 header 高度调整
        z-index: 99;
        background: #fff;
        border-bottom: .0625rem solid #eee;
        
        :deep(.van-tabs__nav) {
            background: #fff;
        }
        :deep(.van-tabs__line) {
            background: #4472C4;
        }
        
        :deep(.van-tab) {
            font-size: 1.25rem;
            font-weight: 350;
            color: rgba(23,26,29,0.6);
            line-height: 1.625rem;
        }
        
        :deep(.van-tab--active) {
            color: #4472C4;
            font-size: 1.375rem;
            font-weight: 500;
        }
    }
    
    .content {
        flex: 1;
        overflow: hidden;        
        // 为动态组件提供完整的高度
        > * {
            height: 100%;
        }
    }
}
</style>