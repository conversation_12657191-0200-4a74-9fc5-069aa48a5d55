import * as THREE from 'three';
import { CustomMesh } from './CustomMesh';

export class MeshBuilder {
  static createBox(size: number = 1, color: number = 0x00ff00): CustomMesh {
    const geometry = new THREE.BoxGeometry(size, size, size);
    const material = new THREE.MeshPhongMaterial({ color });
    return new CustomMesh(geometry, material);
  }
  
  static createSphere(
    radius: number = 1,
    color: number = 0xff0000
  ): CustomMesh {
    const geometry = new THREE.SphereGeometry(radius, 32, 32);
    const material = new THREE.MeshStandardMaterial({ color });
    return new CustomMesh(geometry, material);
  }

  static createFromGeometry(
    geometry: THREE.BufferGeometry,
    materialParams: THREE.MaterialParameters
  ): CustomMesh {
    const material = new THREE.MeshStandardMaterial(materialParams);
    return new CustomMesh(geometry, material);
  }
}
