<template>
  <el-row>
    <el-col :span="12" style="padding: 10px">
      <el-row>
        <el-col :span="24">
          <el-form ref="roleRef" :model="form" label-width="100px">
            <el-form-item label="Base Curve">
              <el-tree-select
                v-model="baseCurve"
                :data="projectStore.projectTreeData"
                :render-after-expand="false"
                :props="defaultProps"
                @change="clickData('base')"
              >
                <template #label="{ label, value }">
                  <span style="font-weight: bold">{{ label }}: </span>
                  {{ findPathById(projectStore.projectTreeData, value) }}
                </template>
              </el-tree-select>
            </el-form-item>
            <el-form-item label="Offset Curve">
              <el-tree-select
                v-model="offsetCurve"
                :data="projectStore.projectTreeData"
                :render-after-expand="false"
                :props="defaultProps"
                @change="clickData('offset')"
              >
                <template #label="{ label, value }">
                  <span style="font-weight: bold">{{ label }}: </span>
                  {{ findPathById(projectStore.projectTreeData, value) }}
                </template>
              </el-tree-select>
            </el-form-item>
            <el-form-item> </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20" style="padding-right: 10px">
          <el-table
            ref="tableRef"
            :data="tableData"
            style="width: 100%"
            max-height="600"
            highlight-current-row
            @cell-dblclick="cellDblclick"
            @row-click="rowClick"
          >
            <el-table-column type="index" width="50" />
            <el-table-column property="base" label="Base">
              <template #default="scope">
                <el-input
                  v-if="scope.row.isEdit === 'base'"
                  @blur="handleBlur(scope.row)"
                  v-model="scope.row.base"
                ></el-input>
                <span v-else>{{ parseFloat(scope.row.base).toFixed(3) }}</span>
              </template>
            </el-table-column>
            <el-table-column property="offset" label="Offset">
              <template #default="scope">
                <el-input
                  v-if="scope.row.isEdit === 'offset'"
                  @blur="handleBlur(scope.row)"
                  v-model="scope.row.offset"
                ></el-input>
                <span v-else>{{
                  parseFloat(scope.row.offset).toFixed(3)
                }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="4">
          <p>PreDepthShift</p>
          <p>
            <el-input v-model="preDepthShift" @change="preDepthShiftHandle" />
          </p>
          <!-- <p>
            <el-upload
              ref="upload"
              :before-upload="handleBeforeUpload"
              :show-file-list="false"
            >
              <template #trigger>
                <el-button style="width: 100%">Load...</el-button>
              </template>
            </el-upload>
          </p>
          <p>
            <el-button style="width: 100%" @click="saveHandle"
              >Save...</el-button
            >
          </p> -->
          <!-- <br /> -->
          <!-- <p>
            <el-button style="width: 100%" @click="parameterHandle"
              >Parameter</el-button
            >
          </p> -->
          <!-- <p>
            <el-button style="width: 100%" @click="autoAddHandle"
              >Auto Add...</el-button
            >
          </p> -->
          <br />
          <p>
            <el-button style="width: 100%" @click="addHandle">Add...</el-button>
          </p>
          <p>
            <el-button style="width: 100%" @click="deleteHandle"
              >Delete...</el-button
            >
          </p>
          <p>
            <el-button style="width: 100%" @click="deleteAllHandle"
              >Delete All...</el-button
            >
          </p>
        </el-col>
      </el-row>
    </el-col>
    <el-col :span="12" style="padding: 10px">
      <ul
        class="clickRightMenu"
        id="depthPairContextMenu"
        style="display: none; width: 150px"
      >
        <li id="deleteDepthPair" @click="DeleteDepthPair()">
          Delete Depth Pairs
        </li>
      </ul>
      <el-select
        v-model="depthRatio"
        title="Select Depth Scale"
        @change="changeDepthRatio"
        style="width: 100px"
        id="depthRatio"
        required
      >
        <el-option value="10">1:10</el-option>
        <el-option value="20">1:20</el-option>
        <el-option value="50">1:50</el-option>
        <el-option value="100">1:100</el-option>
        <el-option value="200">1:200</el-option>
        <el-option value="300">1:300</el-option>
        <el-option value="400">1:400</el-option>
        <el-option value="500">1:500</el-option>
        <el-option value="600">1:600</el-option>
        <el-option value="700">1:700</el-option>
        <el-option value="800">1:800</el-option>
        <el-option value="900">1:900</el-option>
        <el-option value="1000">1:1000</el-option>
        <el-option value="1500">1:1500</el-option>
        <el-option value="2000">1:2000</el-option>
        <el-option value="3000">1:3000</el-option>
        <el-option value="4000">1:4000</el-option>
        <el-option value="5000">1:5000</el-option>
        <el-option value="10000">1:10000</el-option> </el-select
      >&nbsp;&nbsp;
      <!-- <el-button @click="CreateDepthPair()">Add Depth Pairs</el-button>
      <el-button @click="SwitchDepthPairEditable()">Move Depth Pairs</el-button> -->
      <div
        id="well-log-app"
        tabindex="0"
        style="width: 100%; height: calc(100vh - 260px)"
      ></div>
    </el-col>
  </el-row>

  <!-- 添加或修改调色板对话框 -->
  <el-dialog
    title="Auto Search Parameter"
    v-model="openParameter"
    width="500px"
    append-to-body
  >
    <el-form
      ref="parameterRef"
      :model="parameterForm"
      :rules="parameterRules"
      label-width="125px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="CorrWinLength" prop="CorrWinLength">
            <el-input-number v-model="parameterForm.CorrWinLength" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Corrthreshold" prop="Corrthreshold">
            <el-input-number
              v-model="parameterForm.Corrthreshold"
              :step="0.1"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="SearchLength" prop="SearchLength">
            <el-input-number v-model="parameterForm.SearchLength" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="MaxDepthPairs" prop="MaxDepthPairs">
            <el-input-number v-model="parameterForm.MaxDepthPairs" :step="1" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="defaultHandle">Default</el-button>
        <el-button type="primary" @click="openParameter = false">OK</el-button>
        <el-button @click="openParameter = false">Cancel</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="DepthPairs">
import {
  onMounted,
  ref,
  reactive,
  nextTick,
  getCurrentInstance,
  watch,
} from "vue";
import { ElMessageBox } from "element-plus";
// import {
//   getChannelDataByChannelDataInfo,
//   getChannelInfoByChannelId,
//   getChannelTrackInfoByChannelId,
// } from "@/api/epaps/canvas/wellLogCanvas.js";
import { CurvePreview } from "@/api/curve";
import { useProjectStore } from "@/stores/project";
import { useDataStore } from "@/stores/data.js";
import { useEditCurveStore } from "@/stores/editCurveStore";

// import { autoadd } from "@/api/epaps/tcd/data";
const projectStore = useProjectStore();
const dataStore = useDataStore();
const store = useEditCurveStore();
const { proxy } = getCurrentInstance();

const form = ref({});
const baseCurve = ref(null);
const offsetCurve = ref(null);
const preDepthShift = ref("");
const delDepthPairId = ref("");
const depthRatio = ref("10");

const openParameter = ref(false);
const timeScale = ref(false);
const parameterForm = ref({
  CorrWinLength: 16,
  Corrthreshold: 0.8,
  SearchLength: 4,
  MaxDepthPairs: 100,
});
const rowClickId = ref("");

const defaultProps = {
  children: "children",
  label: "name",
  value: "id",
};

const tableData = ref([]);
watch(
  tableData,
  (value) => {
    dataStore.setTableData(value);
  },
  { deep: true }
);
const changeDepthRatio = () => {
  const measure = logPlotCanvas.getPlotParams();
  logPlotCanvas.updatePlotParams(
    measure.startIndex_,
    measure.endIndex_,
    depthRatio.value
  );
};

//曲线上移下移
function preDepthShiftHandle() {
  dataStore.setPreDepthShift(parseInt(preDepthShift.value));
  logPlotCanvas.updatePlotPrsObj(
    logPlotCanvas.getPlotTrackObjs("offset")[0].objId_,
    { offset: preDepthShift.value ? parseInt(preDepthShift.value) : 0 }
  );
}

//自动添加深度校正数据
function autoAddHandle() {
  let form = {
    baseId: baseCurve.value,
    offsetId: offsetCurve.value,
    preDepthShift: preDepthShift.value,
  };
  autoadd(form).then((response) => {
    if (response.data) {
      for (let i = 0; i < tableData.value.length; i++) {
        const element = tableData.value[i];
        logPlotCanvas.deleteDepthPair(element.id);
      }
      tableData.value = [];

      response.data.forEach((item) => {
        const id = GUId();
        tableData.value.push({
          id: id,
          base: item.DepthSrc,
          offset: item.DepthDest,
          isEdit: "",
        });

        logPlotCanvas.createDepthPairLineWithDepth(
          id,
          logPlotCanvas.getPlotTrackObjs("base")[0].objId_,
          item.DepthSrc,
          logPlotCanvas.getPlotTrackObjs("offset")[0].objId_,
          item.DepthDest
        );
      });
    }
  });
}

//添加深度校正数据
function addHandle() {
  tableData.value.push({
    id: GUId(),
    base: -9999,
    offset: -9999,
    isEdit: "",
  });
}

//删除深度校正数据
function deleteHandle() {
  tableData.value = tableData.value.filter(
    (item) => item.id != rowClickId.value
  );

  logPlotCanvas.deleteDepthPair(rowClickId.value);
}

//删除所有深度校正数据
function deleteAllHandle() {
  ElMessageBox.confirm(
    "Are you sure to delete all these " +
      tableData.value.length +
      " depth pairs"
  ).then(() => {
    for (let i = 0; i < tableData.value.length; i++) {
      const element = tableData.value[i];
      logPlotCanvas.deleteDepthPair(element.id);
    }
    tableData.value = [];
  });
}

//双击单元格时
function cellDblclick(row, column, cell, event) {
  tableData.value.forEach((item) => {
    if (item.id === row.id) {
      if (column.property === "base") {
        item.isEdit = "base";
      } else {
        item.isEdit = "offset";
      }
    }
  });
}

//单击表格某一行时
function rowClick(row, column, event) {
  rowClickId.value = row.id;
}

//鼠标离开单元格时
function handleBlur(data) {
  tableData.value.forEach((item) => {
    if (item.id === data.id) {
      item.isEdit = "";
      if (item.base != -9999 && item.offset != -9999) {
        logPlotCanvas.deleteDepthPair(item.id);
        logPlotCanvas.createDepthPairLineWithDepth(
          item.id,
          logPlotCanvas.getPlotTrackObjs("base")[0].objId_,
          item.base,
          logPlotCanvas.getPlotTrackObjs("offset")[0].objId_,
          item.offset
        );
      }
    }
  });
}

//加载深度校正txt文件
function handleBeforeUpload(file) {
  if (file && file.type === "text/plain") {
    const reader = new FileReader();
    reader.onload = (e) => {
      const resultData = e.target.result.split("\n");
      if (resultData.length > 0) {
        resultData.forEach((item) => {
          const data = item.split(",");
          if (data.length > 1) {
            tableData.value.push({
              id: guid24(),
              base: data[0],
              offset: data[1],
              isEdit: "",
            });
          }
        });
      }
    };
    reader.onerror = (error) => {
      console.error("读取文件时出错:", error);
    };
    reader.readAsText(file);
  } else {
    alert("请选择一个有效的 .txt 文件");
  }
  return false;
}

//保存深度校正文件
function saveHandle() {
  // 将数组转换为字符串，每个元素占一行
  let content = "";
  for (let i = 0; i < tableData.value.length; i++) {
    const element = tableData.value[i];
    content += element.base + "," + element.offset + "\n";
  }

  // 创建一个 Blob 对象，包含要导出的文本内容
  const blob = new Blob([content], { type: "text/plain" });

  // 创建一个指向 Blob 对象的 URL
  const url = URL.createObjectURL(blob);

  // 创建一个隐藏的 <a> 元素，并设置其 href 属性为 Blob URL
  const a = document.createElement("a");
  a.href = url;

  // 设置下载的文件名
  a.download = "adm.txt";

  // 触发 <a> 元素的点击事件，以下载文件
  a.click();

  // 释放 Blob URL
  URL.revokeObjectURL(url);
}

//深度范围弹窗
function parameterHandle() {
  openParameter.value = true;
}

//恢复Parameter默认设置
function defaultHandle() {
  parameterForm.value = {
    CorrWinLength: 16,
    Corrthreshold: 0.8,
    SearchLength: 4,
    MaxDepthPairs: 100,
  };
}

// 测井图组件实例
let logPlotCanvas = reactive({});

// 测井图数据对象
let logPlot = reactive({
  userId: "",
  id: "",
  plotName: "",
  plotMemo: "",
  wellboreName: "",
  markerLayerId: "",
  startIndex: 0,
  endIndex: 0,
  currentTopIndex: 0,
  depthRatio: 0,
  logPlotTracks: [],
  dataEntries: [],
  logPlotMarkerlayers: [],
  logPlotBookmarkers: [],
});

// 测井轨迹对象
let logPlotTrack = reactive({
  templateId: null,
  plotTrackName: "Depth",
  plotTrackIndex: 0,
  trackType: "depth",
  trackLinearity: 2,
  trackWidth: 60,
  showHorizontalGrid: true,
  showVerticalGrid: true,
  horizontalMajorGridSpacing: 50,
  horizontalMinorGridSpacing: 10,
  verticalMajorGridSpacing: 2,
  verticalMinorGridSpacing: 5,
  logPlotPrsObjs: [],
  tag: null,
  orderCol: "",
  entityType: 1,
  name: "LogPlotTrack",
  nameSpace: "LogHub.FrameWork.DataModel.Log",
  pk: "Id",
  fields:
    "_id,TemplateId,PlotTrackName,PlotTrackIndex,TrackType,TrackLinearity,TrackWidth,ShowHorizontalGrid,ShowVerticalGrid,HorizontalMajorGridSpacing,HorizontalMinorGridSpacing,VerticalMajorGridSpacing,VerticalMinorGridSpacing",
  state: 0,
  dataDic: {},
  id: "67403a2dc75fcbfdb61eb2c0",
  owerId: null,
  departId: null,
  companyId: null,
  shareState: 0,
});
// 测井图数据对象
let logPlotPrsObj = reactive({
  trackID: "7992cf2c4cd12d67064e9866",
  plotPrsName: "K",
  prsIndex: 1,
  prsColor: "#1364FF",
  prsType: 1,
  prsLineType: 1,
  lValue: 0.07,
  rValue: 10.4,
  prsLineThickness: 2,
  palettes: "",
  bindLogName: "640f1bf2cbde4b8cb2155c6b7b2508d2.las",
  bindChannelSetIndex: "0",
  bindChannelName: "K",
  segmentDepth: 100,
  unit: "%",
  paletteColors: null,
  tag: null,
  orderCol: "",
  entityType: 1,
  name: "LogPlotCurve",
  nameSpace: "LogHub.FrameWork.DataModel.Log",
  pk: "Id",
  fields:
    "_id,TrackId,PlotPrsName,PrsIndex,PrsColor,PrsType,PrsLineType,LValue,RValue,PrsLineThickness,Palettes,BindLogName,BindChannelSetIndex,BindChannelName,SegmentDepth",
  state: 0,
  dataDic: {},
  id: "be0dc62ad53ec129bb417991",
  owerId: null,
  departId: null,
  companyId: null,
  shareState: 0,
});

// 测井数据信息
let dataInformation = reactive({
  logPrsObjId: "f6feacf133ade2110589abb8",
  prsType: "curve",
  logId: "61dcd3a3990b27df23a74ea5",
  logName: "640f1bf2cbde4b8cb2155c6b7b2508d2.las",
  channelSetIndex: "0",
  channelName: "GRSL",
  index: null,
  lastDataIndex: 23538,
  val: null,
  channelUnit: "gAPI",
  startIndex: 1678.************,
  endIndex: 3472.************,
  palettes: "",
  minValueValid: 0,
  maxValueValid: 0,
});

// 拖拽数据基本信息
let dropObjectInfo = reactive({});

// 生成一个4位的唯一标识符片段
function S4() {
  return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
}

// 生成一个24位的唯一标识符
function guid24() {
  return S4() + S4() + S4() + S4() + S4() + S4();
}

// 配置表象对象ID
const checkId = guid24();

// 初始化测井图
function initLogPlotCanvas() {
  const appContainer = document.getElementById("well-log-app");
  if (!appContainer) {
    console.error("未找到容器元素");
    return;
  }
  appContainer.innerHTML = ""; // 清空容器内容
  logPlot = generateEmptyLogPlot();
  renderLogPlotCanvas(appContainer, logPlot);
}

// 生成空白测井图数据
function generateEmptyLogPlot() {
  return {
    userId: "",
    id: "",
    plotName: "",
    plotMemo: "",
    wellboreName: "",
    markerLayerId: "",
    startIndex: 2000,
    endIndex: 2400,
    currentTopIndex: 0,
    depthRatio: 100,
    logPlotTracks: [
      {
        templateId: null,
        plotTrackName: "Depth",
        plotTrackIndex: 0,
        trackType: "depth",
        trackLinearity: 2,
        trackWidth: 60,
        showHorizontalGrid: true,
        showVerticalGrid: true,
        horizontalMajorGridSpacing: 50,
        horizontalMinorGridSpacing: 10,
        verticalMajorGridSpacing: 2,
        verticalMinorGridSpacing: 5,
        logPlotPrsObjs: [],
        tag: null,
        orderCol: "",
        entityType: 1,
        name: "LogPlotTrack",
        nameSpace: "LogHub.FrameWork.DataModel.Log",
        pk: "Id",
        fields:
          "_id,TemplateId,PlotTrackName,PlotTrackIndex,TrackType,TrackLinearity,TrackWidth,ShowHorizontalGrid,ShowVerticalGrid,HorizontalMajorGridSpacing,HorizontalMinorGridSpacing,VerticalMajorGridSpacing,VerticalMinorGridSpacing",
        state: 0,
        dataDic: {},
        id: "67622f122843af4da4283872",
        owerId: null,
        departId: null,
        companyId: null,
        shareState: 0,
      },
      {
        templateId: null,
        plotTrackName: "Base",
        plotTrackIndex: 1,
        trackType: "normal",
        trackLinearity: 1,
        trackWidth: 250,
        showHorizontalGrid: true,
        showVerticalGrid: true,
        horizontalMajorGridSpacing: 0,
        horizontalMinorGridSpacing: 0,
        verticalMajorGridSpacing: 4,
        verticalMinorGridSpacing: 5,
        logPlotPrsObjs: [],
        tag: null,
        orderCol: "",
        entityType: 1,
        name: "LogPlotTrack",
        nameSpace: "LogHub.FrameWork.DataModel.Log",
        pk: "Id",
        fields:
          "_id,TemplateId,PlotTrackName,PlotTrackIndex,TrackType,TrackLinearity,TrackWidth,ShowHorizontalGrid,ShowVerticalGrid,HorizontalMajorGridSpacing,HorizontalMinorGridSpacing,VerticalMajorGridSpacing,VerticalMinorGridSpacing",
        state: 0,
        dataDic: {},
        id: "base",
        owerId: null,
        departId: null,
        companyId: null,
        shareState: 0,
      },
      {
        templateId: null,
        plotTrackName: "Offset",
        plotTrackIndex: 2,
        trackType: "normal",
        trackLinearity: 1,
        trackWidth: 250,
        showHorizontalGrid: true,
        showVerticalGrid: true,
        horizontalMajorGridSpacing: 0,
        horizontalMinorGridSpacing: 0,
        verticalMajorGridSpacing: 4,
        verticalMinorGridSpacing: 5,
        logPlotPrsObjs: [],
        tag: null,
        orderCol: "",
        entityType: 1,
        name: "LogPlotTrack",
        nameSpace: "LogHub.FrameWork.DataModel.Log",
        pk: "Id",
        fields:
          "_id,TemplateId,PlotTrackName,PlotTrackIndex,TrackType,TrackLinearity,TrackWidth,ShowHorizontalGrid,ShowVerticalGrid,HorizontalMajorGridSpacing,HorizontalMinorGridSpacing,VerticalMajorGridSpacing,VerticalMinorGridSpacing",
        state: 0,
        dataDic: {},
        id: "offset",
        owerId: null,
        departId: null,
        companyId: null,
        shareState: 0,
      },
    ],
    dataEntries: [],
    logPlotMarkerlayers: [],
    logPlotBookmarkers: [],
  };
}

// 渲染测井图
function renderLogPlotCanvas(appContainer, logPlot) {
  const topsets = processTopsets(logPlot.logPlotMarkerlayers || []);
  const bookMarkers = processBookmarks(logPlot.logPlotBookmarkers || []);
  const logPlotTrackers = processTracks(logPlot.logPlotTracks || []);
  // 实例化测井图Canvas组件
  logPlotCanvas = new WebCanvas.TWCLogPlot({
    appContainer,
    plot: {
      measure: {
        unit: "meter", // meter|inchw
        ratio: logPlot.depthRatio,
        startIndex: logPlot.startIndex,
        endIndex: logPlot.endIndex,
      },
      tracks: logPlotTrackers,
      bookmarkers: bookMarkers,
      topsets: topsets,
    },
  });
  // 更新数据并设置初始深度
  logPlotCanvas.updatePlotParams(
    logPlot.startIndex,
    logPlot.endIndex,
    logPlot.depthRatio
  );
  logPlotCanvas.goToDepthIndex(logPlot.startIndex);
  logPlotCanvas.setDepthPairLineContextMenuFunc(function (obj) {
    delDepthPairId.value = obj.depthPairId;
    document.getElementById("depthPairContextMenu").style.display = "block";
    document.getElementById("depthPairContextMenu").style.left =
      obj.wCanvas_.lastMouseMovePos.x + 563 + "px";
    document.getElementById("depthPairContextMenu").style.top =
      obj.wCanvas_.lastMouseMovePos.y + 42 + "px";
  });
  // 鼠标位于测井图内
  logPlotCanvas.setMouseOverPlotChartFunc(function (Depth, ValTxt, x, y) {
    var type = timeScale.value ? "Time" : "Depth";
    var vTip = "<b>" + type + "：</b>" + Depth + ValTxt;
    ToolTip.ShowTip(this, vTip, x, y);
    //  console.log("move1620")
  });

  // 鼠标离开测井图
  logPlotCanvas.setMouseLeavePlotChartFunc(function () {
    if (document.getElementById("tipPanel") == null) return;
    document.getElementById("tipPanel").style.display = "none";

    // console.log("leave")
  });

  // 深度对编辑后返回Id, 曲线深度
  logPlotCanvas.setDepthPairEditedFunction(function (Id, repDepth, stdDepth) {
    let isedit = true;
    if (tableData.value && tableData.value.length > 0) {
      for (let i = 0; i < tableData.value.length; i++) {
        if (Id === tableData.value[i].id) {
          tableData.value[i].base = repDepth;
          tableData.value[i].offset = stdDepth;
          isedit = false;
        }
      }
    }
    if (isedit) {
      tableData.value.push({
        id: Id,
        base: repDepth,
        offset: stdDepth,
        isEdit: "",
      });
    }
  });

  // 获取当前深度对是否编辑状态
  logPlotCanvas.getDepthPairEditable();
}

//---------------------------tooltip效果 start-----------------------------------
//获取某个html元素的定位
function GetPos(obj) {
  // alert(  document.getElementById('well-log-app1-').offsetTop);

  var pos = new Object();
  pos.x = obj.offsetLeft;
  pos.y = obj.offsetTop;
  // while (obj = obj.offsetParent) {
  //     pos.x += obj.offsetLeft;
  //     pos.y += obj.offsetTop;
  // }
  return pos;
}

//提示工具
var ToolTip = {
  _tipPanel: null,
  Init: function () {
    if (null == this._tipPanel) {
      var tempDiv = document.createElement("div");
      document.body.insertBefore(tempDiv, document.body.childNodes[0]);
      tempDiv.id = "tipPanel";
      tempDiv.style.display = "none";
      tempDiv.style.position = "absolute";
      tempDiv.style.zIndex = "999";

      //     document.getElementById("tipPanel").style.left =  "0px";
      // document.getElementById("tipPanel").style.top =  "0px";
    }
  },
  AttachTip: function () {}, //添加提示绑定
  DetachTip: function () {}, //移除提示绑定
  ShowTip: function (oTarget) {
    if (document.getElementById("tipPanel") == null) return;

    /*操作流程
     *1、构建新的html片段
     *2、设置提示框新位置
     *3、显示提示框
     */
    //1.
    var tempStr = "<p>" + arguments[1] + "</p>"; //html片段
    // if (arguments.length > 1) {
    //    for (var i = 1; i < arguments.length; i++) {
    //        tempStr += "<p>" + arguments[i] + "</p>";
    //    }
    // }
    document.getElementById("tipPanel").innerHTML = tempStr;
    //2.
    var pos = GetPos(document.getElementById("well-log-app1-"));
    // document.getElementById("tipPanel").style.left = (100) + "px";
    // document.getElementById("tipPanel").style.top = (162) + "px";

    document.getElementById("tipPanel").style.left =
      1.0 * parseInt(arguments[2]) + pos.x + 10 + "px";
    document.getElementById("tipPanel").style.top =
      1.0 * parseInt(arguments[3]) + pos.y + 10 + "px";

    //3.
    document.getElementById("tipPanel").style.display = "";
  },
  HideTip: function () {
    if (document.getElementById("tipPanel") == null) return;
    document.getElementById("tipPanel").style.display = "none";
  },
};

//---------------------------tooltip效果 end-----------------------------------

// 处理标记层数据
function processTopsets(markerLayers) {
  return markerLayers.map((layer) => ({
    Id: layer.id,
    LayerName: layer.layerName,
    MD: layer.md,
  }));
}

// 处理书签数据
function processBookmarks(bookMarkers) {
  return bookMarkers.map((bookmark) => ({
    Id: bookmark.id,
    Description: bookmark.description,
    MD: bookmark.md,
  }));
}

// 处理轨迹数据
function processTracks(logPlotTracks) {
  return logPlotTracks.map((track) => {
    const logPrsVos = (track.logPlotPrsObjs || []).map((prsObj) => ({
      chName: prsObj.plotPrsName,
      Id: prsObj.id,
      type: prsObj.prsType === 1 ? "curve" : "image",
      color: prsObj.prsColor,
      lineStyle: prsObj.prsLineType,
      lineThickness: prsObj.prsLineThickness,
      segmentDataDepth: prsObj.segmentDepth,
      paletteColors: prsObj.paletteColors,
      label: prsObj.plotPrsName,
      unit: prsObj.unit,
      index: prsObj.prsIndex,
      lower: prsObj.lValue,
      upper: prsObj.rValue,
      onEdit: function (prsObjId) {
        editPrsObj(prsObjId);
      },
    }));

    return {
      type: track.trackType,
      label: track.plotTrackName,
      Id: track.id,
      valueScaleType: track.trackLinearity === 1 ? "linear" : "log",
      index: track.plotTrackIndex,
      width: track.trackWidth,
      showVerticalGrid: track.showVerticalGrid,
      showHorizontalGrid: track.showHorizontalGrid,
      horizontalMajorGridSpacing: track.horizontalMajorGridSpacing,
      horizontalMinorGridSpacing: track.horizontalMinorGridSpacing,
      verticalMajorGridSpacing: track.verticalMajorGridSpacing,
      verticalMinorGridSpacing: track.verticalMinorGridSpacing,
      logPrsVos,
      onEdit: function (trackId) {
        editTrack(trackId);
      },
    };
  });
}

// 选择base、offset曲线时
function clickData(trackName) {
  if (tableData.value) {
  }
  for (let i = 0; i < tableData.value.length; i++) {
    const element = tableData.value[i];
    logPlotCanvas.deleteDepthPair(element.id);
  }
  tableData.value = [];
  depthRatio.value = "10";

  let channelID = "";
  if ("base" === trackName) {
    channelID = baseCurve.value;
  } else {
    channelID = offsetCurve.value;
    dataStore.setOffsetChannelId(channelID);
  }

  //   getChannelInfoByChannelId(channelID).then((res) => {
  //     dropObjectInfo = res.data;
  //     setData(trackName);
  //   });

  dropObjectInfo = {
    channelID: channelID,
    channelName: findChannelNameById(projectStore.projectTreeData, channelID)
      .name,
    color: "#114672",
    maxValueValid: 500,
    minValueValid: 0,
  };
  setData(trackName);
}
function findChannelNameById(tree, targetId) {
  function findNode(nodes, parent = null) {
    for (const node of nodes) {
      if (node.id === targetId) {
        return {
          name: node.name,
          parentNode: parent,
        };
      }
      if (node.children) {
        const result = findNode(node.children, node);
        if (result) return result;
      }
    }
    return null;
  }
  return findNode(tree);
}
//把曲线数据放到canvas中
async function setData(trackName) {
  const track = logPlotCanvas.getPlotTrack(trackName);
  const objcet = track.objs_;
  if (objcet && objcet.length > 0) {
    logPlotCanvas.deletePlotPrsObj(objcet[0].objId_);
  }

  // 获取测井图中所有道信息
  const plotTracks = track.parent_.objs_;
  // 测井图所有表象对象列表
  const plotObjs = [];
  // 获取所有道的表象对象
  for (const track of plotTracks) {
    if (track.ObjType === "Track") {
      for (const obj of track.objs_) {
        plotObjs.push(obj);
      }
    }
  }
  // 配置表象对象ID
  const checkId = guid24();
  let checkOut = false;
  // 检查测井图中是否已存在该对象
  for (const item of plotObjs) {
    if (item.objId_ === checkId) {
      checkOut = true;
      break;
    }
  }
  if (checkOut) {
    proxy.$notify({
      title: "提示信息",
      message: "测井图中已存在该对象",
      type: "warning",
      duration: 2000,
    });
    return false;
  }
  // 调用接口获取曲线数据
  const channelId = dropObjectInfo.channelID;
  let datasetId = findChannelNameById(projectStore.projectTreeData, channelId)
    .parentNode.id;
  let cueveData = await CurvePreview({
    wellId: store.wellId,
    datasetId: datasetId,
    id: channelId,
  });
  let data = cueveData.data;

  if (trackName === "base") {
    dataStore.setBaseUnit(data.unit);
  }
  // 准备添加对象的属性
  const loggingDataObject = {
    chName: dropObjectInfo.channelName,
    Id: checkId,
    index: 1,
    type: "curve",
    color: dropObjectInfo.color,
    lineStyle: 1,
    lineThickness: 2,
    segmentDataDepth: data.index.indexValue[data.index.indexValue.length - 1],
    label: dropObjectInfo.channelName,
    unit: data.unit,
    paletteColors: [],
    lower: dropObjectInfo.minValueValid,
    upper: dropObjectInfo.maxValueValid,
  };
  // 获取当前测井图的参数
  const measure = logPlotCanvas.getPlotParams();
  // 更新最大最小深度以及深度比例
  if (plotObjs.length === 0) {
    logPlotCanvas.updatePlotParams(
      data.index.indexValue[0],
      data.index.indexValue[data.index.indexValue.length - 1],
      10
    );
  } else {
    logPlotCanvas.updatePlotParams(
      Math.min(measure.startIndex_, data.index.indexValue[0]),
      Math.max(
        measure.endIndex_,
        data.index.indexValue[data.index.indexValue.length - 1]
      ),
      10
    );
  }
  // 添加表象对象
  logPlotCanvas.addPlotPrsObj(track.trackId_, loggingDataObject);
  let logPrsDataContainer = new WebCanvas.LogPrsDataContainer({
    logPrsObjId: loggingDataObject.Id,
    chName: dropObjectInfo.channelName,
    // logId: channelTrackInfo.logId,
    // logName: channelTrackInfo.logName,
    channelSetIndex: dropObjectInfo.setIndexID,
    startIndex: data.index.indexValue[0],
    endIndex: data.index.indexValue[data.index.indexValue.length - 1],
    palettes: loggingDataObject.paletteColors,
    type: loggingDataObject.type,
    getDataFunc: function (
      logPrsObjId,
      logId,
      type,
      channelSetIndex,
      chName,
      it,
      ib
    ) {
      const loggingData = [];
      if (data.data != null && data.data.length > 0) {
        if (type === "curve") {
          for (let i = 0; i < data.data.length; i++) {
            const d = { tvd: data.index.indexValue[i], y: data.data[i] };
            loggingData.push(d);
          }
          logPlotCanvas.setNewData(logPrsObjId, it, ib, loggingData);
        }
      }
      logPlotCanvas.setDataLoadingStatus(logPrsObjId, false);
    },
  });
  // 添加表象对象数据
  logPlotCanvas.addDataContainer([logPrsDataContainer]);
}

function GUId() {
  return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
}

function CreateDepthPair() {
  logPlotCanvas.createNewDepthPairLine(GUId());
}

function SwitchDepthPairEditable() {
  logPlotCanvas.switchDepthPairEditable();
}

function DeleteDepthPair() {
  logPlotCanvas.deleteSelectedDepthPair();

  for (let i = 0; i < tableData.value.length; i++) {
    const element = tableData.value[i];
    if (element.id === delDepthPairId.value) {
      tableData.value.splice(i, 1);
    }
  }
  contextMenuMouseUp();
}

function contextMenuMouseUp() {
  logPlotCanvas.clearContextMenu();
}

// 定义查找路径的方法
function findPathById(tree, targetId) {
  // 递归查找路径
  function findPath(nodeId, nodes) {
    for (const node of nodes) {
      if (node.id === nodeId) {
        return [node]; // 找到目标节点，返回节点本身的路径
      }
      // 如果当前节点有子节点，递归查找子节点
      if (node.children && node.children.length > 0) {
        const childPath = findPath(nodeId, node.children);
        if (childPath.length > 0) {
          return [node, ...childPath]; // 返回父节点与子节点路径
        }
      }
    }
    return []; // 未找到时返回空数组
  }

  // 调用递归函数获取路径
  const path = findPath(targetId, tree);
  // 如果找到路径，拼接并返回节点名称路径
  if (path.length > 0) {
    return path.map((item) => item.name).join(" > ");
  } else {
    return ""; // 如果没有找到该节点
  }
}

//首次打开重置所有元素
function initDepthPairs() {
  console.log();
  baseCurve.value = null;
  offsetCurve.value = null;
  depthRatio.value = "10";

  for (let i = 0; i < tableData.value.length; i++) {
    const element = tableData.value[i];
    logPlotCanvas.deleteDepthPair(element.id);
  }
  tableData.value = [];
  if (Reflect.ownKeys(logPlotCanvas).length != 0) {
    const measure = logPlotCanvas.getPlotParams();
    if (measure) {
      logPlotCanvas.updatePlotParams(
        measure.startIndex_,
        measure.endIndex_,
        depthRatio.value
      );
    }
  }

  if (
    Reflect.ownKeys(logPlotCanvas).length != 0 &&
    logPlotCanvas.getPlotTrackObjs("base")[0]
  ) {
    logPlotCanvas.deletePlotPrsObj(
      logPlotCanvas.getPlotTrackObjs("base")[0].objId_
    );
  }
  if (
    Reflect.ownKeys(logPlotCanvas).length != 0 &&
    logPlotCanvas.getPlotTrackObjs("offset")[0]
  ) {
    logPlotCanvas.deletePlotPrsObj(
      logPlotCanvas.getPlotTrackObjs("offset")[0].objId_
    );
  }
}
defineExpose({
  initDepthPairs,
});

onMounted(() => {
  store.registerDepthPairs({
    CreateDepthPair,
    SwitchDepthPairEditable,
  });
  nextTick(() => {
    initLogPlotCanvas();
  });
});
</script>

<style scoped>
:deep(.el-upload) {
  width: 100%;
}

.clickRightMenu {
  width: 110px;
  margin-top: 0px;
  background-color: #fff;
  font-size: 12px;
  position: absolute;
  text-align: left;
  padding: 2px 0;
  border: 1px solid #ccc;
  display: none;
  z-index: 100;
}

.clickRightMenu li {
  list-style: none;
  line-height: 20px;
  padding-left: 25px;
}

.clickRightMenu li:hover {
  background-color: #dcdcdc;
  cursor: pointer;
}
</style>
