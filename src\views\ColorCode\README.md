# ColorCode 组件

这是一个使用 jQuery 和 JavaScript 实现的色标组件，支持可视化编辑颜色渐变。

## 特性

- ✅ **端点保护**：最左和最右侧节点不可移动，确保渐变完整性
- 🎨 **可视化编辑**：支持双击添加节点、拖拽移动、右键删除
- 🌈 **实时渐变**：基于Canvas的高性能渐变渲染
- 📊 **数值映射**：支持任意数值范围到颜色的映射
- 💾 **配置导入导出**：支持JSON格式的配置保存和加载
- 📱 **响应式设计**：自适应容器宽度

## 使用方法

### 基本用法

```html
<!DOCTYPE html>
<html>
<head>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div id="colorcode-container"></div>
    
    <script src="colorcode-component.js"></script>
    <script>
        $(document).ready(function() {
            const colorCode = new ColorCode('#colorcode-container', {
                width: 500,
                height: 40,
                minValue: 0,
                maxValue: 100
            });
        });
    </script>
</body>
</html>
```

### 高级用法

```javascript
// 创建实例
const colorCode = new ColorCode('#container', {
    width: 600,
    height: 50,
    minValue: -10,
    maxValue: 50,
    initialNodes: [
        { position: 0, color: '#0000FF', isEndpoint: true },    // 蓝色左端点
        { position: 0.5, color: '#00FF00', isEndpoint: false }, // 绿色中间点
        { position: 1, color: '#FF0000', isEndpoint: true }     // 红色右端点
    ]
});

// 获取指定数值的颜色
const color = colorCode.getColorForValue(25); // 返回 "#FF8000" 等

// 编程方式添加节点
colorCode.addNode(0.3, '#FFFF00'); // 在30%位置添加黄色节点

// 导出配置
const config = colorCode.exportConfig();
console.log(JSON.stringify(config, null, 2));

// 导入配置
colorCode.importConfig(savedConfig);
```

## 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `width` | Number | 400 | 组件宽度（像素） |
| `height` | Number | 40 | 色标高度（像素） |
| `minValue` | Number | 0 | 数值范围最小值 |
| `maxValue` | Number | 100 | 数值范围最大值 |
| `initialNodes` | Array | 默认红蓝端点 | 初始节点配置 |

## API 方法

### 节点操作

- `addNode(position, color)` - 添加节点
- `removeNode(nodeIndex)` - 删除节点
- `setNodeColor(nodeIndex, color)` - 设置节点颜色

### 颜色映射

- `getColorForValue(value)` - 获取数值对应的颜色
- `interpolateColor(position)` - 根据位置插值计算颜色

### 配置管理

- `exportConfig()` - 导出配置为JSON
- `importConfig(config)` - 导入JSON配置
- `reset()` - 重置为初始状态
- `destroy()` - 销毁组件

## 交互操作

### 鼠标操作

- **双击色标条**：在指定位置添加新节点
- **拖拽节点**：移动节点位置（端点节点不可移动）
- **双击节点**：打开颜色选择器编辑节点颜色
- **右键节点**：显示删除菜单（端点节点不可删除）

### 键盘操作

- 支持标准的颜色选择器键盘操作

## 数据格式

### 节点数据结构

```javascript
{
    position: 0.5,        // 位置 (0-1)
    color: '#FF0000',     // 颜色值
    isEndpoint: false,    // 是否为端点
    id: 1234567890        // 唯一标识
}
```

### 导出配置格式

```javascript
{
    version: '1.0',
    timestamp: '2024-01-01T00:00:00.000Z',
    minValue: 0,
    maxValue: 100,
    nodes: [
        {
            index: 0,
            position: 0,
            color: '#FF0000',
            value: 0,
            isEndpoint: true
        },
        // ... 更多节点
    ]
}
```

## 注意事项

1. **端点保护**：位置为 0 和 1 的端点节点不可移动或删除
2. **边界限制**：新节点只能在 5%-95% 的位置范围内添加
3. **顺序约束**：节点不能越过相邻节点的位置
4. **依赖要求**：需要 jQuery 3.0+ 和现代浏览器支持

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- IE 11（部分功能）

## 示例文件

- `CustomerColor.html` - 完整功能演示
- `demo.html` - 简化使用示例
- `colorcode-component.js` - 组件源码

## 许可证

MIT License 