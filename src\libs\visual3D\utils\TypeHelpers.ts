import * as THREE from 'three';

export type ConstructorParams<T> = T extends new (...args: infer P) => any ? P : never;

export function isMeshStandardMaterial(
  material: THREE.Material
): material is THREE.MeshStandardMaterial {
  return (material as THREE.MeshStandardMaterial).isMeshStandardMaterial;
}

export function isPhysicalMaterial(
  material: THREE.Material
): material is THREE.MeshPhysicalMaterial {
  return (material as THREE.MeshPhysicalMaterial).isMeshPhysicalMaterial;
}

export type WithGeometry<T extends THREE.Mesh> = T & {
  geometry: THREE.BufferGeometry;
};

export type InteractiveObject = THREE.Object3D & {
  userData: {
    onClick?: () => void;
    onHover?: (isHovering: boolean) => void;
  };
};