<template>
  <div class="list-container">
    <!-- 导航栏 -->
    <van-nav-bar
      title="工程列表"
      fixed
      placeholder
      left-arrow
      @click-left="onClickLeft"
    />

    <!-- 下拉刷新 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      
      <!-- 列表 -->
      <div class="list-wrapper">
        <van-cell-group inset v-if="!loading && list.length > 0">
          <van-cell 
            v-for="item in list" 
            :key="item.id"
            clickable 
            @click="onItemClick(item)"
            class="project-cell"
          >
            <template #title>
              <div class="cell-title">{{ item.projectName }}</div>
            </template>
            <template #label>
              <div class="cell-info">
                <van-icon name="clock-o" size="14" />
                <span>{{ item.createTime }}</span>
              </div>
              <div class="cell-info" v-if="item.wellboreNumber">
                <van-icon name="location-o" size="14" />
                <span>{{ item.wellboreNumber }}</span>
              </div>
            </template>
            <template #right-icon>
              <van-icon name="arrow" color="#969799" />
            </template>
          </van-cell>
        </van-cell-group>

        <!-- 加载中状态 -->
        <div v-if="loading" class="loading-container">
          <van-loading size="24px" vertical>加载中...</van-loading>
        </div>

        <!-- 空状态 -->
        <van-empty v-if="!loading && list.length === 0" description="暂无工程数据" />
      </div>
      
    </van-pull-refresh>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { showToast } from 'vant';
import { useRouter } from 'vue-router';
import axios from 'axios'
const router = useRouter();
const vWebApiUrl = window.location.protocol + "//" + window.location.host;

// --- 状态管理 ---
const list = ref([]);
const loading = ref(false);
const refreshing = ref(false);

// --- 获取工程列表数据 ---
const getProjectList = async () => {
  try {
    // 从URL的hash部分获取appId参数
    let appid = null;
    
    // 方法1: 从Vue Router的query参数获取
    const route = router.currentRoute.value;
    appid = route.query.appId;
    
    // 方法2: 如果Vue Router没有获取到，手动解析URL hash部分
    if (!appid) {
      const hash = window.location.hash;
      if (hash.includes('?')) {
        const queryString = hash.split('?')[1];
        const urlParams = new URLSearchParams(queryString);
        appid = urlParams.get('appId');
      }
    }
    
    if (!appid) {
      showToast('缺少应用ID参数');
      console.log('当前URL:', window.location.href);
      console.log('Hash部分:', window.location.hash);
      return [];
    }
    
    console.log('获取到的appId:', appid);

    const response = await axios.get(`${vWebApiUrl}/api/project/project/GetProjctList?appId=${appid}`);
    const result = response.data;
    
    if (result.success && result.data && result.data.rows) {
      // 处理返回的数据，提取项目信息
      return result.data.rows.map(item => ({
        id: item.projectId,
        projectName: item.project[0]?.projectName || '未命名项目',
        createTime: item.project[0]?.createTime ? 
          new Date(item.project[0].createTime).toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
          }).replace(/\//g, '-') : '未知时间',
        wellboreNumber: item.wellboreNumber || '',
        appId: item.appId
      }));
    } else {
      showToast(result.message || '获取工程列表失败');
      return [];
    }
  } catch (error) {
    console.error('获取工程列表错误:', error);
    showToast('网络请求失败');
    return [];
  }
};

// --- 事件处理 ---

// 点击左侧返回
const onClickLeft = () => {
  router.back();
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const data = await getProjectList();
    list.value = data;
  } catch (error) {
    showToast('加载失败，请重试');
  } finally {
    loading.value = false;
    refreshing.value = false;
  }
};

// 下拉刷新
const onRefresh = () => {
  loadData();
};

// 点击列表项
const onItemClick = (item) => {
  console.log('点击了项目:', item);
  showToast(`打开项目: ${item.projectName}`);
  
  // 跳转到地质导向应用，并传递项目ID
  const targetUrl = `${vWebApiUrl}/static/GeoSteering/GeoSteeringIndex.html?projectId=${item.id}&appId=${item.appId}`;
  window.open(targetUrl, '_blank');
};

// 页面加载时获取数据
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.list-container {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.list-wrapper {
  min-height: calc(100vh - 46px);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50px 0;
}

.van-cell-group {
  margin: 12px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(100, 101, 102, 0.08);
}

.project-cell {
  padding: 16px;
  transition: all 0.3s ease;
}

.project-cell:active {
  background-color: #f2f3f5;
}

.cell-title {
  font-weight: 500;
  font-size: 16px;
  color: #323233;
  line-height: 24px;
}

.cell-info {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
  font-size: 13px;
  color: #969799;
  
  &:first-of-type {
    margin-top: 8px;
  }
  
  &:not(:first-of-type) {
    margin-top: 4px;
  }
}

.cell-info span {
  line-height: 18px;
}

/* 优化导航栏样式 */
:deep(.van-nav-bar) {
  background-color: #1989fa;
}

:deep(.van-nav-bar__title) {
  color: #fff;
  font-weight: 500;
}

:deep(.van-nav-bar__arrow) {
  color: #fff !important;
}

/* 优化空状态样式 */
:deep(.van-empty) {
  padding: 80px 0;
}
</style>
