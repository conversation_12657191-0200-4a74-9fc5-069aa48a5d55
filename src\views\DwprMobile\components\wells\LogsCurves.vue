<template>
  <div class="logs-curves">
    <div class="logs-nav">
      <van-nav-bar title="Curves" left-text="Logs" @click-left="onClickLeft">
        <template #left>
          <van-icon name="arrow-left" color="#4472C4" :size="24" />
          <span class="left-text">Logs</span>
        </template>
      </van-nav-bar>
      <van-search
        v-model="searchValue"
        placeholder="Search"
        @submit="onSearch"
      />
    </div>
    <div class="logs-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh"
      pulling-text="Pull to refresh..."
      loosing-text="Release to refresh..."
      loading-text="Refreshing..."
      success-text="Refresh successful">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="No More"
          loading-text="Loading..."
          @load="onLoad"
        >
          <div class="logs-curves-item" v-for="item in list" :key="item">
            <div class="item-left">
              <van-icon
                name="/src/icon/dwprMobile/logs-curves-icon.svg"
                :size="16"
              />
              <span class="item-left-text">TFGN10 (raw) </span>
              <span class="item-left-num">4178.656</span>
            </div>
            <div class="item-right">
              <van-icon name="arrow" :size="16" @click="iconClick" />
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
enum LogsType {
  Logs = "logs",
  Curves = "curves",
  Charts = "charts",
}
const emits = defineEmits<{
  (e: "goBack", type: LogsType): void;
}>();
const onClickLeft = () => {
  emits("goBack", LogsType.Logs);
};
const iconClick = () => {
  emits("goBack", LogsType.Charts);
};

const searchValue = ref("");
const onSearch = () => {
  console.log(searchValue.value);
};

const list = ref<any>([]);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);

const onLoad = () => {
  console.log("加载更多");

  setTimeout(() => {
    if (refreshing.value) {
      list.value = [];
      refreshing.value = false;
    }

    for (let i = 0; i < 10; i++) {
      list.value.push(list.value.length + 1);
    }
    loading.value = false;

    if (list.value.length >= 40) {
      finished.value = true;
    }
  }, 1000);
};
const onRefresh = () => {
  // 清空列表数据
  finished.value = false;

  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  onLoad();
};
</script>

<style scoped lang="less">
.logs-curves {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
}
.logs-nav {
  flex-shrink: 0;
}
.logs-list {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; // iOS平滑滚动
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}
.left-text {
  font-weight: 500;
  font-size: 1.125rem;
  color: #4472c4;
}
.logs-curves-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  flex-shrink: 0;
  .item-left {
    display: flex;
    align-items: center;
    .item-left-text {
      padding: 0 1.25rem;
      font-weight: 400;
      font-size: 1rem;
      color: #3d3d3d;
    }
    .item-left-num {
      font-weight: 400;
      font-size: 0.875rem;
      color: #737373;
    }
  }
  .item-right {
    display: flex;
    align-items: center;
  }
}
:deep(.van-nav-bar__title) {
  font-weight: 500;
  font-size: 1.25rem;
  color: #3d3d3d;
}
:deep(.van-search__content) {
  background: #ededed;
}
</style>
