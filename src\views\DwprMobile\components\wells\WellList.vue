<template>
<div class="wellItem" v-for="item in wellList" :key="item.id" @click="handleClick(item)">
    <div class="well-left">
        <div class="well-status">
            <van-icon :name="item.status === WellStatus.PROCEED ? 
            '/src/icon/dwprMobile/wells-item-ing.svg' : 
            '/src/icon/dwprMobile/wells-item-finish .svg'" :size="item.status === WellStatus.PROCEED ? '24' : '16'" />
        </div>
        <span class="well-name">{{item.name}}</span>
        <span class="well-remark">({{ item.remark }})</span>
    </div>
    <div class="well-right">
        <span class="well-date">{{item.date}}</span>
        <van-icon name="arrow" color="#3D3D3D" />
    </div>
</div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()

const props = defineProps<{
    wellList: WellItem[];
}>()

interface WellItem {
    id: string;
    name: string;
    date: string;
    status: WellStatus;
    remark: string;
}
enum WellStatus {
    PROCEED = 1,
    SUCCESS = 2,
}

const handleClick = (item: WellItem) => {
    router.push({
        path: '/Dwpr/detail',
        query: {
            id: item.id,
            name: item.name,
        }
    })
}

</script>

<style scoped lang="scss">
.wellItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0  1rem 0 .5625rem;
    background: #fff;
    height: 4.3125rem;
    margin-bottom: .5rem;
    border-bottom: .0625rem solid #E5E5E5;
    border-top: .0625rem solid #E5E5E5;
}
.wellItem:last-child {
    margin-bottom: 0;
}
.wellItem:first-child {
    margin-top: .375rem;
}
.well-status {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.5rem;
    height: 1.5rem;
}
.well-right {
    height: 100%;
    display: flex;
    align-items: center;
    .well-date {
        margin-right: .5625rem;
        font-weight: 400;
        font-size: 1.125rem;
        color: #4472C4;
    }
}
.well-left {
    height: 100%;
    display: flex;
    align-items: center;
    .well-name {
        margin-left: .0625rem;
        font-weight: 500;
        font-size: 1.125rem;
        color: #3D3D3D;
    }
    .well-remark {
        margin-left: .375rem;
        font-weight: 400;
        font-size: 1rem;
        color: #737373;
    }
}


</style>
