<template>
  <!-- 布局容器 -->
  <div class="layout-box">

    <TabMenu :new-tabs="newTabs" />
    <!-- Dock布局容器 -->
    <div class="dock-container">
      <ReactDockLayout ref="dockRef" v-bind="dockProps" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { applyReactInVue, applyVueInReact } from "veaury";
import * as React from "react";
import { DockLayout, DockPanel } from "rc-dock";
import type { TabData, LayoutData, DockMode, PanelData } from "rc-dock";

// 引入自定义标签页内容组件
import ProjectTree from "@/views/3DViews/ProjectTree.vue";
import TabMenu from "@/components/TabMenu.vue";
import Collapse from "@/components/Collapse.vue";
import ThreeDShows from "@/views/3DViews/3dShows.vue"
import Property from "@/components/property.vue"
// 引入rc-dock样式
import "rc-dock/dist/rc-dock.css";


// 状态管理
const dockRef = ref(); // Dock布局引用
let tabCounter = 1; // 标签页计数器
const resourceTreeRef = ref(); // ResourceTree 组件引用
const threedShowRef = ref();
const propertyRef = ref();
const collapseRef = ref();
let id = ref();
interface TabItem {
  label: string;
  value: string;
  children?: Array<{
    label: string;
    value: string;
    icon?: string;
  }>;
}
interface FormOptionItem {
  label: string;
  value: any;
  type: string;
  group?: string; // 添加问号改为可选属性
  dicData?: Array<{ label: string; value: any }>;
  // ... 其他属性
}
const formOption = ref({
  formName: "基本属性",
  formNode:"",
  options: [
    {
      label: "颜色",
      value: "#ff0000",
      type: "color",
      order: 1
    },
    {
      label: "粗细",
      value: "",
      type: "number",
      order: 2
    },
    {
      label: "展示方式",
      value: "normal",
      type: "select",
      dicData: [
        { label: "正常", value: "normal" },
        { label: "2D", value: "2D" },
        { label: "3D", value: "3D" }
      ],
      order: 4
    },
    {
      label: "Min Value",
      value: "",
      group: "config",
      type: "input",
    },
    {
      label: "Max Value",
      value: "",
      group: "config",
      type: "input",
    },
    {
      label: "Min Calibrate",
      value: "",
      group: "config",
      type: "input",
      order: 6
    },
    {
      label: "Max Calibrate",
      value: "",                            
      group: "config",
      type: "input",
      order: 7
    },
    {
      label: "Min Color",
      value: "#ff0000",
      group: "config",
      type: "color",
      order: 8
    },
    {
      label: "Max Color",
      value: "#ff0000",
      group: "config",
      type: "color",
      order: 9
    }
  ]
});

const tabData = [
  {
    label: "File",
    value: "first1",
    types:"3dviews",
    children: [
      {
        label: "Create Project",
        value: "createProject",
        icon: "CirclePlus",
      },
      {
        label: "Add Well",
        value: "importWell",
        icon: "Upload",
      },
      {
        label: "Add Model",
        value: "importModel",
        icon: "Upload",
      },
      {
        label: "Add Wellbore",
        value: "importWellbore",
        icon: "Upload",
      },
      {
        label: "Add Curve",
        value: "importCurve",
        icon: "Upload",
      }
    ],
  },
  // {
  //   label: "Edit",
  //   value: "second",
  //   children: [
  //     {
  //       label: "Rename",
  //       value: "rename",
  //       icon: "Edit",
  //     },
  //   ],
  // },
  // {
  //   label: "View",
  //   value: "third",
  // },
  // {
  //   label: "Canvas",
  //   value: "fourth",
  // },
];
const newTabs = ref<TabItem[]>(tabData);
// 使用Veaury将Vue组件转换为React组件

const ThreeDShowsContent = applyVueInReact(ThreeDShows, {
  forwardRef: true, // 启用 ref 转发
  useReactWrapper: true,
});


const ProjectTreeContent = applyVueInReact(ProjectTree, {
  forwardRef: true, // 启用 ref 转发
  useReactWrapper: true,
});


const newCollapse = applyVueInReact(Collapse, {
  forwardRef: true, // 启用 ref 转发
  useReactWrapper: true,
});

const PropertyContent = applyVueInReact(Property, {
  forwardRef: true, // 启用 ref 转发
  useReactWrapper: true,
});



function update3Dshows(id: string,curveType:string,moduleType:number) {
  if (threedShowRef.value && moduleType === 6) {
    threedShowRef.value.__veauryVueRef__.getDesignCurve(id,curveType);
  } else if (threedShowRef.value && moduleType === 9) {
    threedShowRef.value.__veauryVueRef__.downloadAndProcessFile(id);
  } else {
    console.error("threedShowRef 组件实例未找到");
  }
}

function changeCurrentNode(id: string) {
  if (resourceTreeRef.value) {
    resourceTreeRef.value.__veauryVueRef__.setCheckNode(id);
  } else {
    console.error("ResourceTree 组件实例未找到");
  }
}



// 布局样式配置
const layoutStyle = computed(() => ({
  width: "100%",
  height: "calc(100vh - 40px)",
  background: "#1E1E1E",
  color: "#CCCCCC",
}));

/**
 * 分组配置
 * 定义不同分组的行为特性
 */
const groupConfig = {
  default: {
    floatable: true, // 是否可浮动
    maximizable: true, // 是否可最大化
    tabLocked: false, // 标签是否锁定
    newWindow: true, // 是否允许新窗口
    preferredFloatWidth: [300, 800],
    preferredFloatHeight: [200, 600],
  },
  main: {
    floatable: true,
    maximizable: true,
    tabLocked: false,
    newWindow: true,
    preferredFloatWidth: [400, 1000],
    preferredFloatHeight: [300, 800],
  },
};

// Dock组件属性配置
const dockProps = computed(() => ({
  defaultLayout: defaultLayout, // 默认布局
  style: layoutStyle.value,
  groups: groupConfig, // 分组配置
  dropMode: "default", // 拖放模式
  mode: "horizontal", // 布局模式
  maximizable: true, // 允许最大化
  floatable: true, // 允许浮动
  newWindow: true, // 允许新窗口
  disableDock: false, // 启用停靠
  draggable: true, // 允许拖拽
  resizable: true, // 允许调整大小
  onLayoutChange: (layout: LayoutData) => {
    // 定义一个递归函数来遍历布局数据
    const findActiveIds = (box: any) => {
      if (box.children) {
        box.children.forEach((child: any) => {
          if (child.activeId && child.group === "main") {
            changeCurrentNode(child.activeId);
          }
          if (child.children) {
            findActiveIds(child);
          }
        });
      }
    };

    // 从 dockbox 开始遍历
    if (layout.dockbox) {
      findActiveIds(layout.dockbox);
    }
  },
}));

// 默认内容组件
const DefaultContent = (text: string) => () =>
  React.createElement("div", null, text);

/**
 * 默认布局配置
 * 定义初始布局结构，包括：
 * - 左侧资源管理器
 * - 中间主编辑区
 * - 底部面板（问题、输出、终端）
 * - 右侧大纲视图
 */
const defaultLayout: LayoutData = {
  dockbox: {
    mode: "horizontal" as DockMode,
    children: [
      {
        mode: "vertical" as DockMode,
        size: 250,
        children: [
          {
            id: "right",
            group: "right",
            tabs: [
              {
                id: "explorer",
                title: "Files",
                content: React.createElement(ProjectTreeContent, {
                  ref: (instance: any): void => {
                    // 保存React包装器的引用
                    resourceTreeRef.value = instance;
                  },
                  onSaveScene: (id: string) => {
                    console.log('index.vue 接收到save-scene事件:', id);
                    if (threedShowRef.value) {
                      threedShowRef.value.__veauryVueRef__.saveScene(id);
                    } else {
                      console.error('threedShowRef.value未定义');
                    }
                  },
                  onCheckboxChoose: (checkedNode) => {
                    // 在3D视图组件中处理节点选中状态变化
                    console.log('index.vue 接收到checkbox-choose事件:', checkedNode);
                    
                    if (threedShowRef.value) {                      
                      threedShowRef.value.__veauryVueRef__.handleNodeCheck(
                        checkedNode.id, 
                        checkedNode.curveType,
                        checkedNode.isChecked,
                        checkedNode.moduleType,
                        checkedNode.wellboreId==""?null:checkedNode.wellboreId,
                        checkedNode.datasetId==""?null:checkedNode.datasetId
                      );
                    } else {
                      console.error('threedShowRef.value未定义');
                    }
                  },
                  onSettingChange: (setting,wellboreThickness,displayMode,min,max,color,minCalibrate,maxCalibrate,minColor,maxColor) => {

                    if(propertyRef.value){
                      console.log('进入onSettingChange');
                      
                      // 根据不同类型配置不同的表单选项
                      let options = [];
                      
                      if(setting.label === '实际轨迹' || setting.label === '设计轨迹') {
                        // 实际轨迹和设计轨迹只显示颜色和粗细
                        options = [
                          {
                            label: "颜色",
                            value: color || "#ff0000",
                            type: "color",
                            order: 1
                          },
                          {
                            label: "粗细",
                            value: wellboreThickness,
                            type: "number",
                            order: 2
                          }
                        ];
                      } else {
                        // 其他类型显示完整的表单选项
                        options = [
                          {
                            label: "颜色",
                            value: color || "#ff0000",
                            type: "color",
                            order: 1
                          },
                          {
                            label: "粗细",
                            value: wellboreThickness,
                            type: "number",
                            order: 2
                          },
                          {
                            label: "展示方式",
                            value: displayMode,
                            type: "select",
                            dicData: [
                              { label: "正常", value: "normal" },
                              { label: "2D", value: "2D" },
                              { label: "3D", value: "3D" }
                            ],
                            order: 3
                          },
                          {
                            label: "Min Value",
                            value: min,
                            group: "config",
                            disabled: true,
                            type: "input",
                            order: 4
                          },
                          {
                            label: "Max Value",
                            value: max,
                            group: "config",
                            disabled: true,
                            type: "input",
                            order: 5
                          },
                          {
                            label: "Min Calibrate",
                            value: minCalibrate,
                            group: "config",
                            type: "input",
                            order: 6
                          },
                          {
                            label: "Max Calibrate",
                            value: maxCalibrate,                            
                            group: "config",
                            type: "input",
                            order: 7
                          },
                          {
                            label: "Min Color",
                            value: minColor || "#ff0000",
                            group: "config",
                            type: "color",
                            order: 8
                          },
                          {
                            label: "Max Color",
                            value: maxColor || "#ff0000",
                            group: "config",
                            type: "color",
                            order: 9
                          }
                        ];
                      }
                      
                      // 定义表单配置
                      formOption.value = {
                        formName: "基本属性",
                        formNode:setting,
                        options: options
                      };
                      propertyRef.value.__veauryVueRef__.init(formOption.value);
                    }
                  }
                }),
                closable: false,
                cached: true,
                group: "left",
              },
            ],
          },
        ],
      },
      {
        mode: "vertical" as DockMode,
        size: 1000,
        children: [
          {
            id: "main",
            group: "main",
            tabs: [
            {
                id: "ThreeDShows",
                title: "3DVisual",
                content: React.createElement(ThreeDShowsContent, {
                  ref: (instance: any): void => {
                    // 保存React包装器的引用
                    threedShowRef.value = instance;
                  },
                  onClick: (text: string, id: string, label: string) => {

                  },
                }),
                closable: false,
                cached: true,
                group: "left",
              },
            ],
            panelLock: {
              panelStyle: "main",
              minWidth: 400,
              minHeight: 200,
            },
          },
        ],
      },
      {
        mode: "vertical" as DockMode,
        size: 300,
        children: [
        {
            id: "right",
            group: "right",
            tabs: [
              {
                id: "property",
                title: "property",
                content: React.createElement(PropertyContent, {
                  ref: (instance: any): void => {
                    // 保存React包装器的引用
                    propertyRef.value = instance;
                  },
                  formOption: formOption.value,
                  onUpdateAvueForm: (value, column, formName) => {
                     //获取表单更新值
                     console.log(value, column.prop, formName);
                  },
                  onApply: (formData) => {
                    if(resourceTreeRef.value){
                      resourceTreeRef.value.__veauryVueRef__.updateSettingCache(formData);
                    }
                    if (threedShowRef.value) {                                            
                      // 从formData中提取需要的值
                      const setting = formData.formNode;
                      const color = formData.options.find(option => option.label === '颜色')?.value || '#ff0000';
                      const wellboreThickness = formData.options.find(option => option.label === '粗细')?.value || 1;
                      const displayMode = formData.options.find(option => option.label === '展示方式')?.value || 'normal';
                      const min = formData.options.find(option => option.label === 'Min Value')?.value || 0;
                      const max = formData.options.find(option => option.label === 'Max Value')?.value || 100;
                      const minCalibration = formData.options.find(option => option.label === 'Min Calibrate').value;
                      const maxCalibration = formData.options.find(option => option.label === 'Max Calibrate').value;
                      const minColor = formData.options.find(option => option.label === 'Min Color')?.value || '#FF0000';
                      const maxColor = formData.options.find(option => option.label === 'Max Color')?.value || '#FF0000';
                      if(setting.label==='实际轨迹'){
                        //实际轨迹和设计轨迹使用wellboreId作为三维模型唯一识别参数
                        threedShowRef.value.__veauryVueRef__.setCurveSetting(setting.wellboreId+'_1', wellboreThickness, displayMode, null, null, color);
                      } else if(setting.label==='设计轨迹'){
                        //实际轨迹和设计轨迹使用wellboreId作为三维模型唯一识别参数
                        threedShowRef.value.__veauryVueRef__.setCurveSetting(setting.wellboreId+'_0', wellboreThickness, displayMode, null, null, color);
                      }
                      else if(setting.moduleType===7){                        
                        //井筒使用id作为三维模型唯一识别参数
                        threedShowRef.value.__veauryVueRef__.setCurveSetting(setting.id+'_99', wellboreThickness, displayMode, min, max, color, minCalibration, maxCalibration, minColor, maxColor);
                      }
                      else{
                        threedShowRef.value.__veauryVueRef__.setCurveSetting(setting.id+'_99', wellboreThickness, displayMode, null, null, color, minCalibration, maxCalibration, minColor, maxColor);
                      }
                      
                    }                    
                  },
                  onReset: (formData) => {
                    console.log('重置按钮点击，表单数据：', formData);
                    
                    if (threedShowRef.value) {
                      console.log('重置setting', formData);
                      
                      // 从formData中提取需要的值
                      const setting = formData.formNode;
                      const color = formData.options.find(option => option.label === '颜色')?.value || '#ff0000';
                      const wellboreThickness = formData.options.find(option => option.label === '粗细')?.value || 1;
                      const displayMode = formData.options.find(option => option.label === '展示方式')?.value || 'normal';
                      const min = formData.options.find(option => option.label === 'Min Value')?.value || 0;
                      const max = formData.options.find(option => option.label === 'Max Value')?.value || 100;
                      const minCalibration = formData.options.find(option => option.label === 'Min Calibrate').value;
                      const maxCalibration = formData.options.find(option => option.label === 'Max Calibrate').value;
                      const minColor = formData.options.find(option => option.label === 'Min Color')?.value || '#FF0000';
                      const maxColor = formData.options.find(option => option.label === 'Max Color')?.value || '#FF0000';
                      
                      if(setting.label==='实际轨迹'){
                        //实际轨迹和设计轨迹使用wellboreId作为三维模型唯一识别参数
                        threedShowRef.value.__veauryVueRef__.setCurveSetting(setting.wellboreId+'_1', wellboreThickness, displayMode, null, null, color);
                      } else if(setting.label==='设计轨迹'){
                        //实际轨迹和设计轨迹使用wellboreId作为三维模型唯一识别参数
                        threedShowRef.value.__veauryVueRef__.setCurveSetting(setting.wellboreId+'_0', wellboreThickness, displayMode, null, null, color);
                      }
                      else if(setting.moduleType===7){                        
                        //井筒使用id作为三维模型唯一识别参数
                        threedShowRef.value.__veauryVueRef__.setCurveSetting(setting.id+'_99', wellboreThickness, displayMode, min, max, color, minCalibration, maxCalibration, minColor, maxColor);
                      }
                      else{
                        threedShowRef.value.__veauryVueRef__.setCurveSetting(setting.id+'_99', wellboreThickness, displayMode, null, null, color, minCalibration, maxCalibration, minColor, maxColor);
                      }
                      
                    }
                    
                    // 更新缓存
                    if(resourceTreeRef.value && formData && formData.formNode && formData.options){
                      const nodeId = formData.formNode.id;
                      const options = formData.options;
                      const wellboreThickness = options.find(opt => opt.label === '粗细')?.value;
                      const displayMode = options.find(opt => opt.label === '展示方式')?.value;
                      const min = options.find(opt => opt.label === 'Min Value')?.value;
                      const max = options.find(opt => opt.label === 'Max Value')?.value;
                      const color = options.find(opt => opt.label === '颜色')?.value;
                      const minCalibration = options.find(opt => opt.label === 'Min Calibrate')?.value;
                      const maxCalibration = options.find(opt => opt.label === 'Max Calibrate')?.value;
                      const minColor = options.find(opt => opt.label === 'Min Color')?.value;
                      const maxColor = options.find(opt => opt.label === 'Max Color')?.value;
                      // 直接更新缓存，避免触发事件
                      resourceTreeRef.value.__veauryVueRef__.settingCache[nodeId] = {
                        wellboreThickness: wellboreThickness,
                        min: min,
                        max: max,
                        displayMode: displayMode,
                        color: color,
                        minCalibration: minCalibration,
                        maxCalibration: maxCalibration,
                        minColor: minColor,
                        maxColor: maxColor
                      };
                      console.log('重置时直接更新缓存');
                    }
                  }
                }),
                closable: false,                
              },
            ],
          },
        ],
      },
    ],
  },
};

onMounted(() => {
  //addLeftTab();
});
// 使用Veaury将React组件转换为Vue组件
const ReactDockLayout = applyReactInVue(DockLayout);
</script>

<style lang="less">
/* 布局容器样式 */
.layout-box {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: unset !important;
  background: var(--theme-bg);
  .dock-layout {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: unset !important;
    background: var(--dock-divider-bg) !important;
  }
  .dock-layout > .dock-box {
    height: calc(100% - 105px);
  }

  /* 工具栏样式 */
  .toolbar {
    height: 40px;
    background: #2d2d2d;
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid #1e1e1e;
  }

  /* 获取编辑器内容按钮样式 */
  .get-value-btn {
    background: #0e639c;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    margin-left: 10px;
  }

  .get-value-btn:hover {
    background: #1177bb;
  }

  /* rc-dock主题变量 */
  :root {
    --dock-color: #cccccc;
    --dock-background: #1e1e1e;
    --dock-tab-color: #969696;
    --dock-tab-active-color: #ffffff;
    --dock-tab-background: #2d2d2d;
    --dock-tab-active-background: #1e1e1e;
    --dock-border-color: #252526;

    /* 指示器样式 */
    --dock-indicator-background: #0e639c;
    --dock-indicator-border: #1177bb;
    --dock-indicator-size: 40px;
    --dock-indicator-opacity: 0.9;
  }
  /* 全局隐藏所有面板的最大化按钮 */
  .dock-panel-max-btn {
    display: none !important;
  }
  .dock-divider {
    flex: 0 0 3px;
    background: var(--dock-divider-bg);
    transform: scaleY(1) !important;
  }
  .dock-panel {
    border: 0;
    background: var(--dock-pane-bg);
    border-radius: 10px 10px 0 0;
    // border-left: 0;
    // border-top: 0;
    // border-right: 0;
  }
  .dock-top .dock-bar {
    border-bottom: 0 !important;
    background: transparent;
    padding-left: 0;
  }
  .dock-tab-active {
    background: var(--left-tab-active-bg) !important;
    border-radius: 10px 10px 0 0;
  }
  .dock-top .dock-ink-bar {
    height: 0;
  }
  .dock-tab {
    border-bottom: 0;
    background: transparent;
    font-size: 12px;
  }
  .dock-tab > div {
    padding: 3px 14px;
  }
  .dock {
    //background: var(--dock-bg);
  }
  .dock-pane-cache {
    background: var(--theme-bg-white);
    border-radius: 0 10px 0 0;
  }
  .dock-tab-active {
    transform: none;
    &:hover {
      transform: none;
    }
    .dock-tab-close-btn {
      display: block;
    }
  }
  .dock-tab:hover .dock-tab-close-btn,
  .dock-tab-close-btn:focus {
    color: var(--font-color-white);
  }
  .dock-tab-close-btn {
    background: var(--btn-close-bg);
    border-radius: 50%;
    transform: scale(0.8, 0.8);
    color: #fff;
    right: 10px;
    display: none;
    width: 18px;
    top: 1px;
  }
  .dock-nav {
    height: 24px;
  }
  .dock-nav-list {
    transform: none !important;
    position: static;
    .dock-tab {
      position: static;
    }
  }
  .dock-nav-operations {
    display: none;
  }
  .dock-panel.dock-style-main .dock-tab {
    background: var(--dock-tab-bg);
    border-radius: 10px 10px 0 0;
    & > div {
      padding: 3px 14px !important;
    }
  }
  .dock-panel.dock-style-main {
    .dock-tab-active {
      background: var(--theme-bg-white) !important;
      color: var(--font-color-black);
    }
    .dock-content-animated {
      background: var(--theme-bg-white);
      border-radius: 10px 10px 0 0;
    }
  }
  .dock-style-left .dock-bar {
    padding-left: 8px;
  }
}
</style>
