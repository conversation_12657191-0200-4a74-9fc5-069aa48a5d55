<template>
  <div class="logs-charts">
    <div class="logs-nav">
      <van-nav-bar
        title="TFGN10 (raw)"
        @click-left="onClickLeft"
        @click-right="onClickRight"
      >
        <template #left>
          <van-icon name="arrow-left" color="#4472C4" :size="24" />
          <span class="left-text">Curves</span>
        </template>
        <template #right>
          <div class="right-content">
            <van-icon
              :name="showChart ? '/src/icon/dwprMobile/logs-digit-icon.svg' : '/src/icon/dwprMobile/logs-curve-icon.svg'"
              size="24"
            />
            <span class="right-text">{{showChart ? 'Digit' : 'Curve'}}</span>
          </div>
        </template>
      </van-nav-bar>
    </div>
    <div class="charts-content">
        <van-empty description="曲线图表" v-if="showChart" />
        <van-empty description="曲线列表" v-else />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
enum LogsType {
  Logs = "logs",
  Curves = "curves",
  Charts = "charts",
}
const emits = defineEmits<{
  (e: "goBack", type: LogsType): void;
}>();
const onClickLeft = () => {
  emits("goBack", LogsType.Curves);
};
const showChart = ref<boolean>(true);
const onClickRight = () => {
    showChart.value = !showChart.value;
};
</script>

<style scoped lang="scss">
.logs-charts {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.logs-nav {
  flex-shrink: 0;
}
.charts-content {
  flex: 1;
  background-color: #f5f5f5;

}

.left-text {
  font-weight: 500;
  font-size: 1.125rem;
  color: #4472c4;
}
.right-content {
  display: flex;
  align-items: center;
  .right-text {
    font-weight: 500;
    font-size: 1.25rem;
    color: #4472c4;
  }
}
:deep(.van-nav-bar__title) {
  font-weight: 500;
  font-size: 1.25rem;
  color: #3d3d3d;
}
</style>
