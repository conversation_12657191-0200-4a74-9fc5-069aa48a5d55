<template>
  <div class="color-code-page">
    <h1>颜色编码调色板</h1>
    
    <!-- 数值范围设置 -->
    <div class="range-settings">
      <h3>数值范围设置：</h3>
      <div class="range-inputs">
        <label>
          最小值:
          <input 
            type="number" 
            v-model.number="minValue"
            @change="updateRange"
            step="0.1"
          />
        </label>
        <label>
          最大值:
          <input 
            type="number" 
            v-model.number="maxValue"
            @change="updateRange"
            step="0.1"
          />
        </label>
        <button @click="resetRange" class="reset-btn">重置为0-100</button>
      </div>
    </div>

    <div class="palette-wrapper">
      <ColorCode
        ref="colorCodeRef"
        :initial-palette="defaultPalette"
        :min-value="minValue"
        :max-value="maxValue"
        @palette-updated="onPaletteUpdated"
        @save-palette="onSavePalette"
        @palette-loaded="onPaletteLoaded"
      />
      <!-- 保存和加载按钮 -->
      <div class="palette-controls">
        <button @click="exportPalette" class="control-btn export-btn">
          📥 导出JSON
        </button>
        <button @click="triggerImport" class="control-btn import-btn">
          📤 导入JSON
        </button>
        <input 
          ref="fileInput" 
          type="file" 
          accept=".json"
          @change="importPalette"
          style="display: none;"
        />
      </div>
    </div>
    
    <!-- 显示当前调色板信息 -->
    <div class="palette-info" v-if="currentPalette.length > 0">
      <h3>当前调色板信息：</h3>
      <div class="node-list">
        <div 
          v-for="(node, index) in currentPalette" 
          :key="index"
          class="node-info"
        >
          <span>节点 {{ index + 1 }}:</span>
          <span class="color-preview" :style="{ backgroundColor: node.color }"></span>
          <span>位置: {{ (node.position * 100).toFixed(1) }}%</span>
          <span>颜色: {{ node.color }}</span>
        </div>
      </div>
    </div>

    <!-- 测试颜色映射 -->
    <div class="test-section">
      <h3>测试颜色映射：</h3>
      <div class="test-input">
        <label>输入数值 ({{ minValue }} - {{ maxValue }}): </label>
        <input 
          type="number" 
          v-model.number="testValue" 
          :min="minValue" 
          :max="maxValue"
          @input="updateTestColor"
        />
        <div 
          class="test-color-preview" 
          :style="{ backgroundColor: testColor }"
          v-if="testColor"
        >
          {{ testColor }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ColorCode from './ColorCode.vue'

export default {
  name: 'ColorCodeIndex',
  components: {
    ColorCode
  },
  data() {
    return {
      // 默认调色板配置
      defaultPalette: [
        { position: 0, color: '#FF0000' },    // 红色 (最小值)
        { position: 0.5, color: '#FFFF00' },  // 黄色 (中间值)
        { position: 1, color: '#0000FF' }     // 蓝色 (最大值)
      ],
      minValue: 0,
      maxValue: 100,
      currentPalette: [],
      mapValueToColorFunction: null,
      testValue: 50,
      testColor: ''
    }
  },
  mounted() {
    this.currentPalette = [...this.defaultPalette]
  },
  methods: {
    // 当调色板更新时触发
    onPaletteUpdated(nodes, mapFunction) {
      this.currentPalette = [...nodes]
      this.mapValueToColorFunction = mapFunction
      this.updateTestColor()
      console.log('调色板已更新:', nodes)
    },
    
    // 当保存调色板时触发
    onSavePalette(nodes) {
      console.log('保存调色板:', nodes)
      // 这里可以添加保存到本地存储或发送到服务器的逻辑
      this.$message?.success?.('调色板已保存') || alert('调色板已保存')
    },

    // 当加载调色板时触发
    onPaletteLoaded(nodes) {
      this.currentPalette = [...nodes]
      console.log('调色板已加载:', nodes)
      this.$message?.success?.('调色板加载成功') || alert('调色板加载成功')
    },
    
    // 导出调色板为JSON文件
    exportPalette() {
      try {
        const jsonData = this.$refs.colorCodeRef.exportPaletteAsJSON()
        
        // 创建下载链接
        const blob = new Blob([jsonData], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `color-palette-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        
        this.$message?.success?.('调色板已导出') || alert('调色板已导出')
        console.log('导出的JSON数据:', jsonData)
      } catch (error) {
        console.error('导出失败:', error)
        this.$message?.error?.('导出失败') || alert('导出失败')
      }
    },

    // 触发文件选择
    triggerImport() {
      this.$refs.fileInput.click()
    },

    // 导入调色板JSON文件
    importPalette(event) {
      const file = event.target.files[0]
      if (!file) return

      if (!file.name.endsWith('.json')) {
        this.$message?.warning?.('请选择JSON文件') || alert('请选择JSON文件')
        return
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const success = this.$refs.colorCodeRef.loadPaletteFromJSON(e.target.result)
          if (!success) {
            this.$message?.error?.('JSON文件格式不正确') || alert('JSON文件格式不正确')
          }
        } catch (error) {
          console.error('导入失败:', error)
          this.$message?.error?.('导入失败') || alert('导入失败')
        }
      }
      reader.readAsText(file)
      
      // 清空input值，允许重复选择同一个文件
      event.target.value = ''
    },
    
    // 更新数值范围
    updateRange() {
      if (this.minValue >= this.maxValue) {
        this.$message?.warning?.('最小值必须小于最大值') || alert('最小值必须小于最大值')
        return
      }
      // 调整测试值到新范围内
      if (this.testValue < this.minValue) {
        this.testValue = this.minValue
      } else if (this.testValue > this.maxValue) {
        this.testValue = this.maxValue
      }
      this.updateTestColor()
    },

    // 重置数值范围
    resetRange() {
      this.minValue = 0
      this.maxValue = 100
      this.testValue = 50
      this.updateTestColor()
    },
    
    // 更新测试颜色
    updateTestColor() {
      if (this.mapValueToColorFunction && typeof this.testValue === 'number') {
        this.testColor = this.mapValueToColorFunction(this.testValue)
      }
    }
  }
}
</script>

<style scoped>
.color-code-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.palette-wrapper {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.palette-info {
  margin-bottom: 30px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 6px;
}

.palette-info h3 {
  margin-top: 0;
  color: #555;
}

.node-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.color-preview {
  width: 20px;
  height: 20px;
  border: 1px solid #000;
  border-radius: 3px;
  display: inline-block;
}

.test-section {
  padding: 15px;
  background-color: #e8f4fd;
  border-radius: 6px;
}

.test-section h3 {
  margin-top: 0;
  color: #2c5aa0;
}

.test-input {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.test-input input {
  padding: 5px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  width: 100px;
}

.test-color-preview {
  padding: 8px 12px;
  border: 1px solid #000;
  border-radius: 4px;
  color: white;
  text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
  min-width: 80px;
  text-align: center;
  font-family: monospace;
}

.palette-controls {
  margin-top: 15px;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.control-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: #f8f9fa;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.export-btn:hover {
  background-color: #d4edda;
  border-color: #28a745;
  color: #155724;
}

.import-btn:hover {
  background-color: #d1ecf1;
  border-color: #17a2b8;
  color: #0c5460;
}

.range-settings {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.range-settings h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #495057;
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.range-inputs label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 500;
  color: #495057;
}

.range-inputs input {
  padding: 5px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  width: 80px;
  font-size: 14px;
}

.reset-btn {
  padding: 6px 12px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s ease;
}

.reset-btn:hover {
  background-color: #545b62;
}
</style>
