<template>
    <!-- <div class="notify-list"> -->
        <div class="notify-item" v-for="value in list">
            <div class="notify-item-left">
                <van-icon name="/src/icon/dwprMobile/notify-item-icon.svg" :size="16" />
            </div>
            <div class="notify-item-right">
                <div class="notify-item-right-title">
                    系统通知
                </div>
                <div class="notify-item-right-content">
                    This is a messageThis is a messageThis is a messageThis
                </div>
            </div>

        </div>
    <!-- </div> -->
</template>

<script setup lang="ts">
const props = defineProps<{
    list: any[]
}>()
</script>

<style scoped lang="less">
.notify-list {
    height: 100%;
    width: 100%;
    background-color: #fff;
    padding: .375rem;
    // margin-top: .5625rem;
    
}
.notify-item {
    width: 100%;
    display: flex;
}
.notify-item-right-title {
    font-weight: 400;
    font-size: 1rem;
    color: #3D3D3D;
}
.notify-item-left {
    padding-top: .75rem;
}
.notify-item-right-content {
    width: 100%;
    font-weight: 400;
    font-size: .75rem;
    color: #737373;
    padding-bottom: .5625rem;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
.notify-item-right {
    flex: 1;
    min-width: 0;
    padding: .5rem 0;
    padding-left: .25rem;
    border-bottom:  .0625rem solid #E5E5E5;;
}
.notify-item:last-child {
    .notify-item-right {
        border-bottom: 0;
    }
}

</style>