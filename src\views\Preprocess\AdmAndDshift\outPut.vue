<template>
  <el-row>
    <el-col :span="24" style="padding: 10px">
      <el-row>
        <el-col :span="24">
          <el-form ref="roleRef" :model="form" label-width="140px">
            <el-form-item label="Input File">
              <el-tree-select
                v-model="inputFile"
                :data="DataSetTreeData"
                :render-after-expand="false"
                :props="defaultProps"
                @change="handleSelect"
                ><template #label="{ label, value }">
                  <span style="font-weight: bold">{{ label }}: </span>
                  {{
                    findPathById(projectStore.projectTreeData, value)
                      .map((item) => item.name)
                      .join(" > ")
                  }}
                </template>
              </el-tree-select>
            </el-form-item>
          </el-form>
          <el-button style="float: right" @click="handleStartDshift"
            >Start Dshift</el-button
          >
        </el-col>
      </el-row>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="24" style="padding: 10px">
      <el-table
        :data="tableData"
        @selection-change="handleSelectionChange"
        style="width: 100%; height: calc(100vh - 430px)"
      >
        <el-table-column type="index" label="NO" width="50" />
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="Curve" />
        <el-table-column prop="dataSet" label="DateSet"> </el-table-column>
      </el-table>
    </el-col>
  </el-row>
  <!-- 弹窗内显示进度条 -->
  <el-dialog
    title="生成过程中请稍后"
    v-model="dialogTableVisible"
    width="55%"
    append-to-body
  >
    <el-progress
      :text-inside="true"
      :stroke-width="20"
      :percentage="percentage"
      status="success"
    ></el-progress>
  </el-dialog>
</template>

<script setup name="Output">
import { onMounted, ref, watch } from "vue";
import { useProjectStore } from "@/stores/project";
import { ElTable, ElMessage } from "element-plus";
// import { startDshift, getDshiftProgress } from "@/api/epaps/tcd/data";
import { DepthCorrect } from "@/api/admAndDshift";
import { useDataStore } from "@/stores/data.js";
const emit = defineEmits(["update", "done"]);
const projectStore = useProjectStore();
const store = useDataStore();
const form = ref({});
const tableData = ref([]);
const DataSetTreeData = ref([]);
const RunTreeData = ref([]);
const inputFile = ref("");
const outputFile = ref("");
const dataSet = ref("");
const newDataSetName = ref("");
const multipleSelection = ref([]);
const timer = ref("");

const dialogTableVisible = ref(false);
const percentage = ref(0);
let WellboreId = "";
const defaultProps = {
  children: "children",
  label: "name",
  value: "id",
};

const handleSelectionChange = (val) => {
  multipleSelection.value = val;
};

//Start Dshift操作
function handleStartDshift() {
  let channelIds = [];
  // console.log(WellboreId);
  // console.log(inputFile.value);
  multipleSelection.value.forEach((item) => {
    channelIds.push(item.id);
  });
  // console.log(channelIds);
  // console.log(store.tableData);
  let tableData = store.tableData;
  const DepthPairs = tableData.map((item) => ({
    DepthSrc: Number(item.base),
    DepthDest: Number(item.offset),
  }));
  DepthCorrect({
    WellboreId: WellboreId,
    DatasetId: inputFile.value,
    ChannelIds: channelIds,
    DepthPairs: DepthPairs,
    DepthUnit: store.baseUnit,
  }).then((res) => {
    if (res.success) {
      ElMessage.success("操作成功");
    } else {
      ElMessage.error(res.message);
    }
  });
}

watch(percentage, (value) => {
  if (percentage.value >= 100) {
    setTimeout(() => {
      dialogTableVisible.value = false;
      emit("done");
      window.clearInterval(timer.value);
    }, 2000);
  }
});

//选择input file
function handleSelect(value) {
  tableData.value = [];
  const allChildren = getAllChildren(projectStore.projectTreeData, value);
  tableData.value = allChildren;
  const path = findPathById(projectStore.projectTreeData, value);
  if (path.length >= 2) {
    WellboreId = path[path.length - 2].id;
  }
}
// 获取指定ID节点的所有子节点
function getAllChildren(tree, targetId) {
  const result = [];
  const findChildren = (nodes) => {
    nodes.forEach((node) => {
      if (node.id === targetId) {
        // 找到目标节点，递归收集所有子节点
        const collectChildren = (childNodes, parentName) => {
          childNodes.forEach((child) => {
            result.push({
              ...child,
              dataSet: parentName,
            });
            if (child.children) collectChildren(child.children, child.name);
          });
        };
        if (node.children) collectChildren(node.children, node.name);
      } else if (node.children) {
        findChildren(node.children);
      }
    });
  };
  findChildren(tree);
  return result;
}
//初始化file select Data
function initProjectTreeData() {
  tableData.value = [];
  inputFile.value = "";
  outputFile.value = "";
  dataSet.value = "";
  if (projectStore.projectTreeData) {
    DataSetTreeData.value = filterTreeData(projectStore.projectTreeData);
  }
}
function filterTreeData(tree) {
  // 创建节点深拷贝函数
  const cloneNode = (node) => JSON.parse(JSON.stringify(node));

  return tree
    .map((node) => cloneNode(node)) // 先创建副本
    .filter((node) => {
      if (node.type === 2) return false;
      if (node.children) {
        // 对副本的children进行递归过滤
        node.children = filterTreeData(node.children);
      }
      return true;
    })
    .map(({ type, ...rest }) => rest);
}

// 定义查找路径的方法
function findPathById(tree, targetId) {
  // 递归查找路径
  function findPath(nodeId, nodes) {
    for (const node of nodes) {
      if (node.id === nodeId) {
        return [node]; // 找到目标节点，返回节点本身的路径
      }
      // 如果当前节点有子节点，递归查找子节点
      if (node.children && node.children.length > 0) {
        const childPath = findPath(nodeId, node.children);
        if (childPath.length > 0) {
          return [node, ...childPath]; // 返回父节点与子节点路径
        }
      }
    }
    return []; // 未找到时返回空数组
  }

  // 调用递归函数获取路径
  const path = findPath(targetId, tree);

  // 如果找到路径，拼接并返回节点名称路径

  if (path.length > 0) {
    //return path.map((item) => item.name).join(" > ");
    return path;
  } else {
    return []; // 如果没有找到该节点
  }
}
onMounted(() => {
  initProjectTreeData();
});
defineExpose({
  //initProjectTreeData,
});
</script>
