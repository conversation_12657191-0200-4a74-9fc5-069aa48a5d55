import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import type { Camera, WebGLRenderer } from 'three';

export class EnhancedOrbitControls {
  private controls: OrbitControls;

  constructor(
    camera: Camera,
    renderer: WebGLRenderer,
    options: {
      enableDamping?: boolean;
      dampingFactor?: number;
    } = {}
  ) {
    this.controls = new OrbitControls(camera, renderer.domElement);
    this.controls.enableDamping = options.enableDamping ?? true;
    this.controls.dampingFactor = options.dampingFactor ?? 0.05;
  }

  update(): this {
    this.controls.update();
    return this;
  }

  dispose(): this {
    this.controls.dispose();
    return this;
  }

  setTarget(x: number, y: number, z: number): this {
    this.controls.target.set(x, y, z);
    return this;
  }
}