<template>
  <div class="custom-tree">
    <el-tree
      v-loading="isLoading"
      :data="treeData"
      :props="defaultProps"
      node-key="id"
      default-expand-all
      highlight-current
      show-checkbox
      :expand-on-click-node="false"
      @node-click="handleNodeClick"
      @check="handleCheckChange"
      ref="tree"
      @node-contextmenu="handleContextMenu"
    >
      <template #default="{ node, data }">
        <span v-if="node.level === 1">
          <el-icon :size="15">
            <Folder />
          </el-icon>
          <span>{{ data.name }}</span>
        </span>
        <span v-else-if="node.level === 4" class="custom-tree-node">
          <img src="@/assets/image/channel.png" alt="" />
          <span>{{ data.name }}</span>
        </span>
        <span v-else-if="node.level === 3">
          <el-icon :size="15">
            <TrendCharts />
          </el-icon>
          <span>{{ data.name }}</span>
        </span>
        <span v-else>
          <el-icon :size="15">
            <Coin />
          </el-icon>
          <span>{{ data.name }}</span>
        </span>
      </template>
    </el-tree>
    <ContextMenu ref="contextMenu" @menu-action="handleMenuAction" />
  </div>
</template>

<script setup>
import "element-plus/dist/index.css";
import { ref, watch, onMounted, nextTick } from "vue";
import ContextMenu from "./ContextMenu.vue";
const props = defineProps({
  treeData: {
    type: Array,
    default: [],
  },
  isLoading: {
    // 新增加载状态prop
    type: Boolean,
    default: false,
  },
});
watch(
  () => props.treeData,
  (newVal) => {
    if (newVal.length) {
      nextTick(() => {
        showCheckbox();
      });
    }
  },
  { deep: true, immediate: true }
);
onMounted(() => {
  //console.log("treeData:", props.treeData);
});
const contextMenu = ref(null);
const tree = ref(null);
const defaultProps = {
  children: "children",
  label: "name",
};
const currentNode = ref(null);
const emit = defineEmits([
  "triggerAddTab",
  "triggerMenuAction",
  "update:isLoading",
  "triggerRemoveTab",
]);
function handleContextMenu(event, data, node) {
  event.preventDefault();
  // 根据节点层级和数据类型生成动态菜单
  const menuRules = {
    1: [
      // 工程层
      { action: "editProject", label: "Edit Project" },
      { action: "refresh", label: "Refresh" },
    ],
    2: [
      // 井眼层
      //{ action: "addDataset", label: "新建数据集" },
      { action: "refresh", label: "Refresh" },
    ],
    3: [
      // 数据集层
      { action: "cloneDataset", label: "Clone Dataset" },
      { action: "deleteDataset", label: "Delete Dataset" },
      { action: "refresh", label: "Refresh" },
    ],
    4: [
      // 曲线层
      { action: "editCurve", label: "Edit Curve" },
      { action: "refresh", label: "Refresh" },
    ],
  };
  const nodeType = node.level;
  const menuItems = menuRules[nodeType] || [];
  // 新增空数据判断：无菜单项或节点无数据时直接返回
  if (menuItems.length === 0) {
    return;
  }
  contextMenu.value.show(event, node, props.treeData[0].id, menuItems);
}
function handleMenuAction(action, node, projectId) {
  currentNode.value = node.data;
  emit("triggerMenuAction", {
    actionType: action,
    nodeData: node,
    projectId: projectId,
  });
}
function showCheckbox() {
  const nodes = document.querySelectorAll(".custom-tree-node");
  //获取nodes的父节点下的el-checkbox
  nodes.forEach((node) => {
    const checkbox = node.parentNode.querySelector(".el-checkbox");
    if (checkbox) {
      checkbox.style.display = "inline-block"; // 显示复选框
    }
  });
}
function renameNode() {}
function deleteNode() {}
// 独立提取的刷新处理
function handleRefresh() {
  if (
    currentNode.value?.isDataSetNode === 1 &&
    currentNode.value?.dataSetNodeType === 4
  ) {
    const parentNode = findParent(props.treeData, currentNode.value.id);
    getChannels(currentNode.value.nodeDataId, parentNode.nodeDataId);
  }
}

// 处理节点点击事件
function handleNodeClick(data, node) {
  if (node.level == 4) {
    currentNode.value = data;
    tree.value.setCurrentKey(data.id);
    emit("triggerAddTab", "1", node, data.name);
  }
}
// 处理复选框变化事件
function handleCheckChange(data, arr) {
  let node = tree.value.getNode(data.id);
  const isChecked = arr.checkedKeys.includes(data.id);
  if (isChecked) {
    emit("triggerAddTab", "", node, data.name);
  } else {
    emit("triggerRemoveTab", data.id);
  }
}
// 找到指定节点的父节点
function findParent(tree, id) {
  for (const node of tree) {
    if (node.children && node.children.length > 0) {
      const index = node.children.findIndex((child) => child.id === id);
      if (index !== -1) {
        return node;
      } else {
        const found = findParent(node.children, id);
        if (found) {
          return found;
        }
      }
    }
  }
  return null;
}
// 根据节点 id 查找对应节点
function findNodeById(tree, id) {
  for (const node of tree) {
    if (node.id === id) {
      return node; // 找到匹配的节点
    }
    if (node.children && node.children.length > 0) {
      const foundNode = findNodeById(node.children, id); // 递归查找
      if (foundNode) {
        return foundNode; // 如果在子节点中找到，返回该节点
      }
    }
  }
  return null; // 如果未找到，返回 null
}
//生成节点的唯一标识符
function generateGUID() {
  return "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx".replace(/[x]/g, () => {
    return ((Math.random() * 16) | 0).toString(16);
  });
}

function setCheckNode(id) {
  tree.value.setCurrentKey(id);
  // tree.value.setCheckedKeys([id]);
  currentNode.value = findNodeById(props.treeData, id);
}
function setCheckedKeys(id, type) {
  tree.value.setChecked(id, type);
}
defineExpose({
  setCheckNode,
  setCheckedKeys,
});
</script>

<style lang="less">
.custom-tree {
  height: 100%;
  .icon-button {
    font-size: 28px;
    /* 放大图标 */
    margin: 0 5px;
    /* 添加间距 */
    cursor: pointer;
    /* 改变鼠标指针样式 */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    /* 设置按钮宽度 */
    height: 32px;
    /* 设置按钮高度 */
    color: black;
    /* 设置图标颜色 */
  }
  .el-tree {
    height: 100%;
    border-radius: 0 10px 0 0;
    background: var(--dock-pane-cache-bg);
  }
  .el-tree-node {
    background: var(--dock-pane-cache-bg);
  }

  .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    background-color: #d9ecff;
  }
  .el-checkbox {
    display: none;
    margin-top: 8px;
  }
}
</style>
