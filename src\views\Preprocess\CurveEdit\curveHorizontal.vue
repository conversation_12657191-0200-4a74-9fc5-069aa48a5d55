<template>
  <div class="curveVertical" ref="curveHorizontalContainer">
    <div
      ref="echartsContainer"
      style="width: calc(100% - 300px); height: 100%"
    ></div>
    <TableTextEdit
      :tableData="tableData"
      :tableHeader="tableHeader"
      :startIndex="startIndex"
      :endIndex="endIndex"
      @curve-data="handleCurveData"
    />
    <!-- 曲线数据文本编辑 -->
    <TextEdit ref="textEditRef" @done="handleDone" />
  </div>
</template>
<script setup>
import { onMounted, ref, computed, markRaw, onUnmounted, watch } from "vue";
import * as echarts from "echarts";
import { ElMessage } from "element-plus";
import { useResizeObserver } from "@vueuse/core";
import { useEditCurveStore } from "@/stores/editCurveStore";
import { EditCurve } from "@/api/curve";
import TableTextEdit from "./tableTextEdit.vue";
import TextEdit from "./textEdit.vue";
const props = defineProps({
  tabId: {
    type: String,
    default: "",
  },
  curveData: {
    type: Object,
    default: {},
  },
});
const store = useEditCurveStore();
const echartsContainer = ref(null);
const chart = ref(null);
const tableData = ref([]);
const tableHeader = ref([]);
const depthSamples = ref([]);
const total = ref(0);
//折线图拖拽节点属性
const dragging = ref(false);
const dataOffsetX = ref("");
const dataOffsetY = ref("");
const editType = ref("");
const drawData = ref([]);
const dataIndex = ref("");
const dataIndexValue = ref("");
const xago = ref("");
const yago = ref("");
const xName = ref("");
const seriesData = ref([]);
const curveHorizontalContainer = ref(null);
const textEditRef = ref(null);
const startIndex = ref(0);
const endIndex = ref(20);
const datazoomStart = ref(90);
const datazoomEnd = ref(100);
let isFullSave = false;
let indexFieldName = "";
// 添加容器尺寸监听
useResizeObserver(echartsContainer, () => {
  chart.value?.resize();
});
const emit = defineEmits(["updateCurveData"]);
//图表初始化
function initChart() {
  if (chart.value) {
    chart.value.dispose();
  }
  let legend = [];
  legend.push(tableHeader.value[1].alias);
  chart.value = markRaw(echarts.init(echartsContainer.value));
  const dynamicSeries = generateDynamicSeries();
  const localDepthSamples = markRaw(depthSamples.value);
  const xData = [...localDepthSamples];
  chart.value.setOption({
    title: {
      text: "Data Curve",
    },
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: legend,
    },
    grid: {
      left: 20,
      right: 50,
      bottom: "3%",
      containLabel: true,
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: xData,
    },
    yAxis: {
      type: "value",
      //   boundaryGap: [0, "100%"],
      //   data: depthSamples.value,
    },
    dataZoom: [
      {
        type: "slider",
        start: 0,
        end: 10,
        filterMode: "empty",
      },
      {
        type: "inside",
        moveOnMouseMove: false,
        start: 0,
        end: 10,
        filterMode: "empty",
      },
    ],
    animation: false,
    series: [
      {
        id: "a",
        name: tableHeader.value[1].alias,
        type: "line",
        // sampling: "lttb",
        //data: seriesData.value,
        data: dynamicSeries,
      },
      {
        id: "b",
        type: "line",
        name: "data",
        // sampling: "lttb",
        color: "#ccc",
        data: [],
      },
      {
        id: "c",
        type: "line",
        color: "#f00",
        data: [],
      },
    ],
    graphic: {
      type: "rect",
      invisible: true,
      shape: {
        x: 100,
        y: 50,
        width: 200,
        height: 300,
      },
      style: {
        fill: "rgba(255, 0, 0, 0.5)",
        stroke: "#red",
        lineWidth: 2,
      },
    },
  });
  chart.value.getZr().on("mousedown", handleMouseDown);
  chart.value.getZr().on("mousemove", handleMouseMove);
  chart.value.getZr().on("mouseup", handleMouseUp);
  startIndex.value = 0;
  endIndex.value = 20;
  chart.value.on("dataZoom", function (params) {
    if (params.end) {
      datazoomStart.value = params.start;
      datazoomEnd.value = params.end;
      startIndex.value = Math.floor((total.value * params.start) / 100);
    } else {
      datazoomStart.value = params.batch[0].start;
      datazoomEnd.value = params.batch[0].end;
      startIndex.value = Math.floor(
        (total.value * params.batch[0].start) / 100
      );
    }
    endIndex.value = startIndex.value + 20;
  });
}
function handleCurveEdit() {
  textEditRef.value.openTextEdit(seriesData.value, 1);
  // if (type === 6) {
  //   textEditRef.value.openTextEdit(seriesData.value, 1);
  // } else {
  //   store.activeAction = type;
  // }
}
//鼠标按下事件
function handleMouseDown(params) {
  dragging.value = true;
  dataOffsetX.value = params.offsetX;
  dataOffsetY.value = params.offsetY;
  let pos = [params.offsetX, params.offsetY];
  let xy = chart.value.convertFromPixel("grid", pos);
  dataIndex.value = xy[0];
  dataIndexValue.value = xy[1].toFixed(4);
  let index = xy[0];
  if (store.activeAction === 1) {
    drawData.value = [];
  }
  const clampedIndex = Math.max(index, 0);
  if (store.minIndex === 0 || index < store.minIndex) {
    store.setMinIndex(clampedIndex);
  }
}
//鼠标移动事件
function handleMouseMove(params) {
  const mouseY = params.offsetY;
  let boxHeight = curveHorizontalContainer.value.offsetHeight;
  //   console.log(mouseY);
  //   console.log(boxHeight);
  if (mouseY > 60 && mouseY < boxHeight - 43) {
    if (dragging.value) {
      if (store.activeAction === 1) {
        DrawCurve(params);
      } else if (store.activeAction === 2) {
        CurveDrag(params);
      } else if (store.activeAction === 3) {
        ConstantValue(params);
      } else if (store.activeAction === 4) {
        HorizontalShift(params);
      } else if (store.activeAction === 5) {
        SmoothCurve(params);
      }
    }
  }
}
//鼠标放开事件
function handleMouseUp(params) {
  dragging.value = false;
  const localXname = markRaw(xName.value);
  const localDrawData = markRaw(drawData.value);
  const localTableData = markRaw(tableData.value);
  const localSeriesData = markRaw(seriesData.value);
  let pos = [params.offsetX, params.offsetY];
  let xy = chart.value.convertFromPixel("grid", pos);
  if (store.activeAction === 1) {
    let index = xy[0];
    if (index > store.maxIndex) {
      store.setMaxIndex(index);
    }
    for (let i = dataIndex.value + 1; i < localDrawData.length; i++) {
      let data = localDrawData[i];
      if (typeof data != "undefined") {
        localTableData[i][localXname] = data;
        localSeriesData[i] = data;
      }
    }
    chart.value.setOption({
      series: [
        {
          id: "a",
          data: seriesData.value,
        },
      ],
    });
    chart.value.setOption({
      series: [
        {
          id: "c",
          data: [],
        },
      ],
    });
  } else if (store.activeAction === 2) {
    let index = dataIndex.value;
    if (index > store.maxIndex) {
      store.setMaxIndex(index);
    }
  } else if (store.activeAction === 3) {
    let index = xy[0];
    if (index > store.maxIndex) {
      store.setMaxIndex(index);
    }
  } else if (store.activeAction === 5) {
    let pos = [params.offsetX, params.offsetY];
    let xy = chart.value.convertFromPixel("grid", pos);
    let arr = [];
    for (let i = dataIndex.value; i < xy[0]; i++) {
      const y = parseFloat(seriesData.value[i]);
      if (y != -9999 && y != -9999.25 && y != -999.25 && !isNaN(y)) {
        arr.push(y);
      }
    }
    let data = movingAverage(arr, 5);
    if (data.length > 1) {
      for (let j = 0; j < data.length; j++) {
        localSeriesData[j + dataIndex.value] = data[j].toFixed(4);
        localTableData[j + dataIndex.value][localXname] = data[j].toFixed(4);
      }
    }
    let index = xy[0];
    if (index > store.maxIndex) {
      store.setMaxIndex(index);
    }
    chart.value.setOption({
      series: [
        {
          id: "a",
          data: seriesData.value,
        },
      ],
      graphic: {
        invisible: true,
      },
    });
  } else if (store.activeAction === 7) {
    startIndex.value = dataIndex.value - 1;
    endIndex.value = dataIndex.value + 20 - 1;
  }
  dataIndex.value = "";
  xago.value = "";
  yago.value = "";
  drawData.value = [];
  //修改后通知父组件更新
  submitCurveData();
}
//画曲线
function DrawCurve(params) {
  let pos = [params.offsetX, params.offsetY];
  let xy = chart.value.convertFromPixel("grid", pos);
  const x = xy[0];
  const y = xy[1];
  if (xago.value != "" && x != xago.value + 1) {
    for (let i = xago.value; i < x; i++) {
      drawData.value[i] = ((y - yago.value) / (x - xago.value) + y).toFixed(4);
    }
  }
  drawData.value[x] = y.toFixed(4);
  chart.value.setOption({
    series: [
      {
        id: "c",
        data: drawData.value,
      },
    ],
  });
  xago.value = x;
  yago.value = y;
}
//曲线拖拽
function CurveDrag(params) {
  const localTableData = markRaw(tableData.value);
  const localSeriesData = markRaw(seriesData.value);
  const localDepthSamples = markRaw(depthSamples.value);

  let pos = [params.offsetX, params.offsetY];
  let xy = chart.value.convertFromPixel("grid", pos);
  localTableData[dataIndex.value][xName.value] = xy[1].toFixed(4);
  localSeriesData[dataIndex.value] = xy[1].toFixed(4);

  let y0index = dataIndex.value - 5;
  let y1index = dataIndex.value + 5;
  if (y0index < 0) {
    y0index = 0;
  }
  if (y1index >= localSeriesData.length) {
    y1index = localSeriesData.length - 1;
  }
  let x0 = localDepthSamples[y0index];
  let y0 = localSeriesData[y0index];
  let x = localDepthSamples[dataIndex.value];
  let y = localSeriesData[dataIndex.value];
  let x1 = localDepthSamples[y1index];
  let y1 = localSeriesData[y1index];

  for (let index = y0index; index < dataIndex.value; index++) {
    let xt = quadraticBezier(x0, y0, x, y, localDepthSamples[index]);
    if (!isNaN(xt)) {
      localSeriesData[index] = xt;
      localTableData[index][xName.value] = xt;
    }
  }

  for (let index = dataIndex.value + 1; index < y1index; index++) {
    let xt = quadraticBezier(x, y, x1, y1, localDepthSamples[index]);
    if (!isNaN(xt)) {
      localSeriesData[index] = xt;
      localTableData[index][xName.value] = xt;
    }
  }

  chart.value.setOption({
    series: [
      {
        id: "a",
        data: seriesData.value,
      },
    ],
  });
}
//画矩形
function ConstantValue(params) {
  const localTableData = markRaw(tableData.value);
  const localSeriesData = markRaw(seriesData.value);

  let pos = [params.offsetX, params.offsetY];
  let xy = chart.value.convertFromPixel("grid", pos);
  const x = xy[0];

  if (xago.value != "" && x != xago.value + 1) {
    for (let i = xago.value; i < x; i++) {
      localTableData[i][xName.value] = dataIndexValue.value;
      localSeriesData[i] = dataIndexValue.value;
    }
  }

  localTableData[x][xName.value] = dataIndexValue.value;
  localSeriesData[x] = dataIndexValue.value;
  chart.value.setOption({
    series: [
      {
        id: "a",
        data: seriesData.value,
      },
    ],
  });
  xago.value = x;
}
//曲线平移
function HorizontalShift(params) {
  isFullSave = true;
  const localTableData = markRaw(tableData.value);
  const localSeriesData = markRaw(seriesData.value);
  const localxName = markRaw(xName.value);

  let pos = [params.offsetX, params.offsetY];
  let xy = chart.value.convertFromPixel("grid", pos);
  const x = xy[0];
  let minusValue = parseFloat(x) - dataIndexValue.value;
  if (minusValue > 10) {
    minusValue = 10;
  }
  if (minusValue < -10) {
    minusValue = -10;
  }

  for (let i = 0; i < localSeriesData.length; i++) {
    let x = parseFloat(localTableData[i][localxName]);
    let x1 = parseFloat(localSeriesData[i]);
    if (x != -9999 && x != -9999.25 && x != -999.25) {
      localTableData[i][localxName] = (x + minusValue).toFixed(4);
    }
    if (x1 != -9999 && x1 != -9999.25 && x1 != -999.25) {
      localSeriesData[i] = (x1 + minusValue).toFixed(4);
    }
  }

  chart.value.setOption({
    series: [
      {
        id: "a",
        data: localSeriesData,
      },
    ],
  });
}
//曲线滤波移动
function SmoothCurve(params) {
  let wid = params.event.offsetX - dataOffsetX.value;
  let boxHeight = curveHorizontalContainer.value.offsetHeight;
  chart.value.setOption({
    graphic: {
      type: "rect",
      invisible: false,
      shape: {
        x: dataOffsetX.value,
        y: 60,
        width: wid,
        height: boxHeight - 105,
      },
      style: {
        fill: "rgba(255, 0, 0, 0.5)",
        stroke: "#red",
        lineWidth: 2,
      },
    },
  });
}
//曲线滤波计算
function movingAverage(data, windowSize) {
  let sum = 0,
    result = [],
    i,
    j;
  for (i = 0; i < windowSize; i++) {
    sum += data[i];
  }
  result.push(sum / windowSize);

  for (j = windowSize; j < data.length; j++) {
    sum = sum - data[j - windowSize] + data[j];
    result.push(sum / windowSize);
  }

  return result;
}
//线性差值计算
function quadraticBezier(x1, y1, x2, y2, x) {
  try {
    let slope = new Decimal(y2)
      .minus(new Decimal(y1))
      .div(new Decimal(x2).minus(new Decimal(x1)))
      .toFixed(4);
    let y = new Decimal(x)
      .minus(new Decimal(x1))
      .times(new Decimal(slope))
      .plus(new Decimal(y1));
    return y.toFixed(4);
  } catch (error) {
    return "_";
  }
}
//更新表格数据的同时更新统计图
function handleCurveData(row) {
  const localTableData = markRaw(tableData.value);
  const localSeriesData = markRaw(seriesData.value);
  localTableData.forEach((item, index) => {
    if (item[indexFieldName] === row[indexFieldName]) {
      localTableData[index].editing = false;
      const y1 = localTableData[index][xName.value];

      if (
        parseFloat(y1) === -9999 ||
        parseFloat(y1) === -9999.25 ||
        parseFloat(y1) === -999.25
      ) {
        localSeriesData[index] = "_";
      } else {
        localSeriesData[index] = y1;
      }
      chart.value.setOption({
        series: [
          {
            id: "a",
            data: localSeriesData,
          },
        ],
      });
    }
  });
  //修改后通知父组件更新
  submitCurveData();
}
function handleDone(value) {
  const localTableData = markRaw(tableData.value);
  const localSeriesData = markRaw(seriesData.value);

  let button = document.querySelectorAll(".button");
  button.forEach((bnt) => {
    bnt.style.backgroundColor = "#fff";
    bnt.style.color = "#000";
  });
  //store.activeAction = "";
  if (value) {
    for (let i = 0; i < value.length; i++) {
      const v = value[i];
      if (v != "_") {
        localSeriesData[i] = value[i];
        localTableData[i][xName.value] = value[i];
      }
    }
    chart.value.setOption({
      series: [
        {
          id: "a",
          data: localSeriesData,
        },
      ],
    });
  }
  //修改后通知父组件更新
  submitCurveData();
}
function generateDynamicSeries() {
  seriesData.value = [];
  tableData.value.forEach((item, index) => {
    let d = item[xName.value];
    if (
      parseFloat(d) === -9999 ||
      parseFloat(d) === -9999.25 ||
      parseFloat(d) === -999.25
    ) {
      seriesData.value.push("_");
    } else {
      seriesData.value.push(d);
    }
  });
  return seriesData.value;
}
function submitCurveData() {
  const newData = {
    ...props.curveData,
    data: [...seriesData.value],
  };
  emit("updateCurveData", newData);
}
function getData() {
  seriesData.value = [];
  let data = [Math.random() * 300];
  for (let i = 1; i < 2000; i++) {
    data.push(Math.round((Math.random() - 0.5) * 20 + data[i - 1]));
  }
  seriesData.value = data;
  //seriesData.value.reverse();
  return seriesData.value;
}
// 添加窗口resize监听
function handleResize() {
  requestAnimationFrame(() => {
    chart.value?.resize();
  });
}
function saveCurve() {
  const rawData = [...seriesData.value].map(Number);
  const slicedData = isFullSave
    ? rawData
    : rawData.slice(store.minIndex, store.maxIndex + 1);
  const formData = new FormData();
  formData.append("wellId", store.wellId);
  formData.append("datasetId", store.datasetId);
  formData.append("id", store.curveId);
  formData.append("startIndex", isFullSave ? 0 : store.minIndex);
  formData.append("data", slicedData.join(","));
  // console.log(isFullSave ? 0 : store.minIndex);
  // console.log(store.maxIndex);
  // console.log(slicedData);
  EditCurve(formData).then((res) => {
    if (res.success) {
      store.setIsStartEdit(false);
      store.setMinIndex(0);
      store.setMaxIndex(0);
      isFullSave = false;
      ElMessage({
        message: "操作成功",
        type: "success",
      });
    } else {
      ElMessage({
        message: res.message,
        type: "error",
      });
    }
  });
}
onMounted(async () => {
  watch(
    () => props.curveData,
    (data) => {
      if (!data?.index?.indexValue?.length || !data?.data?.length) {
        return;
      }
      indexFieldName = data.index.indexType === 0 ? "Depth" : "Time";
      let indexFieldUnit = data.index.indexUnit;
      let dataFieldName = data.name;
      let dataFieldUnit = data.unit;
      depthSamples.value = data.index.indexValue;
      if (
        parseFloat(depthSamples.value[0]) >
        depthSamples.value[depthSamples.value.length - 1]
      ) {
        depthSamples.value.reverse();
      }
      seriesData.value = data.data;
      tableData.value = data.index.indexValue.map((depth, index) => ({
        [dataFieldName]: seriesData.value[index]?.toString() || "-9999.0",
        [indexFieldName]: depth.toString(),
      }));
      tableHeader.value = [
        {
          name: indexFieldName,
          alias: indexFieldName + "(" + indexFieldUnit + ")",
          width: "130",
        },
        {
          name: dataFieldName,
          alias: dataFieldName + "(" + dataFieldUnit + ")",
          width: "170",
        },
      ];
      total.value = tableData.value.length;
      xName.value = data.name;
      initChart();
    },
    { immediate: true }
  );
  // let list = [];
  // for (let i = 0; i < 2000; i++) {
  //   list.push(10000 + i);
  // }
  // depthSamples.value = list;
  // getData();
  // // 组装数据格式
  // tableData.value = list.map((depth, index) => ({
  //   BIT: seriesData.value[index]?.toString() || "-9999.0",
  //   DEPTH: depth.toString(),
  // }));
  // total.value = tableData.value.length;
  // xName.value = "BIT";
  // initChart();
  window.addEventListener("resize", handleResize);
  store.registerCurveEdit(props.tabId, componentInstance);
});
const componentInstance = {
  handleCurveEdit,
  saveCurve,
};
defineExpose(componentInstance);
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  if (chart.value) {
    chart.value.dispose(); // 确保卸载时销毁实例
    chart.value = null;
  }
  //store.registerCurveEdit(null);
});
</script>
<style lang="less">
.curveVertical {
  width: 100%;
  height: calc(100% - 32px);
  display: flex;
}
</style>
