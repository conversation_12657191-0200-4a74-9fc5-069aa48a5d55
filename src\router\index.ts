import { createRouter, createWebHashHistory } from 'vue-router'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/PythonFreeModule',
      component: () => import('@/components/DockLayout.vue')
    },
    {
      path: '/3DViews',
      component: () => import('@/views/3DViews/index.vue')
    },
    {
      path: '/3DImport',
      component: () => import('@/views/3DImport/index.vue')
    },
    {
      path: '/property',
      component: () => import('@/components/DockLayout.vue')
    },
    {
      path: '/ColorCode',
      component: () => import('@/views/ColorCode/index.vue')
    },
    {
      path: '/Dwpr',
      component: () => import('@/views/DwprMobile/newHome.vue'),
      children: [
        {
          path: '/Dwpr/well',
          component: () => import('../views/DwprMobile/pages/wells/wells.vue')
        },
        {
          path: '/Dwpr/work',
          component: () => import('../views/DwprMobile/pages/workbench/workbench.vue')
        },
        {
          path: '/Dwpr/detail',
          component: () => import('../views/DwprMobile/pages/wellsDetail/wellsDetail.vue')
        }
      ]
    },
    {
      path: '/Dwpr/list',
      component: () => import('@/views/DwprMobile/list.vue')
    },
    {
      path: '/DataPreprocess',
      component: () => import('@/views/Preprocess/index.vue')
    }
  ],
})

export default router
