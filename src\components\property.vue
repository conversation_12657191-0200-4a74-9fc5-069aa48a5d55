<template>
  <el-card class="box-card">
    <template #header>
      <div class="card-header">
        <span>{{ formName }}</span>
      </div>
    </template>
    <div class="avueForm" v-if="hasValidFormData">
      <avue-form :option="option" v-model="form"></avue-form>
    </div>
    <div class="no-data-tip" v-else>
      <el-empty description="请选择一个节点查看属性" :image-size="80" />
    </div>
    <div class="card-footer" v-if="hasValidFormData">
      <el-button type="primary" @click="handleApply">应用</el-button>
      <el-button type="primary" @click="handleReset">重置</el-button>
    </div>
  </el-card>
</template>
<script setup>
import { ref, onMounted, nextTick, computed } from "vue";
const emit = defineEmits(["updateAvueForm", "apply", "reset"]);
const props = defineProps({ 
  formOption: {
    type: Object,
    default: {},
  },
});
const isInitializing = ref(true);
const form = ref({});
const formName = ref("");
const formdata=ref({});
const resetFormData = ref({});
const option = ref({
  labelWidth: 90,
  submitBtn: false,
  emptyBtn: false,
  size: "small",
  group: [],
  column: {},
});

// 计算属性：判断是否有有效的表单数据
const hasValidFormData = computed(() => {
  return formdata.value && 
         formdata.value.formNode && 
         formdata.value.options && 
         Array.isArray(formdata.value.options) && 
         formdata.value.options.length > 0;
});

const init = (formOption) => {
  console.log('进入init');
  
  // 验证 formOption 是否有效
  if (!formOption || typeof formOption !== 'object') {
    console.warn('formOption 无效，使用默认值', formOption);
    return;
  }
  
  // 验证 formOption 的基本结构
  if (!formOption.formName || !formOption.formNode || !Array.isArray(formOption.options)) {
    console.warn('formOption 结构不完整', formOption);
    return;
  }
  
  // 设置当前数据
  formdata.value = formOption;
  
  // 获取节点唯一标识符
  const nodeId = formOption.formNode?.id;
  
  if (nodeId) {
    // 如果resetFormData中不存在该节点的数据，则存储初始数据
    if (!resetFormData.value[nodeId]) {
      try {
        // 深拷贝保存初始数据
        resetFormData.value[nodeId] = JSON.parse(JSON.stringify(formOption));
        console.log(`节点 ${nodeId} 初始数据已保存:`, resetFormData.value[nodeId]);
      } catch (error) {
        console.error('保存初始数据失败:', error);
        // 如果 JSON 序列化失败，使用简单的对象拷贝
        resetFormData.value[nodeId] = {
          formName: formOption.formName || '',
          formNode: formOption.formNode || {},
          options: (formOption.options || []).map(option => ({
            label: option.label || '',
            value: option.value !== undefined ? option.value : '',
            type: option.type || 'input',
            order: option.order || 0,
            group: option.group || undefined,
            dicData: option.dicData || undefined
          }))
        };
        console.log(`节点 ${nodeId} 初始数据保存(备用方式):`, resetFormData.value[nodeId]);
      }
    } else {
      console.log(`节点 ${nodeId} 初始数据已存在，不重新保存`);
    }
  }
  
  isInitializing.value = true;
  let data = formOption?.options || props.formOption.options;
  form.value = {};
  formName.value = formOption?.formName || props.formOption.formName;
  data = [...data].sort((a, b) => {
    const aOrder = Number.isInteger(a.order) ? a.order : Infinity;
    const bOrder = Number.isInteger(b.order) ? b.order : Infinity;
    return aOrder - bOrder || a.label.localeCompare(b.label);
  });
  option.value = JSON.parse(
    JSON.stringify({
      labelWidth: 90,
      submitBtn: false,
      emptyBtn: false,
      size: "small",
      group: [],
      column: {},
    })
  );
  nextTick(() => {
    // 新增分组映射表
    const groupMap = new Map();
    data.forEach((item) => {
      form.value[item.label] = item.value;
      if (item.group) {
        // 获取或创建分组
        if (!groupMap.has(item.group)) {
          groupMap.set(item.group, {
            label: item.group,
            column: {},
          });
        }
        // 添加字段到分组
        const currentGroup = groupMap.get(item.group);
        currentGroup.column[item.label] = {
          label: item.label,
          disabled: item.disabled,
          span: 22,
          type: item.type,
          ...(item.type === "color" && {
            colorFormat: "hex",
            showAlpha: false,
          }),
          ...(item.type === "date" && {
            format: item.format || "YYYY-MM-DD",
            valueFormat: item.valueFormat || "YYYY-MM-DD",
          }),
          ...(item.type === "select" && {
            props: item.props || {},
            dicData: item.dicData || [],
          }),
          change: ({ value, column }) => {
            if (!isInitializing.value && form.value[item.label] !== value) {
              emit("updateAvueForm", value, column, formName.value);
            }
          },
        };
      } else {
        option.value.column[item.label] = {
          label: item.label,
          disabled: item.disabled,
          span: 22,
          type: item.type,
          ...(item.type === "color" && {
            colorFormat: "hex",
            showAlpha: false,
          }),
          ...(item.type === "date" && {
            format: item.format || "YYYY-MM-DD",
            valueFormat: item.valueFormat || "YYYY-MM-DD",
          }),
          ...(item.type === "select" && {
            props: item.props || {},
            dicData: item.dicData || [],
          }),
          change: ({ value, column }) => {
            if (!isInitializing.value && form.value[item.label] !== value) {
              emit("updateAvueForm", value, column, formName.value);
            }
          },
        };
      }
    });
    option.value.group = Array.from(groupMap.values());
    setTimeout(() => {
      isInitializing.value = false;
    }, 100);
  });
};
const matchType = (item) => {
  if (item.type) {
    return item.type;
  } else {
    if (typeof item.value === "string") {
      // 检测日期格式 (YYYY-MM-DD, YYYY/MM/DD 等)
      const dateRegex = /^\d{4}[-\/]\d{2}[-\/]\d{2}$/;
      // 检测日期时间格式 (YYYY-MM-DD HH:mm:ss)
      const datetimeRegex = /^\d{4}[-\/]\d{2}[-\/]\d{2} \d{2}:\d{2}:\d{2}$/;

      if (dateRegex.test(item.value)) return "date";
      if (datetimeRegex.test(item.value)) return "datetime";
    }
    if (
      typeof item.value === "string" &&
      /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(item.value)
    ) {
      return "color";
    }
    if (item.dicData) return "select";
    if (typeof item.value === "number") return "number";
    return "input";
  }
};
const handleApply = () => {
  // 更新formdata中的值
  if (formdata.value && formdata.value.options) {
    formdata.value.options.forEach(option => {
      // 从form中获取当前值并更新到formdata中
      option.value = form.value[option.label];
    });
  }
  console.log('formdata.value', formdata.value);
  emit("apply", formdata.value);
};
const handleReset = () => {
  console.log('重置');
  
  // 获取当前节点ID
  const nodeId = formdata.value?.formNode?.id;
  
  if (!nodeId) {
    console.warn('无法获取当前节点ID，重置失败');
    return;
  }
  
  // 从resetFormData中获取该节点的初始数据
  const initialData = resetFormData.value[nodeId];
  
  if (!initialData) {
    console.warn(`节点 ${nodeId} 没有初始数据，重置失败`);
    return;
  }
  
  try {
    console.log(`重置节点 ${nodeId} 数据:`, initialData);
    
    // 深拷贝初始数据进行重置
    const resetData = JSON.parse(JSON.stringify(initialData));
    
    // 重新初始化表单
    init(resetData);
    
    // 触发重置事件
    emit("reset", resetData);
    
  } catch (error) {
    console.error('重置失败:', error);
  }
};
const clearResetData = (nodeId = null) => {
  if (nodeId) {
    // 清空特定节点的重置数据
    delete resetFormData.value[nodeId];
    console.log(`节点 ${nodeId} 的重置数据已清空`);
  } else {
    // 清空所有重置数据
    resetFormData.value = {};
    console.log('所有节点的重置数据已清空');
  }
};
defineExpose({
  init,
  clearResetData
});
onMounted(() => {
  init();
});
</script>
<style lang="less" scoped>
.el-card {
  height: 100%;
  // width: 300px;
  :deep(.el-card__header) {
    padding: 10px;
  }
}
:deep(.el-card__body) {
  padding-left: 0px;
  padding-right: 0px;
  padding-top: 0px;
}
:deep(.avueForm) {
  width: 100% !important;
  // .el-collapse-item__header {
  //   background: #f4f4f5 !important;
  .avue-group__header {
    height: 32px;
    line-height: 32px;
    .avue-group__title {
      font-size: 14px;
      font-weight: normal;
      margin-left: 5px;
    }
  }
  .avue-form__group {
    //margin-top: 10px;
    .el-col {
      &:first-child {
        margin-top: 10px;
      }
    }
  }
  .el-collapse-item__header {
    background: #f4f4f5 !important;
    position: relative;
    margin-bottom: 0;
    .avue-group__header {
      height: 32px;
      line-height: 32px;
      margin-left: 10px;
      .avue-group__title {
        font-size: 14px;
        font-weight: normal;
        margin-left: 5px;
      }
    }

    .el-collapse-item__arrow {
      position: absolute;
    }
  }
  .el-form-item {
    margin-left: 10px;
    margin-right: 10px;
    margin-bottom: 10px;
    .el-form-item__label {
      justify-content: flex-start;
    }
  }
  // }
}
.card-footer {
  padding: 10px;
  text-align: center;
  border-top: 1px solid #EBEEF5;
}

.no-data-tip {
  padding: 40px 20px;
  text-align: center;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
