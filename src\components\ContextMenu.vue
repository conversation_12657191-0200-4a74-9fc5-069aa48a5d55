<template>
  <div
    v-if="visible"
    class="custom-context-menu"
    :style="{ left: left + 'px', top: top + 'px' }"
  >
    <el-menu class="el-menu-vertical-demo" :collapse="true">
      <template v-for="(item, index) in currentMenuItems" :key="index">
        <el-sub-menu v-if="item.children?.length > 0">
          <template #title>
            <div>{{ item.label }}</div>
          </template>
          <el-menu-item
            v-for="(v, i) in item.children"
            :key="i"
            @click="handleMenuClick(v.action)"
            >{{ v.label }}</el-menu-item
          >
        </el-sub-menu>
        <el-menu-item v-else>
          <div @click="handleMenuClick(item.action)">{{ item.label }}</div>
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<script>
export default {
  name: "ContextMenu",
  props: {
    // 定义菜单项的数据结构
    menuItems: {
      type: Array,
      default: () => [
        {
          label: "Create",
          action: "create",
          children: [
            { label: "Create File", action: "add" },
            { label: "Create Folder", action: "addFolder" },
          ],
        },
        { label: "Edit Node", action: "edit" },
        { label: "Delete Node", action: "delete" },
        { label: "Refresh", action: "refresh" },
      ],
    },
  },
  data() {
    return {
      visible: false,
      left: 0,
      top: 0,
      currentNode: null,
      currentMenuItems: this.menuItems, // 添加内部状态来存储当前菜单项
    };
  },

  watch: {
    menuItems: {
      handler(newItems) {
        this.currentMenuItems = newItems;
      },
      immediate: true
    }
  },
  methods: {
    show(event, node) {
      this.visible = true;
      this.left = event.clientX;
      this.top = event.clientY;
      this.currentNode = node;
      this.$nextTick(() => {
        this.checkPosition();
      });
    },

    hide() {
      this.visible = false;
      this.currentNode = null;
      // 不在这里自动重置，让调用方控制
    },

    handleMenuClick(action) {
      this.$emit("menu-action", action, this.currentNode);
      this.hide();
      // 重置为默认菜单项
      this.resetMenuItems();
    },

    checkPosition() {
      const menuElement = this.$el;
      if (!menuElement) return;

      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;
      const menuWidth = menuElement.offsetWidth;
      const menuHeight = menuElement.offsetHeight;

      if (this.left + menuWidth > windowWidth) {
        this.left = windowWidth - menuWidth;
      }
      if (this.top + menuHeight > windowHeight) {
        this.top = windowHeight - menuHeight;
      }
    },

    // 动态设置菜单项的方法
    setMenuItems(items) {
      this.currentMenuItems = items;
    },

    // 重置为默认菜单项
    resetMenuItems() {
      this.currentMenuItems = this.menuItems;
    },
  },

  mounted() {
    document.addEventListener("click", this.hide);
  },

  beforeUnmount() {
    document.removeEventListener("click", this.hide);
  },
};
</script>

<style scoped>
.custom-context-menu {
  position: fixed;
  background: #fff;
  border: 1px solid #ddd;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 2000;
  min-width: 120px;
}

.menu-item {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.menu-item:hover {
  background-color: #f5f7fa;
}
.el-menu--collapse {
  width: auto;
}
.el-menu-item {
  height: 40px;
  line-height: 40px;
}

:deep(.el-sub-menu__title) {
  height: 40px;
  line-height: 40px;
}
</style>
