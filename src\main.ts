/**
 * 主应用入口文件
 * 负责初始化 Vue 应用，集成各个核心模块
 */

// 导入全局样式
import './assets/main.css'

// 核心依赖导入
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { applyReactInVue } from 'veaury'  // Veaury用于Vue和React组件互操作
import React from 'react'
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css';
import '@/assets/theme.css';
import Avue from '@smallwei/avue';
import '@smallwei/avue/lib/index.css';
// 导入 Vant 组件库
import Vant from 'vant';
import 'vant/lib/index.css';
// 导入 vue-devui 组件库
import DevUI from 'vue-devui';
import 'vue-devui/style.css';
// 导入 @surely-vue/table 表格组件
import STable from '@surely-vue/table';
import '@surely-vue/table/dist/index.less';
// 导入根组件和路由配置
import App from './App.vue'
import router from './router'

// 创建Vue应用实例
const app = createApp(App)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
if (typeof crypto !== 'undefined' && !crypto.randomUUID) {
  crypto.randomUUID = function (): `${string}-${string}-${string}-${string}-${string}` {
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/.test(uuid)) {
      throw new Error('Generated UUID does not match expected format');
    }
    return uuid as `${string}-${string}-${string}-${string}-${string}`;
  };
}
// 注册全局插件
app.use(createPinia())  // 状态管理
app.use(router)        // 路由系统
app.use(ElementPlus)
app.use(Avue);
app.use(Vant);         // 移动端UI组件库
app.use(DevUI);        // DevUI组件库
app.use(STable);       // Surely Vue Table表格组件
// 挂载应用到DOM
app.mount('#app')

