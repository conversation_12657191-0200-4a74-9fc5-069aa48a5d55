<template>
  <div>
    <el-dialog
      title="Add DataSet"
      v-model="dialogVisible"
      width="700px"
      :close-on-click-modal="false"
    >
      <el-row>
        <el-col :span="4">
          <div style="height: 32px; line-height: 32px">Dataset</div>
        </el-col>
        <el-col :span="20">
          <el-select
            v-model="dataSet"
            placeholder="Please select dataset"
            @change="onDataSetChange"
          >
            <el-option
              v-for="dataSet in dataSetList"
              :key="dataSet.fileName"
              :label="dataSet.sourceFileName"
              :value="dataSet.fileName"
            ></el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row>
        <el-table
          v-loading="loading"
          ref="multipleTableRef"
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column
            property="channelName"
            label="曲线名称"
            width="100"
            :align="'center'"
          />
          <el-table-column
            prop="dataType"
            label="数据类型"
            width="100"
            :align="'center'"
          />
          <el-table-column label="颜色" width="120" :align="'center'">
            <template #default="scope">
              <div
                :style="`border-top: 3px solid ${scope.row.prsColor}; width: 100%`"
              ></div>
            </template>
          </el-table-column>
          <el-table-column
            prop="channelUnit"
            label="单位"
            width="80"
            :align="'center'"
          />
          <el-table-column
            prop="indexSpacing"
            label="采样间距"
            :align="'center'"
          />
          <el-table-column
            prop="totalSamples"
            label="采样数"
            :align="'center'"
          />
        </el-table>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">Cancel</el-button>
          <el-button type="primary" @click="addDataSet()">Ok</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, nextTick } from "vue";
import {
  GetetDataset,
  GetChannelList,
  AddDataset,
  DeleteDataset,
} from "@/api/project";
import { ElMessage, ElMessageBox } from "element-plus";
const dialogVisible = ref(false);
const dataSet = ref("");
const wellboreId = ref("");
const projectId = ref("");
const dataSetList = ref([]);
const tableData = ref([]);
const multipleSelection = ref([]);
const multipleTableRef = ref(null);
const loading = ref(false);
const emit = defineEmits(["addChannel"]);
const props = defineProps({
  dataSetTable: {
    type: Array,
    default: [],
  },
});
//新增数据集
function addDataSet() {
  if (multipleSelection.value.length > 0) {
    const selectedDataSet = dataSetList.value.find(
      (d) => d.fileName === dataSet.value
    );
    // let params = {
    //   projectId: projectId.value,
    //   wellboreId: wellboreId.value,
    //   datasetId: selectedDataSet?.id,
    //   datasetName: selectedDataSet?.sourceFileName,
    //   logCurvesStr: multipleSelection.value
    //     .map((item) => item.channelName)
    //     .join(","),
    // };
    const formData = new FormData();
    formData.append("projectId", projectId.value);
    formData.append("wellboreId", wellboreId.value);
    formData.append("datasetId", selectedDataSet?.id);
    formData.append("datasetName", selectedDataSet?.sourceFileName);
    formData.append(
      "logCurvesStr",
      multipleSelection.value.map((item) => item.channelName).join(",")
    );
    AddDataset(formData)
      .then((res) => {
        if (res.success) {
          ElMessage({
            message: res.message,
            type: "success",
          });
          emit("addChannel", projectId.value);
        }
      })
      .catch((error) => {
        console.error("新增数据集出错:", error);
      })
      .finally(() => {
        dialogVisible.value = false;
      });
  }
}
//获取数据集列表
function getDataSet(id) {
  GetetDataset(id).then((res) => {
    if (res.success) {
      dataSetList.value = res.data;
    }
  });
}
//打开弹窗
function openDataSetDialog(id, _projectId) {
  wellboreId.value = id;
  projectId.value = _projectId;
  dialogVisible.value = true;
  dataSet.value = "";
  multipleSelection.value = [];
  tableData.value = [];
  getDataSet(id);
}
//选择数据集
function onDataSetChange() {
  loading.value = true;
  GetChannelList({ wellboreId: wellboreId.value, logName: dataSet.value }).then(
    (res) => {
      if (res.success) {
        tableData.value = res.data;
        //默认勾选已有曲线
        let selectDataSet = dataSetList.value.find(
          (d) => d.fileName === dataSet.value
        );
        const selectDataSetList = props.dataSetTable.find(
          (item) => item.id === selectDataSet.id // 先匹配一级ID
        );
        const selectedNames = new Set(
          selectDataSetList?.children.map((child) => child.name) || []
        );
        nextTick(() => {
          tableData.value.forEach((row) => {
            if (selectedNames.has(row.channelName)) {
              multipleTableRef.value?.toggleRowSelection(row, true);
            }
          });
        });
      }
      loading.value = false;
    }
  );
}
//勾选曲线
function handleSelectionChange(val) {
  multipleSelection.value = val;
}
function removeDataSetPrompt(datasetId, projectId) {
  ElMessageBox({
    title: "提示",
    message: "确定删除数据集？",
    showCancelButton: true,
    confirmButtonText: "OK",
    cancelButtonText: "Cancel",
  })
    .then(() => {
      deleteDataset(datasetId, projectId);
    })
    .catch(() => {});
}
//删除数据集
function deleteDataset(datasetId, projectId) {
  const formData = new FormData();
  formData.append("projectId", projectId);
  formData.append("datasetId", datasetId);
  DeleteDataset(formData)
    .then((res) => {
      if (res.success) {
        ElMessage({
          message: res.message,
          type: "success",
        });
        emit("addChannel", projectId);
      }
    })
    .catch((error) => {
      console.error("删除数据集出错:", error);
    })
    .finally(() => {});
}
defineExpose({
  openDataSetDialog,
  removeDataSetPrompt,
});
</script>
<style lang="less" scoped></style>
