<template>
 <!-- 曲线预览弹框 -->
  <el-dialog
    :title=curveTitle
    draggable
    v-model="curvePreviewDialog"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleCurvePreviewDialogClose">
    <curvePreview />
  </el-dialog>
</template>

<script setup>
import curvePreview from './curvePreview.vue'
import { useCurvePreview } from '@/utils/useCurvePreview.ts'
const { curvePreviewDialog, curveTitle, handleCurvePreviewDialogClose, handleCurvePreviewDialogOpen } = useCurvePreview()

defineExpose({
  handleCurvePreviewDialogOpen
})
</script>

<style scoped>

</style>
