export const curveData ={
  "channelData": {
    "id": 17422370043346988,
    "indexData": [
      "8627.244127296579",
      "8627.252467965593",
      "8627.260808634608",
      "8627.269149303622",
      "8627.277489972636",
      "8627.28583064165",
      "8627.294171310665",
      "8627.302511979678",
      "8627.310852648692",
      "8627.319193317706",
      "8627.32753398672",
      "8627.335874655733",
      "8627.344215324747",
      "8627.35255599376",
      "8627.360896662776",
      "8627.36923733179",
      "8627.377578000804",
      "8627.38591866982",
      "8627.394259338833",
      "8627.402600007847",
      "8627.41094067686",
      "8627.419281345876",
      "8627.42762201489",
      "8627.435962683903",
      "8627.444303352917",
      "8627.45264402193",
      "8627.460984690944",
      "8627.469325359958",
      "8627.477666028974",
      "8627.486006697987",
      "8627.494347367001",
      "8627.502688036015",
      "8627.51102870503",
      "8627.519369374044",
      "8627.527710043058",
      "8627.536050712071",
      "8627.544391381087",
      "8627.5527320501",
      "8627.561072719114",
      "8627.569413388128",
      "8627.577754057143",
      "8627.586094726157",
      "8627.59443539517",
      "8627.602776064185",
      "8627.611116733198",
      "8627.619457402212",
      "8627.627798071226",
      "8627.636138740241",
      "8627.644479409255",
      "8627.652820078269",
      "8627.661160747282",
      "8627.669501416298",
      "8627.677842085312",
      "8627.686182754325",
      "8627.694523423339",
      "8627.702864092355",
      "8627.711204761368",
      "8627.719545430382",
      "8627.727886099396",
      "8627.73622676841",
      "8627.744567437423",
      "8627.752908106437",
      "8627.761248775452",
      "8627.769589444466",
      "8627.77793011348",
      "8627.786270782493",
      "8627.794611451509",
      "8627.802952120523",
      "8627.811292789536",
      "8627.819633458552",
      "8627.827974127566",
      "8627.83631479658",
      "8627.844655465593",
      "8627.852996134608",
      "8627.861336803622",
      "8627.869677472636",
      "8627.87801814165",
      "8627.886358810663",
      "8627.894699479677",
      "8627.90304014869",
      "8627.911380817704",
      "8627.91972148672",
      "8627.928062155734",
      "8627.936402824747",
      "8627.944743493763",
      "8627.953084162777",
      "8627.96142483179",
      "8627.969765500804",
      "8627.97810616982",
      "8627.986446838833",
      "8627.994787507847",
      "8628.00312817686",
      "8628.011468845874",
      "8628.019809514888",
      "8628.028150183902",
      "8628.036490852917",
      "8628.044831521931",
      "8628.053172190945",
      "8628.061512859958",
      "8628.069853528974",
      "8628.078194197988",
      "8628.086534867001",
      "8628.094875536015",
      "8628.10321620503",
      "8628.111556874044",
      "8628.119897543058",
      "8628.128238212072",
      "8628.136578881087",
      "8628.1449195501",
      "8628.153260219115",
      "8628.161600888128",
      "8628.169941557142",
      "8628.178282226156",
      "8628.18662289517",
      "8628.194963564185",
      "8628.203304233199",
      "8628.211644902212",
      "8628.219985571226",
      "8628.228326240242",
      "8628.236666909255",
      "8628.245007578269",
      "8628.253348247283",
      "8628.261688916298",
      "8628.270029585312",
      "8628.278370254326",
      "8628.28671092334",
      "8628.295051592353",
      "8628.303392261367",
      "8628.31173293038",
      "8628.320073599396",
      "8628.32841426841",
      "8628.336754937423",
      "8628.345095606437",
      "8628.353436275453",
      "8628.361776944466",
      "8628.37011761348",
      "8628.378458282496",
      "8628.38679895151",
      "8628.395139620523",
      "8628.403480289537",
      "8628.411820958552",
      "8628.420161627566",
      "8628.42850229658",
      "8628.436842965593",
      "8628.445183634607",
      "8628.45352430362",
      "8628.461864972634",
      "8628.470205641648",
      "8628.478546310664",
      "8628.486886979677",
      "8628.495227648691",
      "8628.503568317707",
      "8628.51190898672",
      "8628.520249655734",
      "8628.528590324748",
      "8628.536930993763",
      "8628.545271662777",
      "8628.55361233179",
      "8628.561953000804",
      "8628.570293669818",
      "8628.578634338832",
      "8628.586975007845",
      "8628.59531567686",
      "8628.603656345875",
      "8628.611997014888",
      "8628.620337683902",
      "8628.628678352918",
      "8628.637019021931",
      "8628.645359690945",
      "8628.653700359959",
      "8628.662041028974",
      "8628.670381697988",
      "8628.678722367002",
      "8628.687063036015",
      "8628.695403705031",
      "8628.703744374045",
      "8628.712085043058",
      "8628.720425712072",
      "8628.728766381086",
      "8628.7371070501",
      "8628.745447719113",
      "8628.753788388129",
      "8628.762129057142",
      "8628.770469726156",
      "8628.77881039517",
      "8628.787151064185",
      "8628.795491733199",
      "8628.803832402213",
      "8628.812173071226",
      "8628.820513740242",
      "8628.828854409256",
      "8628.83719507827",
      "8628.845535747283",
      "8628.853876416297",
      "8628.86221708531",
      "8628.870557754324",
      "8628.87889842334",
      "8628.887239092353",
      "8628.895579761367",
      "8628.90392043038",
      "8628.912261099396",
      "8628.92060176841",
      "8628.928942437424",
      "8628.93728310644",
      "8628.945623775453",
      "8628.953964444467",
      "8628.96230511348",
      "8628.970645782496",
      "8628.97898645151",
      "8628.987327120522",
      "8628.995667789535",
      "8629.00400845855",
      "8629.012349127564",
      "8629.020689796578",
      "8629.029030465592",
      "8629.037371134607",
      "8629.045711803621",
      "8629.054052472635",
      "8629.06239314165",
      "8629.070733810664",
      "8629.079074479678",
      "8629.087415148691",
      "8629.095755817707",
      "8629.10409648672",
      "8629.112437155734",
      "8629.120777824748",
      "8629.129118493762",
      "8629.137459162775",
      "8629.14579983179",
      "8629.154140500803",
      "8629.162481169818",
      "8629.170821838832",
      "8629.179162507846",
      "8629.187503176861",
      "8629.195843845875",
      "8629.204184514889",
      "8629.212525183902",
      "8629.220865852918",
      "8629.229206521932",
      "8629.237547190945",
      "8629.24588785996",
      "8629.254228528975",
      "8629.262569197987",
      "8629.270909867",
      "8629.279250536016",
      "8629.28759120503",
      "8629.295931874043",
      "8629.304272543057",
      "8629.312613212072",
      "8629.320953881086",
      "8629.3292945501",
      "8629.337635219114",
      "8629.345975888129",
      "8629.354316557143",
      "8629.362657226156",
      "8629.37099789517",
      "8629.379338564186",
      "8629.3876792332",
      "8629.396019902213",
      "8629.404360571227",
      "8629.41270124024",
      "8629.421041909254",
      "8629.429382578268",
      "8629.437723247283",
      "8629.446063916297",
      "8629.45440458531",
      "8629.462745254325",
      "8629.47108592334",
      "8629.479426592354",
      "8629.487767261367",
      "8629.496107930381",
      "8629.504448599397",
      "8629.51278926841",
      "8629.521129937424",
      "8629.529470606438",
      "8629.537811275452",
      "8629.546151944465",
      "8629.554492613479",
      "8629.562833282494",
      "8629.571173951508",
      "8629.579514620522",
      "8629.587855289536",
      "8629.596195958551",
      "8629.604536627565",
      "8629.612877296579",
      "8629.621217965594",
      "8629.629558634608",
      "8629.637899303621",
      "8629.646239972635",
      "8629.65458064165",
      "8629.662921310664",
      "8629.671261979678",
      "8629.679602648692",
      "8629.687943317706",
      "8629.69628398672",
      "8629.704624655733",
      "8629.712965324747",
      "8629.721305993762",
      "8629.729646662776",
      "8629.73798733179",
      "8629.746328000805",
      "8629.754668669819",
      "8629.763009338832",
      "8629.771350007846",
      "8629.779690676862",
      "8629.788031345875",
      "8629.79637201489",
      "8629.804712683903",
      "8629.813053352917",
      "8629.82139402193",
      "8629.829734690944",
      "8629.838075359958",
      "8629.846416028973",
      "8629.854756697987",
      "8629.863097367",
      "8629.871438036016",
      "8629.87977870503",
      "8629.888119374044",
      "8629.896460043057",
      "8629.904800712073",
      "8629.913141381086",
      "8629.9214820501",
      "8629.929822719114",
      "8629.93816338813",
      "8629.946504057143",
      "8629.954844726157",
      "8629.96318539517",
      "8629.971526064184",
      "8629.979866733198",
      "8629.988207402212",
      "8629.996548071227",
      "8630.00488874024",
      "8630.013229409255",
      "8630.021570078268",
      "8630.029910747284",
      "8630.038251416297",
      "8630.046592085311",
      "8630.054932754325",
      "8630.06327342334",
      "8630.071614092354",
      "8630.079954761368",
      "8630.088295430382",
      "8630.096636099395",
      "8630.104976768409",
      "8630.113317437423",
      "8630.121658106438",
      "8630.129998775452",
      "8630.138339444466",
      "8630.14668011348",
      "8630.155020782495",
      "8630.163361451509",
      "8630.171702120522",
      "8630.180042789538",
      "8630.188383458551",
      "8630.196724127565",
      "8630.205064796579",
      "8630.213405465594",
      "8630.221746134608",
      "8630.230086803622",
      "8630.238427472636",
      "8630.24676814165",
      "8630.255108810663",
      "8630.263449479677",
      "8630.27179014869",
      "8630.280130817706",
      "8630.28847148672",
      "8630.296812155733",
      "8630.305152824749",
      "8630.313493493763",
      "8630.321834162776",
      "8630.33017483179",
      "8630.338515500805",
      "8630.34685616982",
      "8630.355196838833",
      "8630.363537507847",
      "8630.37187817686",
      "8630.380218845874",
      "8630.388559514888",
      "8630.396900183901",
      "8630.405240852917",
      "8630.41358152193",
      "8630.421922190944",
      "8630.43026285996",
      "8630.438603528974",
      "8630.446944197987",
      "8630.455284867001",
      "8630.463625536016",
      "8630.47196620503",
      "8630.480306874044",
      "8630.488647543058",
      "8630.496988212073",
      "8630.505328881087",
      "8630.5136695501",
      "8630.522010219114",
      "8630.530350888128",
      "8630.538691557142",
      "8630.547032226155",
      "8630.55537289517",
      "8630.563713564185",
      "8630.572054233198",
      "8630.580394902212",
      "8630.588735571228",
      "8630.597076240241",
      "8630.605416909255",
      "8630.613757578269",
      "8630.622098247284",
      "8630.630438916298",
      "8630.638779585312",
      "8630.647120254325",
      "8630.655460923339",
      "8630.663801592353",
      "8630.672142261366",
      "8630.680482930382",
      "8630.688823599396",
      "8630.69716426841",
      "8630.705504937423",
      "8630.713845606439",
      "8630.722186275452",
      "8630.730526944466",
      "8630.73886761348",
      "8630.747208282495",
      "8630.755548951509",
      "8630.763889620523",
      "8630.772230289538",
      "8630.780570958552",
      "8630.788911627566",
      "8630.79725229658",
      "8630.805592965593",
      "8630.813933634607",
      "8630.82227430362",
      "8630.830614972634",
      "8630.83895564165",
      "8630.847296310663",
      "8630.855636979677",
      "8630.863977648693",
      "8630.872318317706",
      "8630.88065898672",
      "8630.888999655734",
      "8630.89734032475",
      "8630.905680993763",
      "8630.914021662777",
      "8630.92236233179",
      "8630.930703000804",
      "8630.939043669818",
      "8630.947384338831",
      "8630.955725007845",
      "8630.96406567686",
      "8630.972406345874",
      "8630.980747014888",
      "8630.989087683904",
      "8630.997428352917",
      "8631.005769021931",
      "8631.014109690945",
      "8631.02245035996",
      "8631.030791028974",
      "8631.039131697988",
      "8631.047472367001",
      "8631.055813036017",
      "8631.06415370503",
      "8631.072494374042",
      "8631.080835043058",
      "8631.089175712072",
      "8631.097516381085",
      "8631.105857050099",
      "8631.114197719115",
      "8631.122538388128",
      "8631.130879057142",
      "8631.139219726156",
      "8631.147560395171",
      "8631.155901064185",
      "8631.164241733199",
      "8631.172582402212",
      "8631.180923071228",
      "8631.189263740242",
      "8631.197604409255",
      "8631.205945078269",
      "8631.214285747283",
      "8631.222626416296",
      "8631.23096708531",
      "8631.239307754326",
      "8631.24764842334",
      "8631.255989092353",
      "8631.264329761367",
      "8631.272670430382",
      "8631.281011099396",
      "8631.28935176841",
      "8631.297692437423",
      "8631.306033106439",
      "8631.314373775453",
      "8631.322714444466",
      "8631.331055113482",
      "8631.339395782496",
      "8631.347736451507",
      "8631.356077120521",
      "8631.364417789537",
      "8631.37275845855",
      "8631.381099127564",
      "8631.389439796578",
      "8631.397780465593",
      "8631.406121134607",
      "8631.41446180362",
      "8631.422802472636",
      "8631.43114314165",
      "8631.439483810664",
      "8631.447824479677",
      "8631.456165148693",
      "8631.464505817707",
      "8631.47284648672",
      "8631.481187155734",
      "8631.489527824748",
      "8631.497868493761",
      "8631.506209162775",
      "8631.514549831789",
      "8631.522890500804",
      "8631.531231169818",
      "8631.539571838832",
      "8631.547912507847",
      "8631.556253176861",
      "8631.564593845875",
      "8631.572934514888",
      "8631.581275183904",
      "8631.589615852918",
      "8631.597956521931",
      "8631.606297190945",
      "8631.614637859959",
      "8631.622978528972",
      "8631.631319197986",
      "8631.639659867",
      "8631.648000536015",
      "8631.65634120503",
      "8631.664681874043",
      "8631.673022543058",
      "8631.681363212072",
      "8631.689703881086",
      "8631.6980445501",
      "8631.706385219115",
      "8631.714725888129",
      "8631.723066557142",
      "8631.731407226156",
      "8631.739747895172",
      "8631.748088564185",
      "8631.756429233199",
      "8631.764769902213",
      "8631.773110571226",
      "8631.78145124024",
      "8631.789791909254",
      "8631.79813257827",
      "8631.806473247283",
      "8631.814813916297",
      "8631.82315458531",
      "8631.831495254326",
      "8631.83983592334",
      "8631.848176592353",
      "8631.856517261367",
      "8631.864857930383",
      "8631.873198599396",
      "8631.88153926841",
      "8631.889879937424",
      "8631.898220606437",
      "8631.906561275451",
      "8631.914901944465",
      "8631.92324261348",
      "8631.931583282494",
      "8631.939923951508",
      "8631.948264620522",
      "8631.956605289537",
      "8631.96494595855",
      "8631.973286627564",
      "8631.98162729658",
      "8631.989967965594",
      "8631.998308634607",
      "8632.006649303621",
      "8632.014989972637",
      "8632.02333064165",
      "8632.031671310664",
      "8632.040011979678",
      "8632.048352648691",
      "8632.056693317705",
      "8632.065033986719",
      "8632.073374655733",
      "8632.081715324748",
      "8632.090055993762",
      "8632.098396662775",
      "8632.106737331791",
      "8632.115078000805",
      "8632.123418669818",
      "8632.131759338832",
      "8632.140100007848",
      "8632.148440676861",
      "8632.156781345875",
      "8632.165122014889",
      "8632.173462683902",
      "8632.181803352916",
      "8632.19014402193",
      "8632.198484690944",
      "8632.20682535996",
      "8632.215166028973",
      "8632.223506697987",
      "8632.231847367002",
      "8632.240188036016",
      "8632.24852870503",
      "8632.256869374043",
      "8632.265210043059",
      "8632.273550712072",
      "8632.281891381086",
      "8632.2902320501",
      "8632.298572719115",
      "8632.306913388129",
      "8632.315254057143",
      "8632.323594726156",
      "8632.33193539517",
      "8632.340276064184",
      "8632.348616733198",
      "8632.356957402213",
      "8632.365298071227",
      "8632.37363874024",
      "8632.381979409254",
      "8632.39032007827",
      "8632.398660747283",
      "8632.407001416297",
      "8632.41534208531",
      "8632.423682754326",
      "8632.43202342334",
      "8632.440364092354",
      "8632.448704761367",
      "8632.457045430381",
      "8632.465386099395",
      "8632.473726768409",
      "8632.482067437424",
      "8632.490408106438",
      "8632.498748775452",
      "8632.507089444465",
      "8632.51543011348",
      "8632.523770782494",
      "8632.532111451508",
      "8632.540452120522",
      "8632.548792789537",
      "8632.557133458551",
      "8632.565474127565",
      "8632.57381479658",
      "8632.582155465594",
      "8632.590496134608",
      "8632.598836803621",
      "8632.607177472635",
      "8632.615518141649",
      "8632.623858810663",
      "8632.632199479676",
      "8632.640540148692",
      "8632.648880817706",
      "8632.65722148672",
      "8632.665562155735",
      "8632.673902824748",
      "8632.682243493762",
      "8632.690584162776",
      "8632.698924831791",
      "8632.707265500805",
      "8632.715606169819",
      "8632.723946838832",
      "8632.732287507846",
      "8632.74062817686",
      "8632.748968845874",
      "8632.757309514887",
      "8632.765650183903",
      "8632.773990852917",
      "8632.78233152193",
      "8632.790672190946",
      "8632.79901285996",
      "8632.807353528973",
      "8632.815694197987",
      "8632.824034867002",
      "8632.832375536016",
      "8632.84071620503",
      "8632.849056874044",
      "8632.857397543059",
      "8632.865738212073",
      "8632.874078881086",
      "8632.8824195501",
      "8632.890760219114",
      "8632.899100888128",
      "8632.907441557141",
      "8632.915782226157",
      "8632.92412289517",
      "8632.932463564184",
      "8632.940804233198",
      "8632.949144902213",
      "8632.957485571227",
      "8632.96582624024",
      "8632.974166909255",
      "8632.98250757827",
      "8632.990848247284",
      "8632.999188916297",
      "8633.007529585311",
      "8633.015870254325",
      "8633.024210923339",
      "8633.032551592352",
      "8633.040892261368",
      "8633.049232930382",
      "8633.057573599395",
      "8633.065914268409",
      "8633.074254937424",
      "8633.082595606438",
      "8633.090936275452",
      "8633.099276944466",
      "8633.107617613481",
      "8633.115958282495",
      "8633.124298951509",
      "8633.132639620524",
      "8633.140980289538",
      "8633.149320958551",
      "8633.157661627563",
      "8633.166002296579",
      "8633.174342965593",
      "8633.182683634606",
      "8633.19102430362",
      "8633.199364972636",
      "8633.20770564165",
      "8633.216046310663",
      "8633.224386979678",
      "8633.232727648692",
      "8633.241068317706",
      "8633.24940898672",
      "8633.257749655735",
      "8633.266090324749",
      "8633.274430993763",
      "8633.282771662776",
      "8633.29111233179",
      "8633.299453000804",
      "8633.307793669817",
      "8633.316134338831",
      "8633.324475007847",
      "8633.33281567686",
      "8633.341156345874",
      "8633.34949701489",
      "8633.357837683903",
      "8633.366178352917",
      "8633.37451902193",
      "8633.382859690946",
      "8633.39120035996",
      "8633.399541028974",
      "8633.407881697987",
      "8633.416222367003",
      "8633.424563036016",
      "8633.432903705028",
      "8633.441244374042",
      "8633.449585043058",
      "8633.457925712071",
      "8633.466266381085",
      "8633.4746070501",
      "8633.482947719114",
      "8633.491288388128",
      "8633.499629057142",
      "8633.507969726157",
      "8633.51631039517",
      "8633.524651064185",
      "8633.532991733198",
      "8633.541332402214",
      "8633.549673071228",
      "8633.558013740241",
      "8633.566354409255",
      "8633.574695078269",
      "8633.583035747282",
      "8633.591376416296",
      "8633.599717085312",
      "8633.608057754325",
      "8633.616398423339",
      "8633.624739092353",
      "8633.633079761368",
      "8633.641420430382",
      "8633.649761099396",
      "8633.65810176841",
      "8633.666442437425",
      "8633.674783106439",
      "8633.683123775452",
      "8633.691464444468",
      "8633.69980511348",
      "8633.708145782493",
      "8633.716486451507",
      "8633.724827120523",
      "8633.733167789536",
      "8633.74150845855",
      "8633.749849127564",
      "8633.75818979658",
      "8633.766530465593",
      "8633.774871134607",
      "8633.78321180362",
      "8633.791552472636",
      "8633.79989314165",
      "8633.808233810663",
      "8633.816574479679",
      "8633.824915148693",
      "8633.833255817706",
      "8633.84159648672",
      "8633.849937155734",
      "8633.858277824747",
      "8633.866618493761",
      "8633.874959162775",
      "8633.88329983179",
      "8633.891640500804",
      "8633.899981169818",
      "8633.908321838833",
      "8633.916662507847",
      "8633.92500317686",
      "8633.933343845874",
      "8633.94168451489",
      "8633.950025183904",
      "8633.958365852917",
      "8633.966706521931",
      "8633.975047190945",
      "8633.983387859958",
      "8633.991728528972",
      "8634.000069197986",
      "8634.008409867001",
      "8634.016750536015",
      "8634.025091205029",
      "8634.033431874044",
      "8634.041772543058",
      "8634.050113212072",
      "8634.058453881085",
      "8634.0667945501",
      "8634.075135219115",
      "8634.083475888128",
      "8634.091816557142",
      "8634.100157226158",
      "8634.108497895171",
      "8634.116838564185",
      "8634.125179233199",
      "8634.133519902212",
      "8634.141860571226",
      "8634.15020124024",
      "8634.158541909255",
      "8634.166882578269",
      "8634.175223247283",
      "8634.183563916296",
      "8634.191904585312",
      "8634.200245254326",
      "8634.20858592334",
      "8634.216926592353",
      "8634.225267261369",
      "8634.233607930382",
      "8634.241948599396",
      "8634.25028926841",
      "8634.258629937423",
      "8634.266970606437",
      "8634.27531127545",
      "8634.283651944466",
      "8634.29199261348",
      "8634.300333282494",
      "8634.308673951507",
      "8634.317014620523",
      "8634.325355289537",
      "8634.33369595855",
      "8634.342036627564",
      "8634.35037729658",
      "8634.358717965593",
      "8634.367058634607",
      "8634.375399303623",
      "8634.383739972636",
      "8634.39208064165",
      "8634.400421310664",
      "8634.408761979677",
      "8634.417102648691",
      "8634.425443317705",
      "8634.433783986718",
      "8634.442124655734",
      "8634.450465324748",
      "8634.458805993761",
      "8634.467146662777",
      "8634.47548733179",
      "8634.483828000804",
      "8634.492168669818",
      "8634.500509338834",
      "8634.508850007847",
      "8634.517190676861",
      "8634.525531345875",
      "8634.533872014888",
      "8634.542212683902",
      "8634.550553352916",
      "8634.55889402193",
      "8634.567234690945",
      "8634.575575359959",
      "8634.583916028972",
      "8634.592256697988",
      "8634.600597367002",
      "8634.608938036015",
      "8634.61727870503",
      "8634.625619374045",
      "8634.633960043058",
      "8634.642300712072",
      "8634.650641381086",
      "8634.658982050101",
      "8634.667322719115",
      "8634.675663388129",
      "8634.684004057142",
      "8634.692344726156",
      "8634.70068539517",
      "8634.709026064183",
      "8634.717366733199",
      "8634.725707402213",
      "8634.734048071226",
      "8634.74238874024",
      "8634.750729409256",
      "8634.75907007827",
      "8634.767410747283",
      "8634.775751416297",
      "8634.784092085312",
      "8634.792432754326",
      "8634.80077342334",
      "8634.809114092353",
      "8634.817454761367",
      "8634.82579543038",
      "8634.834136099395",
      "8634.84247676841",
      "8634.850817437424",
      "8634.859158106437",
      "8634.867498775451",
      "8634.875839444467",
      "8634.88418011348",
      "8634.892520782494",
      "8634.900861451508",
      "8634.909202120523",
      "8634.917542789537",
      "8634.92588345855",
      "8634.934224127566",
      "8634.94256479658",
      "8634.950905465594",
      "8634.959246134607",
      "8634.967586803621",
      "8634.975927472635",
      "8634.984268141648",
      "8634.992608810662",
      "8635.000949479678",
      "8635.009290148691",
      "8635.017630817705",
      "8635.025971486719",
      "8635.034312155734",
      "8635.042652824748",
      "8635.050993493762",
      "8635.059334162777",
      "8635.067674831791",
      "8635.076015500805",
      "8635.084356169818",
      "8635.092696838832",
      "8635.101037507846",
      "8635.10937817686",
      "8635.117718845873",
      "8635.126059514889",
      "8635.134400183902",
      "8635.142740852916",
      "8635.151081521932",
      "8635.159422190945",
      "8635.16776285996",
      "8635.176103528973",
      "8635.184444197988",
      "8635.192784867002",
      "8635.201125536016",
      "8635.20946620503",
      "8635.217806874045",
      "8635.226147543059",
      "8635.234488212072",
      "8635.242828881084",
      "8635.2511695501",
      "8635.259510219114",
      "8635.267850888127",
      "8635.276191557143",
      "8635.284532226156",
      "8635.29287289517",
      "8635.301213564184",
      "8635.3095542332",
      "8635.317894902213",
      "8635.326235571227",
      "8635.33457624024",
      "8635.342916909256",
      "8635.35125757827",
      "8635.359598247283",
      "8635.367938916297",
      "8635.37627958531",
      "8635.384620254325",
      "8635.392960923338",
      "8635.401301592354",
      "8635.409642261367",
      "8635.417982930381",
      "8635.426323599395",
      "8635.43466426841",
      "8635.443004937424",
      "8635.451345606438",
      "8635.459686275452",
      "8635.468026944467",
      "8635.47636761348",
      "8635.484708282494",
      "8635.49304895151",
      "8635.501389620524",
      "8635.509730289537",
      "8635.51807095855",
      "8635.526411627565",
      "8635.534752296579",
      "8635.543092965592",
      "8635.551433634606",
      "8635.559774303621",
      "8635.568114972635",
      "8635.576455641649",
      "8635.584796310663",
      "8635.593136979678",
      "8635.601477648692",
      "8635.609818317706",
      "8635.618158986721",
      "8635.626499655735",
      "8635.634840324748",
      "8635.643180993762",
      "8635.651521662776",
      "8635.65986233179",
      "8635.668203000803",
      "8635.676543669817",
      "8635.684884338832",
      "8635.693225007846",
      "8635.70156567686",
      "8635.709906345875",
      "8635.71824701489",
      "8635.726587683903",
      "8635.734928352917",
      "8635.743269021932",
      "8635.751609690946",
      "8635.75995035996",
      "8635.768291028973",
      "8635.776631697989",
      "8635.784972367",
      "8635.793313036014",
      "8635.801653705028",
      "8635.809994374044",
      "8635.818335043057",
      "8635.826675712071",
      "8635.835016381086",
      "8635.8433570501",
      "8635.851697719114",
      "8635.860038388128",
      "8635.868379057143",
      "8635.876719726157",
      "8635.88506039517",
      "8635.893401064184",
      "8635.9017417332",
      "8635.910082402213",
      "8635.918423071227",
      "8635.92676374024",
      "8635.935104409255",
      "8635.943445078268",
      "8635.951785747282",
      "8635.960126416297",
      "8635.968467085311",
      "8635.976807754325",
      "8635.985148423339",
      "8635.993489092354",
      "8636.001829761368",
      "8636.010170430382",
      "8636.018511099395",
      "8636.02685176841",
      "8636.035192437424",
      "8636.043533106438",
      "8636.051873775452",
      "8636.060214444466",
      "8636.06855511348",
      "8636.076895782493",
      "8636.085236451509",
      "8636.093577120522",
      "8636.101917789536",
      "8636.11025845855",
      "8636.118599127565",
      "8636.126939796579",
      "8636.135280465593",
      "8636.143621134606",
      "8636.151961803622",
      "8636.160302472636",
      "8636.16864314165",
      "8636.176983810665",
      "8636.185324479678",
      "8636.193665148692",
      "8636.202005817706",
      "8636.21034648672",
      "8636.218687155733",
      "8636.227027824747",
      "8636.23536849376",
      "8636.243709162776",
      "8636.25204983179",
      "8636.260390500804",
      "8636.26873116982",
      "8636.277071838833",
      "8636.285412507847",
      "8636.29375317686",
      "8636.302093845876",
      "8636.31043451489",
      "8636.318775183903",
      "8636.327115852917",
      "8636.33545652193",
      "8636.343797190944",
      "8636.352137859958",
      "8636.360478528972",
      "8636.368819197987",
      "8636.377159867001",
      "8636.385500536015",
      "8636.39384120503",
      "8636.402181874044",
      "8636.410522543058",
      "8636.418863212071",
      "8636.427203881087",
      "8636.4355445501",
      "8636.443885219114",
      "8636.452225888128",
      "8636.460566557143",
      "8636.468907226157",
      "8636.47724789517",
      "8636.485588564185",
      "8636.493929233198",
      "8636.502269902212",
      "8636.510610571226",
      "8636.518951240241",
      "8636.527291909255",
      "8636.535632578269",
      "8636.543973247282",
      "8636.552313916298",
      "8636.560654585312",
      "8636.568995254325",
      "8636.577335923339",
      "8636.585676592355",
      "8636.594017261368",
      "8636.602357930382",
      "8636.610698599396",
      "8636.61903926841",
      "8636.627379937423",
      "8636.635720606437",
      "8636.644061275452",
      "8636.652401944466",
      "8636.66074261348",
      "8636.669083282493",
      "8636.677423951509",
      "8636.685764620523",
      "8636.694105289536",
      "8636.70244595855",
      "8636.710786627566",
      "8636.71912729658"
    ],
    "curveData": [
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19928",
      "187.19928",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.1994",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19934",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.19928",
      "187.10468",
      "187.19928",
      "187.10461",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.10461",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.10602",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.10461",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.10461",
      "187.19922",
      "187.10461",
      "187.10602",
      "187.10602",
      "187.19922",
      "187.10596",
      "187.19922",
      "187.10461",
      "187.10461",
      "187.10461",
      "187.10461",
      "187.10461",
      "187.10596",
      "187.10602",
      "187.10461",
      "187.10461",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.10461",
      "187.10461",
      "187.19922",
      "187.19922",
      "187.19922",
      "187.10461",
      "187.10596",
      "187.19922",
      "187.19922",
      "187.10596",
      "187.10455",
      "187.19922",
      "187.10596",
      "187.19916",
      "187.19916",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10461",
      "187.10461",
      "187.10461",
      "187.10596",
      "187.10455",
      "187.10455",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10461",
      "187.10596",
      "187.10596",
      "187.10455",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10455",
      "187.10455",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.19922",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10455",
      "187.10596",
      "187.10596",
      "187.19922",
      "187.10449",
      "187.10455",
      "187.10455",
      "187.10596",
      "187.1059",
      "187.10455",
      "187.10596",
      "187.1059",
      "187.10449",
      "187.10449",
      "187.10449",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.10596",
      "187.10449",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10461",
      "187.10468",
      "187.10602",
      "187.10461",
      "187.10602",
      "187.10596",
      "187.10602",
      "187.10596",
      "187.10596",
      "187.10461",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.1059",
      "187.10455",
      "187.10596",
      "187.10455",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10455",
      "187.10596",
      "187.10455",
      "187.10461",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10461",
      "187.10602",
      "187.10608",
      "187.10602",
      "187.10608",
      "187.10608",
      "187.10608",
      "187.10608",
      "187.10474",
      "187.10474",
      "187.10608",
      "187.10608",
      "187.10474",
      "187.10608",
      "187.10608",
      "187.10608",
      "187.10608",
      "187.10608",
      "187.10602",
      "187.10468",
      "187.10461",
      "187.10602",
      "187.10602",
      "187.10602",
      "187.10596",
      "187.10461",
      "187.10602",
      "187.10596",
      "187.10461",
      "187.10602",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10461",
      "187.10455",
      "187.10596",
      "187.10596",
      "187.10596",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.10596",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10449",
      "187.10449",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.01123",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.01123",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.01123",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.01123",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.01123",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.01123",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.01123",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.10455",
      "187.01123",
      "187.01123",
      "187.10455",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.01123",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.10455",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.01123",
      "187.01123",
      "187.1059",
      "187.01123",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.10455",
      "187.1059",
      "187.01123",
      "187.01123",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.10455",
      "187.01123",
      "187.10455",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.01123",
      "187.10455",
      "187.01123",
      "187.1059",
      "187.01123",
      "187.01123",
      "187.1059",
      "187.01123",
      "187.10455",
      "187.10455",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.01123",
      "187.01123",
      "187.01123",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.01123",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.01123",
      "187.01123",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.10455",
      "187.01123",
      "187.1059",
      "187.01123",
      "187.01123",
      "187.01123",
      "187.01123",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.1059",
      "187.01123",
      "187.1059",
      "187.01123",
      "187.01123"
    ]
  }
}