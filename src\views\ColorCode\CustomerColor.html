<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ColorCode 组件</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .custom-palette-container {
            position: relative;
            width: 100%;
            height: 120px;
            margin: 20px 0;
        }

        .palette-canvas {
            width: 100%;
            height: 50px;
            border: 1px solid #000;
            cursor: crosshair;
        }

        .nodes-container {
            position: absolute;
            top: 0;
            width: 100%;
            height: 50px;
            z-index: 20;
            pointer-events: none;
        }

        .node {
            position: absolute;    
            width: 10px;
            height: 10px;
            background: #fff;
            border: 2px solid #000;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            cursor: grab;
            top: 25px;
            pointer-events: auto;
        }

        .node:active {
            cursor: grabbing;
        }

        .end-node {
            background: #ccc;
            cursor: not-allowed !important;
        }

        .end-node:active {
            cursor: not-allowed !important;
        }

        .context-menu {
            position: fixed;
            background: #fff;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 5px 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }

        .context-menu button {
            display: block;
            width: 100%;
            text-align: left;
            padding: 8px 15px;
            border: none;
            background: none;
            cursor: pointer;
        }

        .context-menu button:hover {
            background: #f0f0f0;
        }

        .color-picker {
            position: absolute;
            z-index: 1000;
            opacity: 0;
            pointer-events: none;
        }

        /* 节点数值标签样式 */
        .node-labels {
            position: absolute;
            top: -25px;
            width: 100%;
            height: 20px;
            pointer-events: none;
            z-index: 10;
        }

        .node-label {
            position: absolute;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            white-space: nowrap;
        }

        /* 刻度样式 */
        .scale-container {
            position: absolute;
            top: 55px;
            width: 100%;
            height: 40px;
            pointer-events: none;
            z-index: 5;
        }

        .scale-line {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: #666;
        }

        .scale-tick {
            position: absolute;
            top: 0;
        }

        .major-tick .tick-mark {
            width: 1px;
            height: 8px;
            background: #333;
            margin-left: -0.5px;
        }

        .minor-tick .tick-mark {
            width: 1px;
            height: 4px;
            background: #999;
            margin-left: -0.5px;
        }

        .tick-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 11px;
            color: #666;
            white-space: nowrap;
        }

        .major-tick {
            transform: translateX(-50%);
        }

        .minor-tick {
            transform: translateX(-50%);
        }

        /* 控制按钮样式 */
        .controls {
            margin: 20px 0;
            text-align: center;
        }

        .btn {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 5px;
        }

        .btn:hover {
            background-color: #0056b3;
        }

        .btn-secondary {
            background-color: #6c757d;
        }

        .btn-secondary:hover {
            background-color: #545b62;
        }

        .hint {
            font-size: 12px;
            color: #666;
            font-style: italic;
            margin-top: 10px;
        }

        .info-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }

        .info-panel h3 {
            margin-top: 0;
            color: #333;
        }

        .info-panel code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ColorCode 组件演示</h1>
        
        <div class="info-panel">
            <h3>使用说明：</h3>
            <ul>
                <li><strong>双击色标条</strong>：在指定位置添加新节点</li>
                <li><strong>拖拽节点</strong>：移动节点位置（端点节点不可移动）</li>
                <li><strong>双击节点</strong>：编辑节点颜色</li>
                <li><strong>右键节点</strong>：删除节点（端点节点不可删除）</li>
            </ul>
        </div>

        <div class="custom-palette-container" id="colorCodeContainer">
            <!-- Canvas will be inserted here -->
        </div>

        <div class="controls">
            <button class="btn" onclick="colorCode.addNodeAtCenter()">➕ 在中间添加节点</button>
            <button class="btn btn-secondary" onclick="exportPalette()">导出完整配置</button>
            <button class="btn btn-secondary" onclick="exportPaletteSimple()">导出简化配置</button>
            <button class="btn btn-secondary" onclick="resetPalette()">重置当前调色板</button>
            <br style="margin: 10px 0;">
            <button class="btn" onclick="createNewPalette()" style="background-color: #28a745;">🎨 新建调色板</button>
            <button class="btn btn-secondary" onclick="switchPalette()">🔄 切换调色板</button>
            <button class="btn btn-secondary" onclick="showPaletteList()">📋 查看调色板列表</button>
            <button class="btn btn-secondary" onclick="exportAllPalettes()">💾 导出所有调色板</button>
            <button class="btn" onclick="deleteCurrentPalette()" style="background-color: #dc3545;">🗑️ 删除当前调色板</button>
            <div class="hint">双击色标条添加节点，左右端点不可移动</div>
        </div>

        <div class="info-panel">
            <h3>当前配置：</h3>
            <pre id="configOutput">加载中...</pre>
        </div>

        <!-- Context Menu -->
        <div class="context-menu" id="contextMenu">
            <button onclick="colorCode.deleteSelectedNode()">删除节点</button>
        </div>

        <!-- Hidden color picker -->
        <input type="color" class="color-picker" id="colorPicker" title="选择颜色" aria-label="颜色选择器" />
    </div>

    <script>
        class ColorCodeComponent {
            constructor(containerId, options = {}) {
                this.container = $('#' + containerId);
                this.canvasWidth = 600;
                this.canvasHeight = 50;
                this.minValue = options.minValue || 0;
                this.maxValue = options.maxValue || 100;
                this.paletteName = options.paletteName || 'Default';
                
                // 初始化调色板
                this.initPalette();
                
                this.draggingIndex = null;
                this.selectedNodeIndex = null;
                this.isPickerJustOpened = false;
                
                this.init();
            }

            initPalette() {
                // 如果有指定的调色板名称，尝试找到对应的调色板
                let paletteId = null;
                const existingPalette = PaletteManager.getAllPalettes().find(p => p.name === this.paletteName);
                
                if (existingPalette) {
                    // 找到已存在的调色板
                    paletteId = existingPalette.id;
                } else {
                    // 创建新的调色板
                    paletteId = PaletteManager.createPalette(this.paletteName);
                }
                
                this.paletteId = paletteId;
                PaletteManager.setCurrentPalette(paletteId);
                
                // 从调色板管理器获取节点数据
                this.nodes = PaletteManager.getPaletteNodes(paletteId);
            }

            init() {
                this.createDOM();
                this.resizeCanvas();
                this.drawPalette();
                this.updateDisplay();
                
                // 在DOM创建后立即绑定事件
                setTimeout(() => {
                    this.bindEvents();
                }, 10);
            }

            createDOM() {
                const html = `
                    <canvas class="palette-canvas" width="${this.canvasWidth}" height="${this.canvasHeight}"></canvas>
                    <div class="nodes-container"></div>
                    <div class="node-labels"></div>
                    <div class="scale-container">
                        <div class="scale-line"></div>
                    </div>
                `;
                this.container.html(html);
                
                this.canvas = this.container.find('canvas')[0];
                this.ctx = this.canvas.getContext('2d');
                this.nodesContainer = this.container.find('.nodes-container');
                this.labelsContainer = this.container.find('.node-labels');
                this.scaleContainer = this.container.find('.scale-container');
            }

            bindEvents() {
                const self = this;
                
                console.log('开始绑定事件，canvas元素:', this.canvas);
                
                // Canvas events - 直接绑定
                if (this.canvas) {
                    console.log('✅ Canvas元素存在，绑定双击事件');
                    
                    // 清除之前的事件绑定
                    $(this.canvas).off('click dblclick');
                    
                    // 绑定双击事件添加节点
                    $(this.canvas).on('dblclick', function(e) {
                        console.log('🖱️ Canvas双击事件触发！', e);
                        e.preventDefault();
                        e.stopPropagation();
                        self.addNode(e);
                    });
                    
                    // 绑定单击事件用于调试
                    $(this.canvas).on('click', function(e) {
                        console.log('🖱️ Canvas单击事件', e.offsetX, e.offsetY);
                    });
                    
                    console.log('✅ Canvas事件绑定完成');
                } else {
                    console.error('❌ Canvas元素不存在！');
                }

                // Window resize
                $(window).on('resize', function() {
                    self.resizeCanvas();
                });

                // Global mouse events for dragging
                $(document).on('mousemove', function(e) {
                    self.handleDrag(e);
                }).on('mouseup', function() {
                    self.stopDragging();
                });

                // Hide context menu on click elsewhere
                $(document).on('click', function(e) {
                    // Hide context menu
                    if (!$('#contextMenu').is(e.target) && $('#contextMenu').has(e.target).length === 0) {
                         $('#contextMenu').hide();
                    }
                   
                    // Hide color picker if click is outside of it
                    const $colorPicker = $('#colorPicker');
                    if ($colorPicker.is(':visible') && !$colorPicker.is(e.target)) {
                        self.isPickerJustOpened = false; // Reset flag on any click
                        setTimeout(() => {
                           if (!self.isPickerJustOpened) {
                               $colorPicker.hide();
                           }
                        }, 50);
                    }
                });

                // Color picker change and blur events
                $('#colorPicker').on('change', function() {
                    self.updateNodeColor();
                });
            }

            resizeCanvas() {
                const containerWidth = this.container.width();
                if (containerWidth && containerWidth !== this.canvasWidth) {
                    this.canvasWidth = containerWidth;
                    this.canvas.width = this.canvasWidth;
                    this.drawPalette();
                    this.updateDisplay();
                    
                    // 隐藏颜色选择器
                    $('#colorPicker').hide();
                }
            }

            drawPalette() {
                // Clear canvas
                this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
                
                // Create gradient
                const gradient = this.ctx.createLinearGradient(0, 0, this.canvasWidth, 0);
                const sortedNodes = [...this.nodes].sort((a, b) => a.position - b.position);
                
                sortedNodes.forEach(node => {
                    gradient.addColorStop(node.position, node.color);
                });

                // Draw palette
                this.ctx.fillStyle = gradient;
                this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
                this.ctx.strokeStyle = '#000';
                this.ctx.strokeRect(0, 0, this.canvasWidth, this.canvasHeight);
            }

            updateDisplay() {
                this.updateNodes();
                this.updateLabels();
                this.updateScale();
                this.updateConfig();
            }

            updateNodes() {
                const self = this;
                this.nodesContainer.empty();
                console.log('this.nodes:', this.nodes);
                console.log('this.nodes type:', typeof this.nodes);
                console.log('Array.isArray(this.nodes):', Array.isArray(this.nodes));
                this.nodes.forEach((node, index) => {
                    const $node = $('<div class="node"></div>');
                    if (node.isEndpoint) {
                        $node.addClass('end-node');
                    }
                    
                    $node.css('left', (node.position * this.canvasWidth) + 'px');
                    
                    // Bind events
                    $node.on('mousedown', function(e) {
                        if (!node.isEndpoint) {
                            self.startDragging(e, index);
                        }
                        e.preventDefault();
                    });
                    
                    $node.on('dblclick', function(e) {
                        self.editNodeColor(index);
                        e.stopPropagation();
                    });
                    
                    $node.on('contextmenu', function(e) {
                        if (!node.isEndpoint) {
                            self.showContextMenu(e, index);
                        }
                        e.preventDefault();
                    });
                    
                    this.nodesContainer.append($node);
                });
            }

            updateLabels() {
                this.labelsContainer.empty();
                
                this.nodes.forEach(node => {
                    const value = this.getNodeValue(node.position);
                    const $label = $('<div class="node-label"></div>');
                    $label.text(value.toFixed(1));
                    $label.css('left', (node.position * this.canvasWidth) + 'px');
                    this.labelsContainer.append($label);
                });
            }

            updateScale() {
                const $scaleContent = this.scaleContainer.find('.scale-line').siblings();
                $scaleContent.remove();
                
                // Major ticks
                const majorTicks = this.getMajorTicks();
                majorTicks.forEach(tick => {
                    const $tick = $(`
                        <div class="scale-tick major-tick" style="left: ${tick.position * this.canvasWidth}px">
                            <div class="tick-mark"></div>
                            <div class="tick-label">${tick.value}</div>
                        </div>
                    `);
                    this.scaleContainer.append($tick);
                });
                
                // Minor ticks
                const minorTicks = this.getMinorTicks();
                minorTicks.forEach(tick => {
                    const $tick = $(`
                        <div class="scale-tick minor-tick" style="left: ${tick.position * this.canvasWidth}px">
                            <div class="tick-mark"></div>
                        </div>
                    `);
                    this.scaleContainer.append($tick);
                });
            }

            getMajorTicks() {
                const ticks = [];
                const range = this.maxValue - this.minValue;
                const tickCount = Math.min(10, Math.max(3, Math.floor(range / 10) + 1));
                
                for (let i = 0; i <= tickCount; i++) {
                    const value = this.minValue + (range * i) / tickCount;
                    const position = i / tickCount;
                    ticks.push({ value: Math.round(value * 10) / 10, position });
                }
                return ticks;
            }

            getMinorTicks() {
                const ticks = [];
                const range = this.maxValue - this.minValue;
                const majorTicks = this.getMajorTicks();
                const majorTickCount = majorTicks.length - 1;
                const minorTicksPerMajor = 4;
                
                for (let i = 0; i < majorTickCount; i++) {
                    for (let j = 1; j < minorTicksPerMajor; j++) {
                        const majorStart = i / majorTickCount;
                        const majorEnd = (i + 1) / majorTickCount;
                        const position = majorStart + (majorEnd - majorStart) * j / minorTicksPerMajor;
                        const value = this.minValue + range * position;
                        ticks.push({ value: Math.round(value * 10) / 10, position });
                    }
                }
                return ticks;
            }

            addNode(event) {
                console.log('addNode 方法被调用'); // 调试信息
                
                const canvas = this.canvas;
                if (!canvas) {
                    console.log('画布不存在');
                    return;
                }
                
                const rect = canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const position = x / this.canvasWidth;
                
                console.log('点击信息:', {
                    clientX: event.clientX,
                    rectLeft: rect.left,
                    x: x,
                    canvasWidth: this.canvasWidth,
                    position: position
                });

                // Check boundaries - don't add too close to endpoints
                if (position <= 0.05 || position >= 0.95) {
                    console.log('位置太靠近端点，跳过添加');
                    return;
                }

                // Estimate color at position
                const color = this.interpolateColor(position);
                const newNode = { 
                    position, 
                    color, 
                    id: 'node-' + Date.now(),
                    isEndpoint: false 
                };
                
                console.log('新节点:', newNode);
                
                this.nodes.push(newNode);
                this.nodes.sort((a, b) => a.position - b.position);
                this.saveToGlobal(); // 保存到全局对象
                this.drawPalette();
                this.updateDisplay();
                
                console.log('节点添加完成，当前节点数:', this.nodes.length);
            }

            addNodeAtCenter() {
                const position = 0.5;
                const color = this.interpolateColor(position);
                const newNode = { 
                    position, 
                    color, 
                    id: 'node-' + Date.now(),
                    isEndpoint: false 
                };
                
                this.nodes.push(newNode);
                this.nodes.sort((a, b) => a.position - b.position);
                this.saveToGlobal(); // 保存到全局对象
                this.drawPalette();
                this.updateDisplay();
            }

            startDragging(event, index) {
                if (this.nodes[index].isEndpoint) return; // 端点不可拖拽
                
                this.draggingIndex = index;
                $('#contextMenu').hide();
            }

            handleDrag(event) {
                if (this.draggingIndex === null) return;
                if (this.nodes[this.draggingIndex].isEndpoint) return; // 端点不可拖拽

                const rect = this.canvas.getBoundingClientRect();
                let x = event.clientX - rect.left;
                x = Math.max(0, Math.min(x, this.canvasWidth));
                let newPosition = x / this.canvasWidth;

                // Get constraints from neighboring nodes
                const sortedNodes = [...this.nodes].sort((a, b) => a.position - b.position);
                const currentNodeInSorted = sortedNodes.findIndex(node => 
                    this.nodes.indexOf(node) === this.draggingIndex
                );

                if (currentNodeInSorted > 0) {
                    newPosition = Math.max(newPosition, sortedNodes[currentNodeInSorted - 1].position + 0.01);
                }
                if (currentNodeInSorted < sortedNodes.length - 1) {
                    newPosition = Math.min(newPosition, sortedNodes[currentNodeInSorted + 1].position - 0.01);
                }

                this.nodes[this.draggingIndex].position = newPosition;
                this.drawPalette();
                this.updateDisplay();
            }

            stopDragging() {
                if (this.draggingIndex !== null) {
                    this.saveToGlobal(); // 拖拽结束时保存到全局对象
                }
                this.draggingIndex = null;
            }

            showContextMenu(event, index) {
                if (this.nodes[index].isEndpoint) return; // 端点不可删除
                
                this.selectedNodeIndex = index;
                const $menu = $('#contextMenu');
                $menu.css({
                    left: event.clientX + 'px',
                    top: event.clientY + 'px'
                }).show();
            }

            deleteSelectedNode() {
                if (this.selectedNodeIndex !== null && !this.nodes[this.selectedNodeIndex].isEndpoint) {
                    this.nodes.splice(this.selectedNodeIndex, 1);
                    this.saveToGlobal(); // 保存到全局对象
                    this.drawPalette();
                    this.updateDisplay();
                }
                $('#contextMenu').hide();
                this.selectedNodeIndex = null;
            }

            editNodeColor(index) {
                this.selectedNodeIndex = index;
                const $colorPicker = $('#colorPicker');
                
                $colorPicker.val(this.nodes[index].color);
                
                const containerOffset = this.container.offset();
                const nodeLeft = this.nodes[index].position * this.canvasWidth;
                
                $colorPicker.css({
                    position: 'fixed',
                    left: (containerOffset.left + nodeLeft) + 'px',
                    top: (containerOffset.top + 70) + 'px',
                    opacity: 1,
                    pointerEvents: 'auto',
                    zIndex: 1000,
                    width: '40px',
                    height: '40px'
                }).show();
                
                this.isPickerJustOpened = true;
                setTimeout(() => {
                    $colorPicker[0].click();
                    // After programmatically clicking, allow subsequent clicks to hide it.
                    setTimeout(() => {
                       this.isPickerJustOpened = false;
                    }, 50);
                }, 50);
            }

            updateNodeColor() {
                if (this.selectedNodeIndex !== null) {
                    const newColor = $('#colorPicker').val();
                    this.nodes[this.selectedNodeIndex].color = newColor;
                    this.saveToGlobal(); // 保存到全局对象
                    this.drawPalette();
                    this.updateDisplay();
                    $('#colorPicker').hide();
                }
            }

            interpolateColor(position) {
                const sortedNodes = [...this.nodes].sort((a, b) => a.position - b.position);
                let leftNode = sortedNodes[0];
                let rightNode = sortedNodes[sortedNodes.length - 1];

                for (let i = 0; i < sortedNodes.length - 1; i++) {
                    if (position >= sortedNodes[i].position && position <= sortedNodes[i + 1].position) {
                        leftNode = sortedNodes[i];
                        rightNode = sortedNodes[i + 1];
                        break;
                    }
                }

                const factor = (position - leftNode.position) / (rightNode.position - leftNode.position);
                const leftRGB = this.hexToRGB(leftNode.color);
                const rightRGB = this.hexToRGB(rightNode.color);
                
                const r = Math.round(leftRGB.r + factor * (rightRGB.r - leftRGB.r));
                const g = Math.round(leftRGB.g + factor * (rightRGB.g - leftRGB.g));
                const b = Math.round(leftRGB.b + factor * (rightRGB.b - leftRGB.b));
                
                return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
            }

            hexToRGB(hex) {
                const r = parseInt(hex.slice(1, 3), 16);
                const g = parseInt(hex.slice(3, 5), 16);
                const b = parseInt(hex.slice(5, 7), 16);
                return { r, g, b };
            }

            getNodeValue(position) {
                return this.minValue + (this.maxValue - this.minValue) * position;
            }

            mapValueToColor(value) {
                const normalized = (value - this.minValue) / (this.maxValue - this.minValue);
                return this.interpolateColor(normalized);
            }

            updateConfig() {
                const config = {
                    currentPalette: this.getCurrentPaletteName(),
                    totalPalettes: this.getAllPalettes().length,
                    minValue: this.minValue,
                    maxValue: this.maxValue,
                    nodes: this.nodes.map((node, index) => ({
                        index,
                        position: node.position,
                        color: node.color,
                        value: this.getNodeValue(node.position).toFixed(1),
                        isEndpoint: node.isEndpoint || false
                    }))
                };
                $('#configOutput').text(JSON.stringify(config, null, 2));
            }

            exportPalette() {
                const paletteData = {
                    version: '1.0',
                    timestamp: new Date().toISOString(),
                    palette: {
                        nodeCount: this.nodes.length,
                        minValue: this.minValue,
                        maxValue: this.maxValue,
                        nodes: this.nodes.map((node, index) => ({
                            index,
                            position: node.position,
                            color: node.color,
                            id: node.id,
                            isEndpoint: node.isEndpoint || false
                        }))
                    }
                };
                return JSON.stringify(paletteData, null, 2);
            }

            // 新增：转换为指定格式的方法
            convertToCustomFormat() {
                // 将节点按位置排序
                const sortedNodes = [...this.nodes].sort((a, b) => a.position - b.position);
                
                // 将十六进制颜色转换为RGB格式
                const rgbColors = sortedNodes.map(node => {
                    const hex = node.color.replace('#', '');
                    const r = parseInt(hex.substring(0, 2), 16);
                    const g = parseInt(hex.substring(2, 4), 16);
                    const b = parseInt(hex.substring(4, 6), 16);
                    return `${r}, ${g}, ${b}`;
                });

                // 生成colors字符串
                const colorsString = rgbColors.join('^');
                
                // 通过统计colors中的颜色数量计算colorLevels
                // 分割字符串并计算数量（包括首尾）
                const colorLevels = colorsString.split('^').length;

                // 构建自定义格式
                const customFormat = {
                    id: "CustomPalette",
                    paletteType: 0,
                    category: "",
                    paletteName: "CustomPalette",
                    colorLevels: colorLevels,
                    colors: colorsString
                };

                return customFormat;
            }

            reset() {
                this.nodes = PaletteManager.getDefaultNodes();
                this.saveToGlobal(); // 保存到调色板管理器
                this.drawPalette();
                this.updateDisplay();
            }

            // 保存数据到调色板管理器
            saveToGlobal() {
                PaletteManager.savePaletteNodes(this.paletteId, this.nodes);
            }

            // 切换到指定调色板
            switchToPalette(paletteName) {
                this.paletteName = paletteName;
                this.initPalette();
                this.drawPalette();
                this.updateDisplay();
            }

            // 获取当前调色板名称
            getCurrentPaletteName() {
                const palette = PaletteManager.getPalette(this.paletteId);
                return palette ? palette.name : 'Unknown';
            }

            // 获取所有调色板列表
            getAllPalettes() {
                return PaletteManager.getAllPalettes();
            }

            // 删除当前调色板
            deleteCurrentPalette() {
                if (this.getAllPalettes().length <= 1) {
                    alert('至少需要保留一个调色板！');
                    return false;
                }
                
                PaletteManager.deletePalette(this.paletteId);
                
                // 切换到第一个可用的调色板
                const remainingPalettes = this.getAllPalettes();
                if (remainingPalettes.length > 0) {
                    this.switchToPalette(remainingPalettes[0].name);
                }
                return true;
            }


        }

        // 全局实例
        let colorCode;
        
        // 全局调色板数据存储，支持多个调色板
        window.colorCodeGlobalData = window.colorCodeGlobalData || {
            palettes: [], // 调色板列表
            currentPaletteId: null // 当前选中的调色板ID
        };
        
        // 调色板管理器
        const PaletteManager = {
            // 获取默认节点
            getDefaultNodes() {
                return [
                    { position: 0, color: '#FF0000', id: 'end-left', isEndpoint: true },
                    { position: 1, color: '#0000FF', id: 'end-right', isEndpoint: true }
                ];
            },
            
            // 创建新调色板
            createPalette(name) {
                const palette = {
                    id: 'palette_' + Date.now(),
                    name: name,
                    nodes: this.getDefaultNodes(),
                    createdAt: new Date().toISOString()
                };
                window.colorCodeGlobalData.palettes.push(palette);
                return palette.id;
            },
            
            // 获取调色板
            getPalette(paletteId) {
                return window.colorCodeGlobalData.palettes.find(p => p.id === paletteId);
            },
            
            // 获取调色板节点
            getPaletteNodes(paletteId) {
                const palette = this.getPalette(paletteId);
                return palette ? palette.nodes : this.getDefaultNodes();
            },
            
            // 保存调色板节点
            savePaletteNodes(paletteId, nodes) {
                const palette = this.getPalette(paletteId);
                if (palette) {
                    palette.nodes = [...nodes]; // 创建副本
                    palette.updatedAt = new Date().toISOString();
                }
            },
            
            // 删除调色板
            deletePalette(paletteId) {
                const index = window.colorCodeGlobalData.palettes.findIndex(p => p.id === paletteId);
                if (index > -1) {
                    window.colorCodeGlobalData.palettes.splice(index, 1);
                }
            },
            
            // 获取所有调色板列表
            getAllPalettes() {
                return window.colorCodeGlobalData.palettes;
            },
            
            // 设置当前调色板
            setCurrentPalette(paletteId) {
                window.colorCodeGlobalData.currentPaletteId = paletteId;
            },
            
            // 获取当前调色板ID
            getCurrentPaletteId() {
                return window.colorCodeGlobalData.currentPaletteId;
            }
        };

        // 页面加载完成后初始化
        $(document).ready(function() {
            colorCode = new ColorCodeComponent('colorCodeContainer', {
                minValue: 0,
                maxValue: 100,
                paletteName: 'Default' // 可以通过外部传入不同的调色板名称
            });
        });

        // 全局函数
        function exportPalette() {
            const currentName = colorCode.getCurrentPaletteName();
            const currentPalette = PaletteManager.getPalette(colorCode.paletteId);
            
            // 获取自定义格式的数据
            const customData = colorCode.convertToCustomFormat();
            
            // 构建完整的导出数据，包含调色板信息和节点数据
            const exportData = {
                // 调色板基本信息
                paletteInfo: {
                    name: currentName,
                    id: currentPalette.id,
                    createdAt: currentPalette.createdAt,
                    updatedAt: currentPalette.updatedAt || currentPalette.createdAt,
                    nodeCount: colorCode.nodes.length
                },
                
                // 详细节点数据
                nodes: colorCode.nodes.map((node, index) => ({
                    index: index,
                    id: node.id,
                    position: node.position,
                    color: node.color,
                    value: colorCode.getNodeValue(node.position).toFixed(1),
                    isEndpoint: node.isEndpoint || false,
                    // 添加RGB值
                    rgb: {
                        r: parseInt(node.color.slice(1, 3), 16),
                        g: parseInt(node.color.slice(3, 5), 16),
                        b: parseInt(node.color.slice(5, 7), 16)
                    }
                })),
                
                // 调色板配置
                config: {
                    minValue: colorCode.minValue,
                    maxValue: colorCode.maxValue,
                    canvasWidth: colorCode.canvasWidth,
                    canvasHeight: colorCode.canvasHeight
                },
                
                // 自定义格式数据（兼容原有格式）
                customFormat: customData,
                
                // 导出元数据
                exportInfo: {
                    version: '2.0',
                    exportedAt: new Date().toISOString(),
                    exportedBy: 'ColorCode组件'
                }
            };
            
            console.log('导出的完整调色板数据:', exportData);
            
            // 创建下载链接
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `colorcode-palette-${currentName}-complete.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 导出简化配置（只包含基本信息和自定义格式）
        function exportPaletteSimple() {
            const currentName = colorCode.getCurrentPaletteName();
            const customData = colorCode.convertToCustomFormat();
            
            // 简化的导出数据
            const exportData = {
                paletteName: currentName,
                customFormat: customData,
                exportedAt: new Date().toISOString()
            };
            
            console.log('导出的简化调色板数据:', exportData);
            
            // 创建下载链接
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `colorcode-palette-${currentName}-simple.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 导出所有调色板
        function exportAllPalettes() {
            const allPalettes = PaletteManager.getAllPalettes();
            
            const exportData = {
                totalPalettes: allPalettes.length,
                palettes: allPalettes.map(palette => ({
                    name: palette.name,
                    id: palette.id,
                    nodeCount: palette.nodes.length,
                    nodes: palette.nodes,
                    createdAt: palette.createdAt,
                    updatedAt: palette.updatedAt
                })),
                exportInfo: {
                    version: '2.0',
                    exportedAt: new Date().toISOString(),
                    exportedBy: 'ColorCode组件 - 全量导出'
                }
            };
            
            console.log('导出的所有调色板数据:', exportData);
            
            // 创建下载链接
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `colorcode-all-palettes-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 初始化ColorCode组件的全局函数（供外部调用）
        function initColorCode(containerId, options = {}) {
            return new ColorCodeComponent(containerId, options);
        }

        function resetPalette() {
            if (confirm('确定要重置调色板吗？')) {
                colorCode.reset();
            }
        }

        function clearStoredData() {
            if (confirm('确定要重置当前调色板吗？')) {
                colorCode.reset();
            }
        }

        // 新增调色板管理函数
        function createNewPalette() {
            const name = prompt('请输入新调色板名称：');
            if (name && name.trim()) {
                const trimmedName = name.trim();
                // 检查名称是否已存在
                const existing = PaletteManager.getAllPalettes().find(p => p.name === trimmedName);
                if (existing) {
                    alert('调色板名称已存在！');
                    return;
                }
                colorCode.switchToPalette(trimmedName);
                updatePaletteSelector();
            }
        }

        function switchPalette() {
            const palettes = colorCode.getAllPalettes();
            if (palettes.length === 0) {
                alert('没有可用的调色板！');
                return;
            }
            
            let options = palettes.map((p, index) => `${index + 1}. ${p.name}`).join('\n');
            const choice = prompt('选择要切换的调色板（输入序号）：\n' + options);
            
            if (choice) {
                const index = parseInt(choice) - 1;
                if (index >= 0 && index < palettes.length) {
                    colorCode.switchToPalette(palettes[index].name);
                    updatePaletteSelector();
                } else {
                    alert('无效的选择！');
                }
            }
        }

        function deleteCurrentPalette() {
            const currentName = colorCode.getCurrentPaletteName();
            if (confirm(`确定要删除调色板 "${currentName}" 吗？`)) {
                if (colorCode.deleteCurrentPalette()) {
                    updatePaletteSelector();
                }
            }
        }

        function showPaletteList() {
            const palettes = colorCode.getAllPalettes();
            const currentName = colorCode.getCurrentPaletteName();
            
            let info = `当前调色板：${currentName}\n\n所有调色板：\n`;
            palettes.forEach((palette, index) => {
                const marker = palette.name === currentName ? '★ ' : '  ';
                info += `${marker}${index + 1}. ${palette.name} (${palette.nodes.length} 个节点)\n`;
            });
            
            alert(info);
        }

        function updatePaletteSelector() {
            // 更新当前配置显示，包含调色板信息
            if (colorCode && typeof colorCode.updateConfig === 'function') {
                colorCode.updateConfig();
            }
        }
    </script>
</body>
</html>
