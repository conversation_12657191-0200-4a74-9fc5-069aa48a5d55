<template>
  <div>
    <el-col style="margin-left: 80%">
      <el-tooltip content="Execute Code" placement="bottom">
        <el-icon
          v-if="!isDisabled"
          class="icon-button"
          @click="runEditorCode(tabId)"
        >
          <VideoPlay />
        </el-icon>
        <el-icon
          v-else-if="tabId == id && isDisabled"
          class="icon-button is-loading"
        >
          <Loading />
        </el-icon>
        <el-icon
          v-else
          class="icon-button"
          style="color: #999; cursor: not-allowed"
        >
          <VideoPlay />
        </el-icon>
      </el-tooltip>
      <el-tooltip content="Save Code" placement="bottom">
        <el-icon v-if="!isSaving" class="icon-button" @click="saveCode(tabId)">
          <DocumentChecked />
        </el-icon>
        <el-icon v-else class="icon-button is-loading">
          <Loading />
        </el-icon>
      </el-tooltip>
    </el-col>

    <div ref="editor" class="monaco-editor"></div>
  </div>
</template>

<script>
import axios from "axios";
import * as monacoautocomplete from "monaco-editor";
import * as monaco from "monaco-editor/esm/vs/editor/editor.api";
import {
  toSocket,
  WebSocketMessageReader,
  WebSocketMessageWriter,
} from "vscode-ws-jsonrpc";
import {
  MonacoLanguageClient,
  CloseAction,
  ErrorAction,
  MonacoServices,
} from "monaco-languageclient";
import "monaco-editor/esm/vs/basic-languages/python/python.contribution";
import * as Debugger from "../js/debughelper.js";
import { toRaw } from "vue";
export default {
  name: "MonacoEditor",
  props: {
    value: {
      type: String,
      default: "",
    },
    language: {
      type: String,
      default: "python",
    },
    tabId: {
      type: String,
      default: "",
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      editor: null,
      wsClient: null, // 添加一个socket实例
      outputResult: "", // 输出结果
      isRunning: false, // 用于控制执行按钮是否可用
      isSaving: false, // 用于控制执行按钮是否可用
      vWebApiUrl:
        window.location.protocol + "//" + window.location.host + "/api",      
    };
  },
  watch: {
    value(newValue) {
      if (this.editor && toRaw(this.editor).getValue() !== newValue) {
        this.editor.setValue(newValue);
      }
    },
  },
  mounted() {
    this.editor = monacoautocomplete.editor.create(this.$refs.editor, {
      value: this.value, // 默认代码
      language: this.language,
      theme: "vs",
    });

    // 添加值变化监听
    this.editor.onDidChangeModelContent(() => {
      this.$emit("input", toRaw(this.editor).getValue());
    });


    // 设置自动补全
    this.setupAutoComplete();

    // 连接WebSocket
    //this.connectLanguageWebSocket();

    //this.initCode();
  },
  methods: {
    getValue() {
      console.log(toRaw(this.editor).getValue());
      return toRaw(this.editor).getValue();
    },
    initCode() {
      // 请求服务器配置信息
      var serverConfiguration = {};
      let configRequest = new XMLHttpRequest();
      //configRequest.open("GET", "http://*************51:8888/config");
      configRequest.open('GET', 'http://127.0.0.1:8888/config');
      configRequest.onload = () => {
        serverConfiguration = JSON.parse(configRequest.response);
        console.log(serverConfiguration);
        Debugger.init(serverConfiguration.wsPort, serverConfiguration.filepath);
      };
      configRequest.send();
    },
    //执行编辑器内容
    async runEditorCode() {
      this.$emit("myEvent", this.tabId);
      let code = toRaw(this.editor).getValue();

      // 调试：检查当前cookies
      console.log('当前cookies:', document.cookie);

      try {
        const response = await axios.post(
          '/api/fastapi/execute',
          {
            user_id: this.tabId,
            project_name:"",
            code: code,
            appId:"",
            projectId:"",
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',              
            },            
            withCredentials: true
          }
        );
        // const response = await axios.post(
        //   "/api/execute",
        //   {
        //     user_id: this.tabId,
        //     project_name: "",
        //     code: code,
        //     appId: "",
        //     projectId: "",             
        //   },
        //   {
        //     headers: {
        //       "Content-Type": "application/json",              
        //       "Accept": "application/json",
        //     },
        //     //withCredentials: true
        //   }
        // );
        // const response = await axios.post(
        //   "http://127.0.0.1:5005/execute",
        //   {
        //     user_id: this.tabId,
        //     project_name: "",
        //     code: code,
        //     appId: "",
        //     projectId: "",             
        //   },
        //   {
        //     headers: {
        //       "Content-Type": "application/json",
        //       Accept: "application/json"
        //     },       
        //     withCredentials: true     
        //   }
        // );
        const data = response.data;
        if (data.error !== null) {
          this.$emit("code-executed", data.error);
        } else {
          this.$emit("code-executed", data.result);
          // 显示图片
          if (data.images && data.images.length > 0) {
            this.$emit("img-executed", data.images);
          }
        }
        this.$emit("resetBtnState");
      } catch (error) {
        let errorMessage = "";
        if (error.response) {
          // 服务器返回了非2xx的响应
          errorMessage = `请求失败: ${error.response.status} - ${JSON.stringify(
            error.response.data
          )}`;
        } else if (error.request) {
          // 请求已发送但无响应
          errorMessage = "服务器无响应";
        } else {
          // 请求配置错误
          errorMessage = `请求配置错误: ${error.message}`;
        }
        this.$emit("code-executed", errorMessage);
        this.$emit("resetBtnState");
      }
    },
    // 连接LSP服务
    connectLanguageWebSocket() {
      MonacoServices.install();
      let url = "ws://*************51:6098";
      console.log("url", url);
      this.wsClient = new WebSocket(url);
      this.wsClient.onopen = () => {
        const socket = toSocket(this.wsClient);
        const reader = new WebSocketMessageReader(socket);
        const writer = new WebSocketMessageWriter(socket);
        const languageClient = this.createLanguageClient({ reader, writer });
        languageClient.start();
        reader.onClose(() => languageClient.stop());
        reader.onError((e) => {
          console.log("lsp error");
          console.log(e);
        });
      };
    },
    //创建语言服务器
    createLanguageClient(transport) {
      return new MonacoLanguageClient({
        name: "monaco-editor",
        clientOptions: {
          documentSelector: [this.language],
          errorHandler: {
            error: () => ErrorAction.Continue,
            closed: () => CloseAction.DoNotRestart,
          },
        },
        connectionProvider: {
          get: () => {
            return Promise.resolve(transport);
          },
        },
      });
    },
    // 自动补全功能
    setupAutoComplete() {
      const pythonKeywords = [
        "and",
        "as",
        "assert",
        "break",
        "class",
        "continue",
        "def",
        "del",
        "elif",
        "else",
        "except",
        "False",
        "finally",
        "for",
        "from",
        "global",
        "if",
        "import",
        "in",
        "is",
        "lambda",
        "None",
        "nonlocal",
        "not",
        "or",
        "pass",
        "raise",
        "return",
        "True",
        "try",
        "while",
        "with",
        "yield",
        "print",
      ];

      const pythonFunctions = [
        "abs",
        "all",
        "any",
        "ascii",
        "bin",
        "bool",
        "breakpoint",
        "bytearray",
        "bytes",
        "callable",
        "chr",
        "classmethod",
        "compile",
        "complex",
        "copyright",
        "credits",
        "delattr",
        "dict",
        "dir",
        "divmod",
        "enumerate",
        "eval",
        "exec",
        "filter",
        "float",
        "format",
        "frozenset",
        "getattr",
        "globals",
        "hasattr",
        "hash",
        "help",
        "hex",
        "id",
        "input",
        "int",
        "isinstance",
        "issubclass",
        "iter",
        "len",
        "list",
        "locals",
        "map",
        "max",
        "memoryview",
        "min",
        "next",
        "object",
        "oct",
        "open",
        "ord",
        "pow",
        "property",
        "quit",
        "range",
        "repr",
        "reversed",
        "round",
        "set",
        "setattr",
        "slice",
        "sorted",
        "staticmethod",
        "str",
        "sum",
        "super",
        "tuple",
        "type",
        "vars",
        "zip",
      ];

      const provider = {
        provideCompletionItems: (model, position) => {
          const suggestions = [];

          // 获取当前编辑器的文本内容
          const word = model.getWordUntilPosition(position);
          const range = {
            startLineNumber: position.lineNumber,
            endLineNumber: position.lineNumber,
            startColumn: word.startColumn,
            endColumn: word.endColumn,
          };

          // 添加关键字补全
          pythonKeywords.forEach((keyword) => {
            suggestions.push({
              label: keyword,
              kind: monaco.languages.CompletionItemKind.Keyword,
              insertText: keyword,
              range: range,
            });
          });

          // 添加函数补全
          pythonFunctions.forEach((func) => {
            suggestions.push({
              label: func,
              kind: monaco.languages.CompletionItemKind.Function,
              insertText: func,
              range: range,
            });
          });

          return {
            suggestions: suggestions,
          };
        },
      };

      monaco.languages.registerCompletionItemProvider(this.language, provider);
    },
    // 处理代码执行结果
    handleCodeExecuted(result) {
      this.outputResult = result;
      //this.$emit('code-executed', this.outputResult);
      this.$emit("code-executed", this.outputResult);
      if (result.includes("completed")) {
        //this.isRunning = false; // 设置为未运行状态
        this.$emit("resetBtnState");
      }
    },
    //保存文件
    saveCode(tabid) {
      this.isSaving = true;
      // 获取当前编辑器中的内容
      const code = toRaw(this.editor).getValue();
      const hashParams = new URLSearchParams(window.location.hash.split('?')[1]);
      const projectId = hashParams.get("id"); // 从 URL 获取 ProjectId
      const appId = hashParams.get("appId"); // 从 URL 获取 appId
      const payload = {
        pyModuleId: this.generateGUID(),
        nodeId: tabid,
        projectId: projectId,
        appId: appId,
        code: code,
      };

      // 发送请求保存代码
      axios
        .post(`${this.vWebApiUrl}/sdk/pythonfreemodule/SaveCode`, payload)
        .then((response) => {
          if (response.data.success) {
            this.$emit("updateTree", response.data.data);
            axios
              .post(`${this.vWebApiUrl}/project/project/SaveTreeForPythonApp`, {
                appId: appId,
                projectId: projectId,
                treeData: JSON.parse(response.data.data),
              })
              .then((response) => {
                //this.$message.success(response.data.message);
                if (response.data.success) {
                  console.log("保存文件后，数据库保存成功");
                }
              })
              .catch((error) => {
                console.error("保存代码时,树状图保存失败:", error);
              });
          }
        })
        .catch((error) => {
          console.error("Error saving code:", error);
        })
        .finally(() => {
          this.$message.success("Save success");
          this.isSaving = false;
        });
    },
    //生成节点的唯一标识符
    generateGUID() {
      return "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx".replace(/[x]/g, () => {
        return ((Math.random() * 16) | 0).toString(16);
      });
    },
    
  },
  created() {
    // const self = this; // 保存组件实例到局部变量
    // // 监听 Debugger 的 outputResult 变化
    // Object.defineProperty(Debugger.debugOption, "outputResult", {
    //   get: function () {
    //     return this._outputResult;
    //   },
    //   set: function (value) {
    //     this._outputResult = value;
    //     self.handleCodeExecuted(value); // 使用保存的组件实例
    //   },
    //   enumerable: true,
    //   configurable: true,
    // });
  },
  // beforeDestroy() {
  //   console.log('beforeDestroy');
  //   if (this.editor) {
  //     this.editor.dispose();
  //   }
  //   if (this.wsClient) {
  //     this.wsClient.close(); // 在组件销毁时关闭WebSocket连接
  //   }
  // },
  beforeUnmount() {
    // this.$emit('tabClose', this.tabId);
    if (this.wsClient) {
      this.wsClient.close(); // 在组件销毁时关闭WebSocket连接
    }
    console.log("beforeUnmount");
  },
};
</script>

<style scoped>
.monaco-editor {
  width: 100%;
  height: 630px;
}

.is-loading {
  animation: rotate 1s linear infinite;
}
.el-icon {
  width: 24px;
  height: 24px;
}
.el-icon svg {
  width: 24px;
  height: 24px;
}

</style>
