import request from '@/js/request'

//曲线预览
export function CurvePreview(data) {
    return request({
        url: '/Preprocess/CurveEdit/Preview?wellId=' + data.wellId + '&datasetId=' + data.datasetId + '&id=' + data.id,
        method: 'get',
    })
}

//开始曲线编辑
export function StartEditCurve(data) {
    return request({
        url: '/Preprocess/CurveEdit/StartEdit',
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        data: data
    })
}

//编辑曲线
export function EditCurve(data) {
    return request({
        url: '/Preprocess/CurveEdit/UpdateLogCurve',
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        data: data
    })
}