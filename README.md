# Vue3 + Veaury + React + RC-Dock

基于 Vue3 的项目，演示如何使用 Veaury 集成 React 组件（RC-Dock），实现类似 VS Code 的布局系统。

## 功能特点

- 🎯 基于 RC-Dock 的灵活布局系统
- 🔄 Vue3 和 React 组件无缝集成
- 📦 开箱即用的 IDE 风格界面
- 🎨 可自定义的主题样式

## 使用方法

### 1. 安装依赖

```bash
npm install
```

### 2. 开发调试

```bash
npm run dev
```

### 3. 生产构建

```bash
npm run build
```

## 布局系统使用说明

### 基础布局

项目默认提供了类似 VS Code 的布局：
- 左侧：资源管理器
- 中间：主编辑区
- 底部：问题/输出/终端面板
- 右侧：大纲视图

### 操作方式

1. **新增标签页**
   - 点击顶部工具栏的"新增标签页"按钮
   - 新标签页默认添加到主编辑区

2. **面板操作**
   - 拖拽：通过标签拖拽调整面板位置
   - 停靠：可以停靠到四个方向
   - 浮动：支持面板浮动
   - 调整大小：拖动分割线调整面板大小

3. **标签页管理**
   - 关闭：点击标签页的关闭按钮
   - 移动：拖拽标签页到其他面板
   - 最大化：双击标签页标题

## 自定义开发

### 1. 添加新的面板

在 `DockLayout.vue` 的 `defaultLayout` 中添加新的面板配置：

```typescript
{
  id: 'your-panel',
  title: '面板标题',
  content: DefaultContent('面板内容'),
  closable: true,
  group: 'main'
}
```

### 2. 自定义标签页内容

1. 创建新的 Vue 组件
2. 使用 `applyVueInReact` 转换为 React 组件
3. 在 `addNewTab` 函数中使用该组件

### 3. 主题定制

在 CSS 中修改以下变量：
```css
:root {
  --dock-color: #CCCCCC;
  --dock-background: #1E1E1E;
  --dock-tab-color: #969696;
  /* 更多变量见 DockLayout.vue */
}
```

## 相关文档

- [Vue 3 文档](https://v3.vuejs.org/)
- [Veaury 文档](https://github.com/devilwjp/veaury)
- [RC-Dock 文档](https://github.com/ticlo/rc-dock)
- [Vite 配置参考](https://vitejs.dev/config/)
