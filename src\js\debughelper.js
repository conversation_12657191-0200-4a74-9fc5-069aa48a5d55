
import * as DAP from './daptool.js';
import { ScopeTreeNode, VariableTreeNode } from './treenode.js';

export const debugOption = {
    filepath: "",
    noDebug: false,
    isDebugging: false,
    threadId: -1,
    stackFrames: [],
    scopes: [],
    watchObjs: [],
    lastBreakpointLine: -1,
    outputResult: "",
    _outputResult: "",
}

var nodesWaitingChildren = [];

var client;

export function init(port, filepath) {
     let url = 'ws://192.168.110.251:' + port;
    //let url = 'ws://127.0.0.1:' + port;
    client = new WebSocket(url);
    client.onopen = e => {
        console.log(e);
        send('web client connected');
    }
    client.onclose = e => console.info('client closed' + e);
    client.onerror = e => {
        console.error('error: ' + e.message);
        console.error(e);
    };
    client.onmessage = e => {
        let msg = e.data;
        console.info('receive message: ' + msg)
        let objs = DAP.read_dap_objs(msg);
        if (objs) {
            processDapObjs(objs);
        }
        else {
            let rex1 = /Save Success/
            let success = msg.match(rex1);
            if (success) {
                send('Launch Debugger');
            }
        }
    }
    debugOption.filepath = filepath;
}

function send(data) {
    console.info('send message: ' + data);
    client.send(data);
}

function processDapObjs(objs) {
    for (const obj of objs) {
        switch (obj.type) {
            case 'event':
                process_dap_event(obj);
                break;
            case 'response':
                process_dap_response(obj);
                break;
            default:
                break;
        }
    }
}

const eventProcesser = {
    'output': process_output_event,
    'initialized': process_initialized_event,
    'thread': process_thread_event,
    'stopped': process_stopped_event,
    'continued': process_continued_event,
    'exited': process_exited_event,
    'terminated': process_terminated_event,
}

function process_dap_event(obj) {
    let func = eventProcesser[obj.event]
    if (func) {
        func(obj)
    }
}

function process_output_event(obj) {
    if (obj.seq > 1 && obj.body.category == 'telemetry') {
        let msg = DAP.init_request();
        send(msg);
    }
    else if (obj.body.category == 'stdout') {
        let msg = obj.body.output;
        console.info('output: ' + msg);        
        debugOption.outputResult += msg;
    }
    else if (obj.body.category == 'stderr') {
        let msg = obj.body.output;
        console.info('output: ' + msg);
        const matchLine = msg.match(/line (\d+)/);
        if (matchLine) {
            const originalLineNumber = parseInt(matchLine[1], 10);
            console.log('原始行号: ' + originalLineNumber);
    
            if (originalLineNumber > 1) {
                const newLineNumber = (originalLineNumber+1)/2;
                console.log('处理后的行号: ' + newLineNumber);
                // 使用正则表达式替换 msg 中的原始行号为新的行号
                msg = msg.replace(/line (\d+)/, `line ${newLineNumber}`);
            } 
        } else {
            console.log("未找到匹配的行号");
            debugOption.outputResult = "Error:\n" + msg;
        }

        const match = msg.match(/line [\s\S]*/);
        if (match) {
            const result = match[0];
            console.log(result);            
            debugOption.outputResult = "Error:\n"+result;
        } else {
            console.log("未找到匹配的内容");
        }

    }
    
}

function process_initialized_event() {
    let msg = DAP.set_breakpoints_request(debugOption.filepath);
    send(msg);
}

function process_thread_event(obj) {
    if (obj.body.reason == 'started') {
        debugOption.threadId = obj.body.threadId;
    }
}

function process_stopped_event(obj) {
    if (obj.body.reason == 'step' || obj.body.reason == 'breakpoint') {
        debugOption.threadId = obj.body.threadId;
        let msg = DAP.stack_trace_request(debugOption.threadId);
        send(msg);
    }
}

function process_continued_event() {
    debugOption.scopes = [];
    debugOption.stackFrames = [];
    debugOption.watchObjs.forEach(x => x.result = null)
}

function process_exited_event() {    
    //document.getElementById('btnRun').disabled = false;
    debugOption.outputResult += "Run completed.\n\n";
}

function process_terminated_event() {
    closeDebugger();
}

const responseProcesser = {
    'initialize': process_initialize_response,
    'setBreakpoints': process_set_breakpoints_response,
    'stackTrace': process_stack_trace_response,
    'scopes': process_scopes_response,
    'variables': process_variables_response,
    'evaluate': process_evaluate_response
}

function process_dap_response(obj) {
    let func = responseProcesser[obj.command]
    if (func) {
        func(obj)
    }
}

function process_initialize_response(obj) {
    if (obj.success) {
        let msg = DAP.launch_request(debugOption.noDebug, debugOption.filepath);
        send(msg);
    }
}

function process_set_breakpoints_response(obj) {
    if (obj.success) {
        let msg = DAP.configure_done_request();
        send(msg);
    }
}

function process_stack_trace_response(obj) {
    if (obj.success) {
        debugOption.stackFrames = obj.body.stackFrames;
        if (debugOption.stackFrames.length > 0) {
            let frame = debugOption.stackFrames[0];
            //let line = frame.line;
            let msg = DAP.scopes_request(frame.id);
            send(msg);
            //main.indicateCurrentLine(line);
        }
        //main.updateStackAsync(debugOption.stackFrames);
    }
}

function process_scopes_response(obj) {
    if (obj.success) {
        let request = DAP.findHistory(obj.request_seq);
        if (request) {
            let frameID = request.arguments.frameId;
            let scopes = obj.body.scopes;
            for (let scope of scopes) {
                let obj = new ScopeTreeNode(scope.name, scope.variablesReference, scope.expensive);
                debugOption.scopes.push(obj);
                if (scope.variablesReference > 0) {
                    nodesWaitingChildren.push(obj);
                    let msg = DAP.variables_request(scope.variablesReference);
                    send(msg);
                }
            }
            getWatchObjs(frameID);
        }
    }
}

function getWatchObjs(frameID) {
    debugOption.watchObjs.forEach(obj => {
        let msg = DAP.evaluate_request(obj.expression, frameID, obj.context);
        send(msg);
    });
}

function process_variables_response(obj) {
    if (obj.success) {
        let request = DAP.findHistory(obj.request_seq);
        if (request) {
            let vr = request.arguments.variablesReference;
            let index = nodesWaitingChildren.findIndex(x => x.variablesReference == vr);
            if (index > -1) {
                let parentNode = nodesWaitingChildren[index];
                console.log(parentNode);
                console.log(nodesWaitingChildren);
                if (parentNode.hasChild) {
                    parentNode.clearChildren();
                }
                let vars = obj.body.variables;
                for (let temp of vars) {
                    let node = new VariableTreeNode(temp.name, temp.value, temp.type, temp.evaluateName, temp.variablesReference);
                    parentNode.appendChild(node);
                }
                nodesWaitingChildren.splice(index, 1);
                //main.updateScopeAsync(debugOption.scopes);
                //main.updateWatchAsync(debugOption.watchObjs);
            }
        }
    }
}

function process_evaluate_response(obj) {
    let request = DAP.findHistory(obj.request_seq);
    if (request) {
        let exp = request.arguments.expression;
        for (let i = 0; i < debugOption.watchObjs.length; i++) {
            let temp = debugOption.watchObjs[i];
            if (temp.expression == exp) {
                if (obj.success) {
                    temp.result = new VariableTreeNode(exp, obj.body.result, obj.body.type, exp, obj.body.variablesReference);
                }
                else {
                    temp.result = new VariableTreeNode(exp, obj.message, "", exp, 0);
                }
                //main.updateWatchAsync(debugOption.watchObjs);
                break;
            }
        }
    }
}

export function run(code) {
    debugOption.noDebug = true;
    debugOption.isDebugging = false;
    sendSaveFileMsg(code);
}

function sendSaveFileMsg(code) {
    send('Save File {' + code + '}');
}

export function closeDebugger() {
    debugOption.isDebugging = false;
    debugOption.scopes = [];
    debugOption.stackFrames = [];
    debugOption.watchObjs = [];
    send('Close Debugger');
    console.info('send msg: Close Debugger');
}