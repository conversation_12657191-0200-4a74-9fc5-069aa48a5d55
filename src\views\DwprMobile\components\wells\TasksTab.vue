<template>
  <div class="tasks-tab">
    <div class="tasks-project">
      <tasks-tab-card />
    </div>
    <div class="tasks-witsml">
      <tasks-witsml-task />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { showToast } from "vant";
import TasksTabCard from "./TasksTabCard.vue";
import TasksWitsmlTask from "./TasksWitsmlTask.vue";

</script>

<style scoped lang="less">
.tasks-tab {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0.3125rem 0.75rem 1.125rem 0.75rem;

  .tasks-project {
    height: 12.6875rem;
    margin-bottom: 0.6875rem;
  }

  .tasks-witsml {
    flex: 1;
    min-height: 0;
  }
}
</style>
