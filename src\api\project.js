import request from '@/js/request'

//创建工程
export function CreateProject(data) {
    return request({
        url: '/Preprocess/Project/CreateProject',
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        data: data
    })
}

//修改工程信息
export function UpdateProject(data) {
    return request({
        url: '/Preprocess/Project/UpdateProject',
        method: 'post',
        params: data
    })
}

//获取工程信息
export function GetProject(id) {
    return request({
        url: '/Preprocess/Project/GetProject?id=' + id,
        method: 'get',

    })
}

//获取数据集
export function GetetDataset(id) {
    return request({
        url: '/log/LogPlotChart/getLogPlotDataFile?wellboreId=' + id,
        method: 'get',
    })
}

//新增数据集
export function AddDataset(data) {
    return request({
        url: '/Preprocess/Project/AddDataset',
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        data: data
    })
}

//删除数据集
export function DeleteDataset(data) {
    return request({
        url: '/log/Dataset/Delete',
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        data: data
    })
}
//clone数据集
export function CloneDataset(data) {
    return request({
        url: '/log/Dataset/Clone',
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        data: data
    })
}
//获取曲线
export function GetChannelList(data) {
    return request({
        url: '/multiWellCompare/Log/GetChannelList?wellboreId=' + data.wellboreId + '&logName=' + data.logName,
    })
}

//获取所有井
export function GetWellListAll(data) {
    return request({
        url: '/oil/oilWell/list',
        method: 'post',
        params: data
    })
}