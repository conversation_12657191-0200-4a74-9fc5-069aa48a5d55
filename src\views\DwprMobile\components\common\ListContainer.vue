<template>
  <van-pull-refresh v-model="refreshing" @refresh="onRefresh" 
  pulling-text="Pull to refresh..."
  loosing-text="Release to refresh..."
  loading-text="Refreshing..."
  success-text="Refresh successful">
    <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="No More"
      loading-text="Loading..."
      @load="onLoad"
    >
      <!-- 使用插槽，允许父组件自定义列表项内容 -->
      <slot :list="list" :loading="loading" :finished="finished"></slot>
    </van-list>
  </van-pull-refresh>
</template>

<script setup lang="ts">
import { ref } from "vue";

// 定义组件 props
interface Props {
  // 列表数据
  data?: any[];
  // 自定义加载函数
  loadMore?: () => Promise<any[]>;
  // 自定义刷新函数
  refresh?: () => Promise<any[]>;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loadMore: undefined,
  refresh: undefined
});

// 定义 emits
const emit = defineEmits<{
  load: [data: any[]];
  refresh: [data: any[]];
}>();

// 响应式数据
const list = ref<any[]>(props.data);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);

// 加载更多数据
const onLoad = async () => {
  try {
    loading.value = true;
    
    if (props.loadMore) {
      // 使用自定义加载函数
      const newData = await props.loadMore();
      if (newData && newData.length > 0) {
        list.value.push(...newData);
        emit('load', newData);
      } else {
        finished.value = true;
      }
    }
  } catch (error) {
    console.error('加载数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const onRefresh = async () => {
  try {
    refreshing.value = true;
    finished.value = false;
    
    if (props.refresh) {
      // 使用自定义刷新函数
      const newData = await props.refresh();
      list.value = newData || [];
      emit('refresh', newData || []);
    } else {
      // 重置列表数据
      list.value = [];
      emit('refresh', []);
    }
    // 重新加载数据
    loading.value = true;
    await onLoad();
  } catch (error) {
    console.error('刷新数据失败:', error);
  } finally {
    refreshing.value = false;
  }
};
</script>

<style scoped lang="scss"></style>
