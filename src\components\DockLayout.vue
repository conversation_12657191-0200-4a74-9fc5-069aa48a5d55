<template>
  <!-- 布局容器 -->
  <div class="layout-box">
    <!-- 工具栏区域 -->
    <!-- <div class="toolbar">
      <button class="add-tab-btn" @click="addNewTab">新增标签页</button>
      <button
        class="add-tab-btn"
        @click="addNewTab('print(123)', 'zz213', 'test')"
      >
        新增标签页test
      </button>
      <button
        class="add-tab-btn"
        @click="addNewTab('print(123)', 'zz2133', 'test2')"bubu
      >
        新增标签页test2
      </button>
      <button
        class="add-tab-btn"
        @click="addNewTab('print(123)', 'zz2134', 'test3')"
      >
        新增标签页test3
      </button>
      <button class="add-tab-btn" @click="addNewTab_bottom">测试按钮</button>
      <button class="add-tab-btn" @click="updateOutput('')">
        修改输出数据
      </button>
    </div> -->
    <TabMenu :new-tabs="newTabs" />
    <!-- Dock布局容器 -->
    <div class="dock-container">
      <ReactDockLayout ref="dockRef" v-bind="dockProps" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { applyReactInVue, applyVueInReact } from "veaury";
import * as React from "react";
import { DockLayout, DockPanel } from "rc-dock";
import type { TabData, LayoutData, DockMode, PanelData } from "rc-dock";

// 引入自定义标签页内容组件
import MonacoEditor from "./MonacoEditor.vue";
import ResourceTree from "./ResourceTree.vue";
import OutputResult from "./OutputResult.vue";
import TabMenu from "./TabMenu.vue";
import Collapse from "./Collapse.vue";
// 引入rc-dock样式
import "rc-dock/dist/rc-dock.css";

// 状态管理
const dockRef = ref(); // Dock布局引用
let tabCounter = 1; // 标签页计数器
const resourceTreeRef = ref(); // ResourceTree 组件引用
const outputResultRef = ref();
const collapseRef = ref();
let isDisabled = ref(false); //控制代码执行按钮状态
let id = ref();
interface TabItem {
  label: string;
  value: string;
  children?: Array<{
    label: string;
    value: string;
    icon?: string;
  }>;
}
const newTabs = ref<TabItem[]>([]);
// 使用Veaury将Vue组件转换为React组件
const MonacoContent = applyVueInReact(MonacoEditor, {
  forwardRef: true, // 启用 ref 转发
  useReactWrapper: true,
});

const ResourceTreeContent = applyVueInReact(ResourceTree, {
  forwardRef: true, // 启用 ref 转发
  useReactWrapper: true,
});

const OutputResultContent = applyVueInReact(OutputResult, {
  forwardRef: true, // 启用 ref 转发
  useReactWrapper: true,
});

const newCollapse = applyVueInReact(Collapse, {
  forwardRef: true, // 启用 ref 转发
  useReactWrapper: true,
});

/**
 * 添加新标签页
 * 创建新的标签页并添加到主布局区域
 */
// DockLayout.vue
/**
 * 添加新标签页
 * 创建新的标签页并添加到主布局区域
 */
function addNewTab(text: string, eidtorId: string, label: string) {
  const layout = dockRef.value?.__veauryReactRef__;
  if (!layout) {
    console.log("Layout instance not found");
    return;
  }
  const currentCounter = tabCounter;
  const newTab: TabData = {
    id: eidtorId,
    //title: label,
    title: React.createElement(
      "div",
      {
        style: { padding: "3px 14px", cursor: "pointer" },
        onClick: (e) => {
          //e.stopPropagation();
          handleTabClick(label);
        },
      },
      label
    ),
    content: React.createElement(MonacoContent, {
      index: currentCounter,
      title: label,
      value: text, // 设置初始值
      tabId: eidtorId,
      isDisabled: isDisabled,
      id: id,
      // 事件处理
      onClick: (count: number) => {},
      onMyEvent: (ids: string) => {
        isDisabled.value = true;
        id.value = ids;
      },
      onResetBtnState: () => {
        isDisabled.value = false;
      },
      onTabClose: (tabId: string) => {
        changeCurrentNode(tabId);
      },
      onUpdateTree: (treeData: any) => {
        resourceTreeRef.value.__veauryVueRef__.updateTreeData(treeData);
      },
      onCodeExecuted: (res: string) => {
        updateOutput(res);
        //updateIMGOutput(res);
      },
      onImgExecuted: (res: string) => {
        updateIMGOutput(res);
      },
    }),

    closable: true,
    group: "main",
    cached: true,
  };
  layout.dockMove(newTab, "main", "middle");
  handleVisibleTabs(label);
  tabCounter++;
}

function addNewTab_bottom() {
  const layout = dockRef.value?.__veauryReactRef__;
  if (!layout) {
    console.log("Layout instance not found");
    return;
  }

  const currentCounter = tabCounter;
  const tabId = `new-tab-${currentCounter}`;

  const newTab: TabData = {
    id: "output",
    title: `输出`,
    content: React.createElement(OutputResultContent, {
      ref: (instance: any): void => {
        // 保存React包装器的引用
        outputResultRef.value = instance;
      },
      index: currentCounter,
    }),
    closable: false,
    group: "bottom",
    cached: true,
  };

  layout.dockMove(newTab, "bottom", "middle");
  tabCounter++;
}

function switchToTab(tabId: string) {
  const layout = dockRef.value?.__veauryReactRef__;
  if (!layout) {
    console.log("Layout instance not found");
    return;
  }

  // 查找指定的标签页
  const tab = layout.find(tabId);
  if (tab) {
    // 将标签页移动到当前激活的位置
    layout.updateTab(tab.id, tab);
    return true;
  }
  return false;
}


function changeCurrentNode(id: string) {
  if (resourceTreeRef.value) {
    resourceTreeRef.value.__veauryVueRef__.setCheckNode(id);
  } else {
    console.error("ResourceTree 组件实例未找到");
  }
}

function updateOutput(text: string) {
  if (outputResultRef.value) {
    outputResultRef.value.__veauryVueRef__.addTextOutput(text);
  } else {
    console.error("OutputResult 组件实例未找到");
  }
}

function updateIMGOutput(imageData: any[]) {
  if (outputResultRef.value) {
    outputResultRef.value.__veauryVueRef__.addImageOutput(imageData);
  } else {
    console.error("OutputResult 组件实例未找到");
  }
}
addNewTab_bottom();

// 布局样式配置
const layoutStyle = computed(() => ({
  width: "100%",
  height: "calc(100vh - 40px)",
  background: "#1E1E1E",
  color: "#CCCCCC",
}));

/**
 * 分组配置
 * 定义不同分组的行为特性
 */
const groupConfig = {
  default: {
    floatable: true, // 是否可浮动
    maximizable: true, // 是否可最大化
    tabLocked: false, // 标签是否锁定
    newWindow: true, // 是否允许新窗口
    preferredFloatWidth: [300, 800],
    preferredFloatHeight: [200, 600],
  },
  main: {
    floatable: true,
    maximizable: true,
    tabLocked: false,
    newWindow: true,
    preferredFloatWidth: [400, 1000],
    preferredFloatHeight: [300, 800],
  },
};

// Dock组件属性配置
const dockProps = computed(() => ({
  defaultLayout: defaultLayout, // 默认布局
  style: layoutStyle.value,
  groups: groupConfig, // 分组配置
  dropMode: "default", // 拖放模式
  mode: "horizontal", // 布局模式
  maximizable: true, // 允许最大化
  floatable: true, // 允许浮动
  newWindow: true, // 允许新窗口
  disableDock: false, // 启用停靠
  draggable: true, // 允许拖拽
  resizable: true, // 允许调整大小
  onLayoutChange: (layout: LayoutData) => {
    // 定义一个递归函数来遍历布局数据
    const findActiveIds = (box: any) => {
      if (box.children) {
        box.children.forEach((child: any) => {
          if (child.activeId && child.group === "main") {
            changeCurrentNode(child.activeId);
          }
          if (child.children) {
            findActiveIds(child);
          }
        });
      }
    };

    // 从 dockbox 开始遍历
    if (layout.dockbox) {
      findActiveIds(layout.dockbox);
    }
  },
}));

// 默认内容组件
const DefaultContent = (text: string) => () =>
  React.createElement("div", null, text);

/**
 * 默认布局配置
 * 定义初始布局结构，包括：
 * - 左侧资源管理器
 * - 中间主编辑区
 * - 底部面板（问题、输出、终端）
 * - 右侧大纲视图
 */
const defaultLayout: LayoutData = {
  dockbox: {
    mode: "horizontal" as DockMode,
    children: [
      {
        mode: "vertical" as DockMode,
        size: 250,
        children: [
          {
            id: "left",
            group: "left",
            tabs: [
              {
                id: "explorer",
                title: "Files",
                content: React.createElement(ResourceTreeContent, {
                  ref: (instance: any): void => {
                    // 保存React包装器的引用
                    resourceTreeRef.value = instance;
                  },
                  onClick: (text: string, id: string, label: string) => {
                    //判断是否是已经打开了的标签页
                    if (!switchToTab(id)) {
                      addNewTab(text, id, label);
                      //addLeftTab();
                    }
                  }
                }),
                closable: false,
                cached: true,
                group: "left",
              },
            ],
          },
        ],
      },
      {
        mode: "vertical" as DockMode,
        size: 1000,
        children: [
          {
            id: "main",
            group: "main",
            tabs: [
              // {
              //   id: 'welcome',
              //   title: '欢迎',
              //   content: DefaultContent('主编辑区域'),
              //   closable: true,
              //   group: 'main'
              // },
              // {
              //   id: 'test',
              //   title: '功能测试',
              //   content: DefaultContent('主编辑区域'),
              //   closable: true,
              //   group: 'main'
              // },
              // {
              //   id: 'multsTab',
              //   title: '多标签页',
              //   content: DefaultContent('主编辑区域'),
              //   closable: true,
              //   group: 'main'
              // }
            ],
            panelLock: {
              panelStyle: "main",
              minWidth: 400,
              minHeight: 200,
            },
          },
          {
            id: "bottom",
            group: "bottom",
            size: 75,
            tabs: [
              {
                id: "output",
                title: "result",
                content: React.createElement(OutputResultContent, {
                  ref: (instance: any): void => {
                    // 保存React包装器的引用
                    outputResultRef.value = instance;
                  },
                }),
                closable: false,
                cached: true,
              },
              // {
              //   id: 'problems',
              //   title: 'pic',
              //   content: React.createElement(IMGoutputResultContent, {
              //     ref: (instance: any): void => {
              //       // 保存React包装器的引用
              //       IMGoutputResultRef.value = instance;
              //     },
              //   }),
              //   closable: false
              // },
              // {
              //   id: 'terminal',
              //   title: '终端',
              //   content: DefaultContent('终端'),
              //   closable: false
              // }
            ],
          },
        ],
      },
      {
        mode: "vertical" as DockMode,
        size: 300,
        children: [
          // {
          //   tabs: [
          //     {
          //       id: 'outline',
          //       title: '大纲',
          //       content: DefaultContent('大纲视图'),
          //       closable: false
          //     }
          //   ]
          // }
        ],
      },
    ],
  },
};
//
const handleTabClick = (tabActiveId: string) => {
  handleVisibleTabs(tabActiveId);
};
//根据visibleTabs的值来判断是否显示对应的顶部标签页
const handleVisibleTabs = (val: string) => {
  if (val === "111.py") {
    newTabs.value = [
      {
        label: "log",
        value: "dynamic1",
        children: [
          {
            label: "Create Project",
            value: "createProject",
            icon: "CirclePlus",
          },
        ],
      },
    ];
  } else {
    newTabs.value = [];
  }
};

const addLeftTab = () => {
  const layout = dockRef.value?.__veauryReactRef__;

  if (!layout) {
    console.log("Layout instance not found");
    return;
  }

  if (layout.find("wellLog")) {
    console.log("标签页已经创建");
    return;
  }
  const newTab: TabData = {
    id: "wellLog",
    title: "well log",
    content: React.createElement(newCollapse, {
      // 事件处理
      onClick: (count: number) => {},
    }),

    closable: false,
    group: "left",
    cached: true,
  };
  layout.dockMove(newTab, "explorer", "after-tab");
};
onMounted(() => {
  //addLeftTab();
});
// 使用Veaury将React组件转换为Vue组件
const ReactDockLayout = applyReactInVue(DockLayout);
</script>

<style lang="less">
/* 布局容器样式 */
.layout-box {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: unset !important;
  background: var(--theme-bg);
  .dock-layout {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: unset !important;
    background: var(--dock-divider-bg) !important;
  }
  .dock-layout > .dock-box {
    height: calc(100% - 105px);
  }

  /* 工具栏样式 */
  .toolbar {
    height: 40px;
    background: #2d2d2d;
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid #1e1e1e;
  }

  /* 获取编辑器内容按钮样式 */
  .get-value-btn {
    background: #0e639c;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    margin-left: 10px;
  }

  .get-value-btn:hover {
    background: #1177bb;
  }

  /* rc-dock主题变量 */
  :root {
    --dock-color: #cccccc;
    --dock-background: #1e1e1e;
    --dock-tab-color: #969696;
    --dock-tab-active-color: #ffffff;
    --dock-tab-background: #2d2d2d;
    --dock-tab-active-background: #1e1e1e;
    --dock-border-color: #252526;

    /* 指示器样式 */
    --dock-indicator-background: #0e639c;
    --dock-indicator-border: #1177bb;
    --dock-indicator-size: 40px;
    --dock-indicator-opacity: 0.9;
  }
  /* 全局隐藏所有面板的最大化按钮 */
  .dock-panel-max-btn {
    display: none !important;
  }
  .dock-divider {
    flex: 0 0 3px;
    background: var(--dock-divider-bg);
    transform: scaleY(1) !important;
  }
  .dock-panel {
    border: 0;
    background: var(--dock-pane-bg);
    border-radius: 10px 10px 0 0;
    // border-left: 0;
    // border-top: 0;
    // border-right: 0;
  }
  .dock-top .dock-bar {
    border-bottom: 0 !important;
    background: transparent;
    padding-left: 0;
  }
  .dock-tab-active {
    background: var(--left-tab-active-bg) !important;
    border-radius: 10px 10px 0 0;
  }
  .dock-top .dock-ink-bar {
    height: 0;
  }
  .dock-tab {
    border-bottom: 0;
    background: transparent;
    font-size: 12px;
  }
  .dock-tab > div {
    padding: 3px 14px;
  }
  .dock {
    //background: var(--dock-bg);
  }
  .dock-pane-cache {
    background: var(--theme-bg-white);
    border-radius: 0 10px 0 0;
  }
  .dock-tab-active {
    transform: none;
    &:hover {
      transform: none;
    }
    .dock-tab-close-btn {
      display: block;
    }
  }
  .dock-tab:hover .dock-tab-close-btn,
  .dock-tab-close-btn:focus {
    color: var(--font-color-white);
  }
  .dock-tab-close-btn {
    background: var(--btn-close-bg);
    border-radius: 50%;
    transform: scale(0.8, 0.8);
    color: #fff;
    right: 10px;
    display: none;
    width: 18px;
    top: 1px;
  }
  .dock-nav {
    height: 24px;
  }
  .dock-nav-list {
    transform: none !important;
    position: static;
    .dock-tab {
      position: static;
    }
  }
  .dock-nav-operations {
    display: none;
  }
  .dock-panel.dock-style-main .dock-tab {
    background: var(--dock-tab-bg);
    border-radius: 10px 10px 0 0;
    & > div {
      padding: 0;
    }
  }
  .dock-panel.dock-style-main {
    .dock-tab-active {
      background: var(--theme-bg-white) !important;
      color: var(--font-color-black);
    }
    .dock-content-animated {
      background: var(--theme-bg-white);
      border-radius: 10px 10px 0 0;
    }
  }
  .dock-style-left .dock-bar {
    padding-left: 8px;
  }
  .dock-content .dock-tabpane
{
  background: white !important;
}
}
</style>
