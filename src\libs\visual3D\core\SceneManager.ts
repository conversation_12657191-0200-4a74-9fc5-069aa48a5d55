import * as THREE from 'three';

type DisposeCallback = () => void;
type SceneEventMap = {
  'dispose': { type: 'dispose' };
  'object-added': { type: 'object-added'; object: THREE.Object3D };
  'object-removed': { type: 'object-removed'; object: THREE.Object3D };
};

export class SceneManager {
  public readonly scene: THREE.Scene;
  private readonly _disposeCallbacks = new Set<DisposeCallback>();
  private readonly _resourceCache = new Map<string, THREE.Object3D>();
  private readonly _eventDispatcher = new THREE.EventDispatcher<SceneEventMap>();

  constructor() {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0xeeeeee);
  }

  // region 核心功能
  addObject(object: THREE.Object3D, trackResources = true): this {
    this.scene.add(object);
    
    if (trackResources) {
      this._trackResource(object);
    }

    this._eventDispatcher.dispatchEvent({
      type: 'object-added',
      object
    });
    return this;
  }

  removeObject(object: THREE.Object3D, dispose = true): this {
    if (dispose) {
      this._disposeObjectTree(object);
    }

    this.scene.remove(object);
    this._resourceCache.delete(object.uuid);

    this._eventDispatcher.dispatchEvent({
      type: 'object-removed',
      object
    });
    return this;
  }

  dispose(): void {
    // 释放所有注册资源
    this._disposeCallbacks.forEach(cb => cb());
    this._disposeCallbacks.clear();

    // 深度释放场景资源
    this.scene.traverse(obj => this._disposeSingleObject(obj));
    this.scene.clear();

    this._eventDispatcher.dispatchEvent({ type: 'dispose' });
  }
  // endregion

  // region 资源追踪
  trackDisposable<T extends { dispose: () => void }>(resource: T): T {
    this._disposeCallbacks.add(() => resource.dispose());
    return resource;
  }

  private _trackResource(object: THREE.Object3D): void {
    this._resourceCache.set(object.uuid, object);
    object.traverse(child => {
      this._resourceCache.set(child.uuid, child);
    });
  }
  // endregion

  // region 事件系统
  on<T extends keyof SceneEventMap>(
    type: T,
    listener: (event: SceneEventMap[T]) => void
  ): this {
    this._eventDispatcher.addEventListener(type, listener);
    return this;
  }

  off<T extends keyof SceneEventMap>(
    type: T,
    listener: (event: SceneEventMap[T]) => void
  ): this {
    this._eventDispatcher.removeEventListener(type, listener);
    return this;
  }
  // endregion

  // region 私有方法
  private _disposeObjectTree(object: THREE.Object3D): void {
    object.traverse(child => {
      this._disposeSingleObject(child);
      this._resourceCache.delete(child.uuid);
    });
  }

  private _disposeSingleObject(object: THREE.Object3D): void {
    // 释放几何体
    if (object instanceof THREE.Mesh && object.geometry) {
      object.geometry.dispose();
    }

    // 释放材质
    if (object instanceof THREE.Mesh && object.material) {
      if (Array.isArray(object.material)) {
        object.material.forEach(m => m.dispose());
      } else {
        object.material.dispose();
      }
    }

    // 释放灯光
    if (object instanceof THREE.Light && object.dispose) {
      object.dispose();
    }

    // 释放其他资源
    if ('dispose' in object && typeof object.dispose === 'function') {
      (object as any).dispose();
    }

    // 清理用户数据
    if (object.userData) {
      Object.values(object.userData).forEach(value => {
        if (value instanceof THREE.Texture) value.dispose();
        if (value instanceof THREE.BufferGeometry) value.dispose();
      });
      object.userData = {};
    }
  }
  // endregion
}