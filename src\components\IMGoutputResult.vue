<template>
  <!-- 图片结果 -->
  <div v-if="resultType === 'image' && images.length > 0" class="result-images">
    <div v-for="(image, index) in images" :key="index" class="image-container">
      <img 
        :src="`data:image/${image.format};base64,${image.base64_data}`" 
        :alt="image.filename"
        class="result-image"
      />
      <div class="image-filename">{{ image.filename }}</div>
    </div>
  </div>

  <!-- 文本结果 -->
  <div v-if="resultType === 'text'" class="result-text">
    {{ resultContent }}
  </div>
</template>

<script setup>
import { ref, defineExpose } from 'vue';

// 数据状态
const showResult = ref(true);
const resultType = ref('text'); // 'image' 或 'text'
const resultContent = ref('');
const images = ref([]);

// 更新图片输出
const updateIMGOutput = (imageData) => {
  if (Array.isArray(imageData) && imageData.length > 0) {
    images.value = imageData;
    resultType.value = 'image';
  } else {
    console.warn('updateIMGOutput: 传入的数据不是有效的图片数组');
  }
};

// 更新文本输出
const updateOutput = (output) => {
  resultContent.value = output;
  resultType.value = 'text';
  images.value = []; // 清空图片数据
};

// 暴露方法给外部
defineExpose({
  updateOutput,
  updateIMGOutput
});
</script>

<style scoped>
.result-images {
  padding: 15px;
  max-height: 95%;
  overflow-y: auto;
}

.image-container {
  margin-bottom: 20px;
  text-align: center;
}

.result-image {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto 10px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-filename {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.result-text {
  padding: 15px;
  white-space: pre-wrap;
  line-height: 1.6;
  max-height: 95%;
  overflow-y: auto;
}
</style>
