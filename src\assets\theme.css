:root {
    --theme-bg-white: #fff;/* 通用白色背景 */
    --theme-bg: #D2E8FC;/* 整体背景色 */
    --font-color-white: #fff;/* 通用字体颜色 */
    --font-color-black: #3d3d3d;/* 通用字体颜色 */
    --header-tab-bg: #F7FBFF;/* 顶部菜单栏背景色 */
    --header-font-color: #0D76BA;/* 顶部菜单栏字体颜色 */
    --header-tab-active-bg: linear-gradient(180deg, #1f94df 0%, #cfe9ff 89%);/* 顶部菜单栏选中背景色 */
    --header-tab-content-bg: linear-gradient(0deg, #FFFFFF -52%, #CEE8FF 100%);/* 顶部菜单栏内容区域背景色 */
    --dock-pane-cache-bg: linear-gradient(180deg, #f5fafe 0%, #f1f9ff 98%);/* dock面板内容区背景色 */
    --dock-pane-bg: #D2E8FC;/* dock面板背景色 */
    --left-tab-active-bg: linear-gradient(180deg, #a6d5ff 0%, #f1f7fd 100%);/* 左侧tab选中背景色 */
    --dock-divider-bg: #D2E8FC;/* dock容器分割线颜色 */
    --btn-close-bg:#81b6e7;/* 关闭按钮背景色 */
    --divider-bg:#a2cdf3;/* 分隔线颜色 */

  }
.dark{
    /* --header-tab-bg: #000; */
}