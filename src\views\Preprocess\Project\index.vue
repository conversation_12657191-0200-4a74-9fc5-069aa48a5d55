<template>
  <div class="project" v-loading.fullscreen.lock="fullscreenLoading">
    <ResourceTree
      ref="resourceTreeRef"
      @triggerAddTab="addDockTab"
      @triggerRemoveTab="removeDockTab"
      :treeData="treeData"
      :isLoading="isTreeLoading"
      @triggerMenuAction="triggerHandleMenuAction"
    />
    <createProject
      ref="createProjectRef"
      @project-created="handleProjectCreated"
    />
    <addDataSet
      ref="addDataSetRef"
      @addChannel="loadProjectData"
      :dataSetTable="dataSetTable"
    />
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import ResourceTree from "./ResourceTree.vue";
import createProject from "./createProject.vue";
import addDataSet from "./addDataSet.vue";
import {
  GetProject,
  CreateProject,
  GetWellListAll,
  AddDataset,
  DeleteDataset,
  CloneDataset,
} from "@/api/project";
import { useEditCurveStore } from "@/stores/editCurveStore";
import { useProjectStore } from "@/stores/project";
const store = useEditCurveStore();
const projectStore = useProjectStore();
const resourceTreeRef = ref();
const createProjectRef = ref();
const addDataSetRef = ref();
const treeData = ref([]);
const dataSetTable = ref([]);
const fullscreenLoading = ref(false);
const isTreeLoading = ref(false);
const route = useRoute();
const router = useRouter();
defineExpose({
  resourceTreeRef,
});
const emit = defineEmits(["click", "tabClose"]);
function showCreateProjectPrompt() {
  ElMessageBox({
    title: "提示",
    message: "未找到工程，是不是创建新工程？",
    showCancelButton: true,
    confirmButtonText: "OK",
    cancelButtonText: "Cancel",
    closeOnClickModal: false,
  })
    .then(() => {
      createProjectRef.value.openDialog();
    })
    .catch(() => {});
}
//分发右键菜单工程相关事件
function triggerHandleMenuAction({ actionType, nodeData, projectId }) {
  const actionMap = {
    cloneDataset: () => handleCloneDataset(nodeData, projectId),
    deleteDataset: () => handleDeleteDataSet(nodeData, projectId),
    editProject: () => handleEditProject(nodeData, projectId),
    refresh: () => loadProjectData(projectId),
    editCurve: () => addDockTab(" ", nodeData, nodeData.data.name),
  };
  actionMap[actionType]?.();
}
function handleEditProject(node, id) {
  createProjectRef.value.openDialog(node, id, 1);
}
function loadProjectData(id) {
  isTreeLoading.value = true;
  GetProject(id).then((res) => {
    if (res.success) {
      const projectData = res.data;
      store.setWellId(projectData.projectBindInfo.wellId);
      const newData = {
        id: projectData.id,
        name: projectData.name,
        children: projectData.projectBindInfo.wellbores.map((wellbore) => {
          return {
            ...wellbore,
          };
        }),
      };
      treeData.value = [newData];
      projectStore.updateTreeData([newData]);
      // 使用Vue Router更新URL参数
      const query = { ...route.query };
      query.id = projectData.id;
      router.push({ query });
      fullscreenLoading.value = false;
      isTreeLoading.value = false;
    } else {
      ElMessage({
        message: res.message,
        type: "error",
      });
      isTreeLoading.value = false;
    }
  });
}
function handleProjectCreated(projectId) {
  loadProjectData(projectId); // 调用加载方法
}
function addDockTab(val, nodeData, label) {
  store.setCurveId(nodeData.data.id);
  store.setDatasetId(nodeData.parent.data.id);
  let info = {
    wellId: store.wellId,
    datasetId: store.datasetId,
    id: store.curveId,
  };
  if (val !== "1") {
    resourceTreeRef.value.setCheckedKeys(nodeData.data.id, true);
  }
  emit("click", val, info, label);
}
function removeDockTab(tabId) {
  emit("tabClose", tabId);
}
function handleCloneDataset(nodeData, projectId) {
  // dataSetTable.value = wellbore.children;
  // addDataSetRef.value.openDataSetDialog(wellbore.id, projectId);
  const formData = new FormData();
  formData.append("wellboreId", nodeData.parent.data.id);
  formData.append("id", nodeData.data.id);
  formData.append("type", nodeData.data.type);
  CloneDataset(formData).then((res) => {
    if (res.success) {
      ElMessage({
        message: "克隆成功",
        type: "success",
      });
      loadProjectData(projectId);
    }
  });
}
function handleDeleteDataSet(nodeData, projectId) {
  // addDataSetRef.value.removeDataSetPrompt(datasetId, projectId);
  ElMessageBox({
    title: "提示",
    message: "确定删除数据集？",
    showCancelButton: true,
    confirmButtonText: "OK",
    cancelButtonText: "Cancel",
    closeOnClickModal: false,
  })
    .then(() => {
      deleteDataSet(
        nodeData.data.id,
        nodeData.parent.data.id,
        nodeData.data.type,
        projectId
      );
    })
    .catch(() => {});
}
//删除数据集
function deleteDataSet(datasetId, wellboreId, type, projectId) {
  const formData = new FormData();
  formData.append("wellboreId", wellboreId);
  formData.append("id", datasetId);
  formData.append("type", type);
  DeleteDataset(formData)
    .then((res) => {
      if (res.success) {
        ElMessage({
          message: "删除成功",
          type: "success",
        });
        //emit("addChannel", projectId);
        loadProjectData(projectId);
      }
    })
    .catch((error) => {
      console.error("删除数据集出错:", error);
    })
    .finally(() => {});
}
//生成时间戳
function generateTimestamp() {
  const now = new Date();

  // 获取各时间组成部分
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, "0");
  const day = now.getDate().toString().padStart(2, "0");
  const hours = now.getHours().toString().padStart(2, "0");
  const minutes = now.getMinutes().toString().padStart(2, "0");
  const seconds = now.getSeconds().toString().padStart(2, "0");
  const milliseconds = now.getMilliseconds().toString().padStart(3, "0");

  // 拼接成完整时间戳
  return `${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}`;
}
async function createProject_wellId(wellId) {
  let list = await GetWellListAll();
  let wellList = list.data.find((d) => d.id === wellId);
  let wellName = wellList.wellName;
  let projectName = wellName + "_工程_" + generateTimestamp();
  const urlParams = new URLSearchParams(window.location.search);
  const formData = new FormData();
  formData.append("name", projectName);
  formData.append("wellId", wellId);
  formData.append("remark", projectName);
  formData.append("appId", urlParams.get("appId"));
  CreateProject(formData).then((res) => {
    if (res.success) {
      const projectId = res.data;
      loadProjectData(projectId);
    }
  });
}
async function createProject_channelId(data) {
  let list = await GetWellListAll();
  let wellList = list.data.find((d) => d.id === data.wellId);
  let wellName = wellList.wellName;
  let projectName = wellName + "_工程_" + generateTimestamp();
  const urlParams = new URLSearchParams(window.location.search);
  const formData = new FormData();
  formData.append("name", projectName);
  formData.append("wellId", data.wellId);
  formData.append("remark", projectName);
  formData.append("appId", urlParams.get("appId"));
  CreateProject(formData).then((res) => {
    if (res.success) {
      const projectId = res.data;
      let params = {
        projectId: projectId,
        datasetName: data.datasetName,
        datasetId: data.datasetId,
        wellboreId: data.wellboreId,
        logCurvesStr: [data.channelName],
      };
      AddDataset(params).then((res) => {
        if (res.success) {
          loadProjectData(projectId);
        }
      });
    }
  });
}
const handleChildMessage = (event) => {
  if (event.origin !== window.location.origin) return;
  if (event.data.type === "projectInfo") {
    createProject_channelId(event.data.data);
  }
};
onMounted(() => {
  // 获取参数
  let id = route.query.id; //工程id
  let wellId = route.query.wellId; //井id
  let channelId = route.query.channelId; //曲线id
  // let obj = {
  //   channelName: "GR",
  //   datasetName: "LWD",
  //   datasetId: "ab3cd8cc5bf646bcaaabe6c85ad2875c",
  //   wellboreId: "5dcc5f3990564fa097abd47bb70359da", //井眼id
  //   wellId: "8fb8cdf4b4be4ca69b92221f8027e082", //井id
  // };

  if (!id && !wellId && !channelId) {
    showCreateProjectPrompt();
  } else {
    if (id) {
      loadProjectData(id);
    } else if (wellId) {
      fullscreenLoading.value = true;
      createProject_wellId(wellId);
    } else if (channelId) {
      fullscreenLoading.value = true;
      window.addEventListener("message", handleChildMessage);
    }
  }
});
onUnmounted(() => {
  window.removeEventListener("message", handleChildMessage);
});
</script>
<style lang="less" scoped>
.project {
  height: 100%;
}
</style>
