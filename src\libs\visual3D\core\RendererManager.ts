import * as THREE from 'three';

type RendererConfig = {
  antialias?: boolean;
  alpha?: boolean;
  shadowMap?: boolean;
};

export class RendererManager {
  public renderer: THREE.WebGLRenderer;
  private animationFrameId: number | null = null;

  constructor(private canvas: HTMLCanvasElement, config: RendererConfig) {
    this.renderer = new THREE.WebGLRenderer({
      canvas,
      antialias: config.antialias ?? true,
      alpha: config.alpha ?? true
    });

    this.renderer.shadowMap.enabled = config.shadowMap ?? true;
    this.updateSize();
  }

  startRenderLoop(
    scene: THREE.Scene,
    camera: THREE.Camera,
    beforeRender?: () => void
  ): void {
    const render = () => {
      beforeRender?.();
      this.renderer.render(scene, camera);
      this.animationFrameId = requestAnimationFrame(render);
    };
    render();
  }

  stopRenderLoop(): void {
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
    }
  }

  updateSize(): void {
    const width = this.canvas.clientWidth;
    const height = this.canvas.clientHeight;
    if (this.canvas.width !== width || this.canvas.height !== height) {
      this.renderer.setSize(width, height, false);
      // 获取你屏幕对应的设备像素比.devicePixelRatio告诉threejs,以免渲染模糊问题
      this.renderer.setPixelRatio(window.devicePixelRatio);
    }
  }

  dispose(): void {
    this.stopRenderLoop();
    this.renderer.dispose();
  }
}