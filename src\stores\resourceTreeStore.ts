import { defineStore } from 'pinia'
export const useResourceTreeStore = defineStore('resourceTree', {
    state: () => ({
      treeInstance: null
    }),
    actions: {
      registerTreeInstance(instance: any) {
        this.treeInstance = instance
      },
      callTreeAction(action: any) {
        if (this.treeInstance?.handleAction) {
          this.treeInstance.handleAction(action)
        }
      }
    }
  })
