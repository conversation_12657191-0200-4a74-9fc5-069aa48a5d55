import * as THREE from 'three';

type TextureConfig = {
  flipY?: boolean;
  anisotropy?: number;
  wrapS?: THREE.Wrapping;
  wrapT?: THREE.Wrapping;
};

export class TextureManager {
  private static instance: TextureManager;
  private loader: THREE.TextureLoader;
  private cache = new Map<string, THREE.Texture>();
  private loadingManager = new THREE.LoadingManager();

  private constructor() {
    this.loader = new THREE.TextureLoader(this.loadingManager);
  }

  static getInstance(): TextureManager {
    if (!TextureManager.instance) {
      TextureManager.instance = new TextureManager();
    }
    return TextureManager.instance;
  }

  async load(
    url: string,
    config?: TextureConfig
  ): Promise<THREE.Texture> {
    if (this.cache.has(url)) {
      return this.cache.get(url)!.clone();
    }

    return new Promise((resolve, reject) => {
      this.loader.load(
        url,
        texture => {
          this.applyConfig(texture, config);
          this.cache.set(url, texture);
          resolve(texture.clone());
        },
        undefined,
        err => reject(err)
      );
    });
  }

  private applyConfig(texture: THREE.Texture, config?: TextureConfig) {
    texture.flipY = config?.flipY ?? false;
    texture.anisotropy = config?.anisotropy ?? 1;
    texture.wrapS = config?.wrapS ?? THREE.ClampToEdgeWrapping;
    texture.wrapT = config?.wrapT ?? THREE.ClampToEdgeWrapping;
    texture.needsUpdate = true;
  }

  dispose(): void {
    this.cache.forEach(texture => texture.dispose());
    this.cache.clear();
  }
}