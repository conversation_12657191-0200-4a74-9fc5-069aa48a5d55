window.onbeforeunload = function (e) {
    var e = window.event || e;
    e.returnValue = "确定离开当前页面吗？";
};
/**
 * ColorCode 组件 - 使用jQuery实现的色标组件
 * 特点：最左和最右侧节点不可移动
 */
class ColorCode {
    constructor(selector, options = {}) {
        this.container = $(selector);
        if (this.container.length === 0) {
            throw new Error('容器元素未找到');
        }

        // 配置选项
        this.options = {
            width: options.width || 400,
            height: options.height || 40,
            minValue: options.minValue || 0,
            maxValue: options.maxValue || 100,
            initialNodes: options.initialNodes || [
                { position: 0, color: '#FF0000', isEndpoint: true },   // 左端点（红色）
                { position: 1, color: '#0000FF', isEndpoint: true }    // 右端点（蓝色）
            ]
        };

        // 状态变量
        this.nodes = [...this.options.initialNodes];
        this.dragging = null;
        this.contextMenuTarget = null;

        // 初始化组件
        this.init();
    }

    init() {
        this.createElements();
        this.bindEvents();
        this.render();
    }

    createElements() {
        // 创建基础HTML结构
        const html = `
            <div class="colorcode-container" style="position: relative; width: ${this.options.width}px; height: ${this.options.height + 60}px;">
                <!-- 色标画布 -->
                <canvas class="colorcode-canvas" 
                        width="${this.options.width}" 
                        height="${this.options.height}"
                        style="border: 1px solid #000; cursor: crosshair; display: block;">
                </canvas>
                
                <!-- 节点容器 -->
                <div class="colorcode-nodes" style="position: absolute; top: 0; left: 0; width: 100%; height: ${this.options.height}px; z-index: 10;"></div>
                
                <!-- 刻度容器 -->
                <div class="colorcode-scale" style="position: absolute; top: ${this.options.height + 5}px; left: 0; width: 100%; height: 30px; font-size: 12px; color: #666;"></div>
                
                <!-- 右键菜单 -->
                <div class="colorcode-menu" style="position: fixed; background: white; border: 1px solid #ccc; border-radius: 4px; padding: 5px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.15); display: none; z-index: 1000;">
                    <div class="menu-item" style="padding: 5px 12px; cursor: pointer; hover: background-color: #f0f0f0;">删除节点</div>
                </div>
                
                <!-- 隐藏的颜色选择器 -->
                <input type="color" class="colorcode-picker" style="position: absolute; opacity: 0; pointer-events: none;" title="选择颜色" aria-label="颜色选择器" />
            </div>
        `;

        this.container.html(html);

        // 获取元素引用
        this.canvas = this.container.find('.colorcode-canvas')[0];
        this.ctx = this.canvas.getContext('2d');
        this.nodesContainer = this.container.find('.colorcode-nodes');
        this.scaleContainer = this.container.find('.colorcode-scale');
        this.contextMenu = this.container.find('.colorcode-menu');
        this.colorPicker = this.container.find('.colorcode-picker');
    }

    bindEvents() {
        const self = this;

        // 确保canvas元素存在后绑定事件
        if (this.canvas) {
            console.log('✅ Canvas元素存在，绑定双击事件');
            
            // 画布双击 - 添加节点
            $(this.canvas).on('dblclick', function(e) {
                console.log('🖱️ Canvas双击事件触发！', e);
                e.preventDefault();
                e.stopPropagation();
                self.addNodeAtPosition(e);
            });
            
            // 单击事件用于调试
            $(this.canvas).on('click', function(e) {
                console.log('🖱️ Canvas单击事件', e.offsetX, e.offsetY);
            });
            
            console.log('✅ Canvas事件绑定完成');
        } else {
            console.error('❌ Canvas元素不存在！');
        }

        // 全局鼠标事件（用于拖拽）
        $(document).on('mousemove', function(e) {
            self.handleMouseMove(e);
        }).on('mouseup', function() {
            self.handleMouseUp();
        });

        // 隐藏右键菜单
        $(document).on('click', function() {
            self.hideContextMenu();
        });

        // 右键菜单项点击
        this.container.find('.menu-item').on('click', function() {
            self.deleteNode();
        });

        // 颜色选择器变化
        this.colorPicker.on('change', function() {
            self.updateNodeColor();
        });

        // 阻止右键菜单事件冒泡
        this.contextMenu.on('click', function(e) {
            e.stopPropagation();
        });
    }

    render() {
        this.drawCanvas();
        this.updateNodes();
        this.updateScale();
    }

    drawCanvas() {
        // 清除画布
        this.ctx.clearRect(0, 0, this.options.width, this.options.height);

        // 创建渐变
        const gradient = this.ctx.createLinearGradient(0, 0, this.options.width, 0);
        const sortedNodes = [...this.nodes].sort((a, b) => a.position - b.position);

        sortedNodes.forEach(node => {
            gradient.addColorStop(node.position, node.color);
        });

        // 绘制渐变
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.options.width, this.options.height);

        // 绘制边框
        this.ctx.strokeStyle = '#000';
        this.ctx.strokeRect(0, 0, this.options.width, this.options.height);
    }

    updateNodes() {
        const self = this;
        this.nodesContainer.empty();

        this.nodes.forEach((node, index) => {
            const nodeElement = $(`
                <div class="colorcode-node" data-index="${index}"
                     style="position: absolute; 
                            left: ${node.position * this.options.width}px; 
                            top: ${this.options.height / 2}px;
                            width: 12px; 
                            height: 12px; 
                            background: ${node.isEndpoint ? '#ccc' : '#fff'}; 
                            border: 2px solid #000; 
                            border-radius: 50%; 
                            transform: translate(-50%, -50%);
                            cursor: ${node.isEndpoint ? 'not-allowed' : 'grab'};
                            z-index: 20;">
                </div>
            `);

            // 绑定节点事件
            nodeElement.on('mousedown', function(e) {
                if (!node.isEndpoint) {
                    self.startDragging(index, e);
                }
                e.preventDefault();
            });

            nodeElement.on('dblclick', function(e) {
                self.editNodeColor(index);
                e.stopPropagation();
            });

            nodeElement.on('contextmenu', function(e) {
                if (!node.isEndpoint) {
                    self.showContextMenu(index, e);
                }
                e.preventDefault();
            });

            this.nodesContainer.append(nodeElement);
        });
    }

    updateScale() {
        this.scaleContainer.empty();

        // 生成刻度
        const tickCount = 5;
        for (let i = 0; i <= tickCount; i++) {
            const position = i / tickCount;
            const value = this.options.minValue + (this.options.maxValue - this.options.minValue) * position;
            const x = position * this.options.width;

            const tick = $(`
                <div style="position: absolute; 
                           left: ${x}px; 
                           top: 0; 
                           width: 1px; 
                           height: 5px; 
                           background: #999;
                           transform: translateX(-50%);">
                </div>
                <div style="position: absolute; 
                           left: ${x}px; 
                           top: 8px; 
                           transform: translateX(-50%);
                           font-size: 10px;
                           color: #666;">
                    ${value.toFixed(0)}
                </div>
            `);

            this.scaleContainer.append(tick);
        }
    }

    addNodeAtPosition(event) {
        console.log('addNodeAtPosition 方法被调用');
        
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const position = x / this.options.width;

        console.log('点击信息:', {
            clientX: event.clientX,
            rectLeft: rect.left,
            x: x,
            canvasWidth: this.options.width,
            position: position
        });

        // 限制在边界内
        if (position <= 0.05 || position >= 0.95) {
            console.log('位置太靠近端点，跳过添加');
            return;
        }

        // 插值计算新节点颜色
        const color = this.interpolateColor(position);
        const newNode = {
            position: position,
            color: color,
            isEndpoint: false,
            id: Date.now()
        };

        console.log('新节点:', newNode);

        this.nodes.push(newNode);
        this.nodes.sort((a, b) => a.position - b.position);
        this.render();
        
        console.log('节点添加完成，当前节点数:', this.nodes.length);
    }

    startDragging(nodeIndex, event) {
        if (this.nodes[nodeIndex].isEndpoint) return;

        this.dragging = {
            nodeIndex: nodeIndex,
            startX: event.clientX
        };

        this.hideContextMenu();
        $('body').css('user-select', 'none');
    }

    handleMouseMove(event) {
        if (!this.dragging) return;

        const node = this.nodes[this.dragging.nodeIndex];
        if (node.isEndpoint) return;

        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        let newPosition = x / this.options.width;

        // 约束位置（不能超越相邻节点，不能到达端点）
        const sortedNodes = [...this.nodes].sort((a, b) => a.position - b.position);
        const currentIndex = sortedNodes.findIndex(n => this.nodes.indexOf(n) === this.dragging.nodeIndex);

        if (currentIndex > 0) {
            newPosition = Math.max(newPosition, sortedNodes[currentIndex - 1].position + 0.01);
        }
        if (currentIndex < sortedNodes.length - 1) {
            newPosition = Math.min(newPosition, sortedNodes[currentIndex + 1].position - 0.01);
        }

        // 更新节点位置
        this.nodes[this.dragging.nodeIndex].position = newPosition;
        this.render();
    }

    handleMouseUp() {
        if (this.dragging) {
            this.dragging = null;
            $('body').css('user-select', '');
        }
    }

    showContextMenu(nodeIndex, event) {
        if (this.nodes[nodeIndex].isEndpoint) return;

        this.contextMenuTarget = nodeIndex;
        this.contextMenu.css({
            left: event.clientX + 'px',
            top: event.clientY + 'px',
            display: 'block'
        });
    }

    hideContextMenu() {
        this.contextMenu.hide();
        this.contextMenuTarget = null;
    }

    deleteNode() {
        if (this.contextMenuTarget !== null && !this.nodes[this.contextMenuTarget].isEndpoint) {
            this.nodes.splice(this.contextMenuTarget, 1);
            this.render();
        }
        this.hideContextMenu();
    }

    editNodeColor(nodeIndex) {
        console.log('🎨 编辑节点颜色:', nodeIndex, this.nodes[nodeIndex]);
        this.colorPicker.data('nodeIndex', nodeIndex);
        this.colorPicker.val(this.nodes[nodeIndex].color);
        
        // 计算节点在页面中的绝对位置
        const containerOffset = this.container.offset();
        const nodeLeft = this.nodes[nodeIndex].position * this.options.width;
        
        console.log('📍 定位信息:', {
            containerOffset: containerOffset,
            nodeLeft: nodeLeft,
            canvasWidth: this.options.width,
            nodePosition: this.nodes[nodeIndex].position
        });
        
        // 显示颜色选择器并定位到节点位置
        this.colorPicker.css({
            position: 'fixed',
            left: (containerOffset.left + nodeLeft) + 'px',
            top: (containerOffset.top + this.options.height + 10) + 'px',
            opacity: 1,
            pointerEvents: 'auto',
            zIndex: 1000,
            width: '40px',
            height: '40px'
        }).show();
        
        // 触发颜色选择器
        setTimeout(() => {
            this.colorPicker[0].click();
        }, 50);
        
        console.log('🎨 颜色选择器已显示在位置:', this.colorPicker.css('left'), this.colorPicker.css('top'));
    }

    updateNodeColor() {
        const nodeIndex = this.colorPicker.data('nodeIndex');
        if (nodeIndex !== undefined) {
            const newColor = this.colorPicker.val();
            console.log('更新节点颜色:', nodeIndex, newColor);
            this.nodes[nodeIndex].color = newColor;
            this.render();
            
            // 隐藏颜色选择器
            this.colorPicker.css({
                opacity: 0,
                pointerEvents: 'none'
            });
        }
    }

    interpolateColor(position) {
        const sortedNodes = [...this.nodes].sort((a, b) => a.position - b.position);
        
        // 找到位置两侧的节点
        let leftNode = sortedNodes[0];
        let rightNode = sortedNodes[sortedNodes.length - 1];

        for (let i = 0; i < sortedNodes.length - 1; i++) {
            if (position >= sortedNodes[i].position && position <= sortedNodes[i + 1].position) {
                leftNode = sortedNodes[i];
                rightNode = sortedNodes[i + 1];
                break;
            }
        }

        // 线性插值
        const factor = (position - leftNode.position) / (rightNode.position - leftNode.position);
        const leftRGB = this.hexToRGB(leftNode.color);
        const rightRGB = this.hexToRGB(rightNode.color);

        const r = Math.round(leftRGB.r + factor * (rightRGB.r - leftRGB.r));
        const g = Math.round(leftRGB.g + factor * (rightRGB.g - leftRGB.g));
        const b = Math.round(leftRGB.b + factor * (rightRGB.b - leftRGB.b));

        return this.rgbToHex(r, g, b);
    }

    hexToRGB(hex) {
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        return { r, g, b };
    }

    rgbToHex(r, g, b) {
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    // 公共API方法
    getColorForValue(value) {
        const position = (value - this.options.minValue) / (this.options.maxValue - this.options.minValue);
        return this.interpolateColor(Math.max(0, Math.min(1, position)));
    }

    addNode(position, color) {
        if (position <= 0.05 || position >= 0.95) return false;
        
        const newNode = {
            position: Math.max(0.05, Math.min(0.95, position)),
            color: color || this.interpolateColor(position),
            isEndpoint: false,
            id: Date.now()
        };

        this.nodes.push(newNode);
        this.nodes.sort((a, b) => a.position - b.position);
        this.render();
        return true;
    }

    removeNode(nodeIndex) {
        if (nodeIndex >= 0 && nodeIndex < this.nodes.length && !this.nodes[nodeIndex].isEndpoint) {
            this.nodes.splice(nodeIndex, 1);
            this.render();
            return true;
        }
        return false;
    }

    setNodeColor(nodeIndex, color) {
        if (nodeIndex >= 0 && nodeIndex < this.nodes.length) {
            this.nodes[nodeIndex].color = color;
            this.render();
            return true;
        }
        return false;
    }

    exportConfig() {
        return {
            version: '1.0',
            timestamp: new Date().toISOString(),
            minValue: this.options.minValue,
            maxValue: this.options.maxValue,
            nodes: this.nodes.map((node, index) => ({
                index: index,
                position: node.position,
                color: node.color,
                value: this.options.minValue + (this.options.maxValue - this.options.minValue) * node.position,
                isEndpoint: node.isEndpoint || false
            }))
        };
    }

    importConfig(config) {
        try {
            if (config.nodes && Array.isArray(config.nodes)) {
                this.nodes = config.nodes.map(node => ({
                    position: node.position,
                    color: node.color,
                    isEndpoint: node.isEndpoint || false,
                    id: node.id || Date.now() + Math.random()
                }));

                if (config.minValue !== undefined) this.options.minValue = config.minValue;
                if (config.maxValue !== undefined) this.options.maxValue = config.maxValue;

                this.render();
                return true;
            }
        } catch (error) {
            console.error('导入配置失败:', error);
        }
        return false;
    }

    reset() {
        this.nodes = [...this.options.initialNodes];
        this.render();
    }

    destroy() {
        // 清理事件监听器
        $(document).off('mousemove mouseup');
        this.container.empty();
    }
}

// 如果在Node.js环境中，导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ColorCode;
} 