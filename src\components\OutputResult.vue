<template>
  <div class="output-container">
    <!-- 清空按钮 -->
    <div class="clear-button-container">
      <el-button 
        type="danger" 
        size="small" 
        @click="clearOutput"
        :disabled="outputHistory.length === 0"
      >
        <el-icon><Delete /></el-icon>
        Clean
      </el-button>
    </div>

    <!-- 输出历史记录 -->
    <div v-for="(item, index) in outputHistory" :key="index" class="output-item">
      <!-- 图片输出 -->
      <div v-if="item.type === 'image' && item.images && item.images.length > 0" class="result-images">
        <div v-for="(image, imgIndex) in item.images" :key="imgIndex" class="image-container">
          <img 
            :src="`data:image/${image.format};base64,${image.base64_data}`" 
            :alt="image.filename"
            class="result-image"
          />
          <div class="image-filename">{{ image.filename }}</div>
        </div>
      </div>

      <!-- 文本输出 -->
      <div v-if="item.type === 'text' && item.content" class="result-text">
        {{ item.content }}
      </div>

      <!-- 分隔线 -->
      <div v-if="index < outputHistory.length - 1" class="separator"></div>
    </div>

    <!-- 空状态 -->
    <div v-if="outputHistory.length === 0" class="empty-state">
      <el-empty description="暂无输出结果" />
    </div>
  </div>
</template>

<script setup>
import { ref, defineExpose } from 'vue';
import { Delete } from '@element-plus/icons-vue';

// 输出历史记录
const outputHistory = ref([]);

// 添加文本输出
const addTextOutput = (content) => {
  const outputItem = {
    id: Date.now(),
    type: 'text',
    content: content,
    timestamp: new Date()
  };
  
  // 将新结果添加到最上方
  outputHistory.value.unshift(outputItem);
};

// 添加图片输出
const addImageOutput = (images) => {
  if (Array.isArray(images) && images.length > 0) {
    const outputItem = {
      id: Date.now(),
      type: 'image',
      images: images,
      timestamp: new Date()
    };
    
    // 将新结果添加到最上方
    outputHistory.value.unshift(outputItem);
  } else {
    console.warn('addImageOutput: 传入的数据不是有效的图片数组');
  }
};

// 清空所有输出
const clearOutput = () => {
  outputHistory.value = [];
};


// 兼容旧版本的 updateOutput 方法
const updateOutput = (output) => {
  addTextOutput(output);
};

// 兼容旧版本的 updateIMGOutput 方法
const updateIMGOutput = (imageData) => {
  addImageOutput(imageData);
};

// 暴露方法给外部
defineExpose({
  updateOutput,
  updateIMGOutput,
  addTextOutput,
  addImageOutput,
  clearOutput
});
</script>

<style scoped>
.output-container {
  padding: 15px;
  max-height: 95%;
  overflow-y: auto;
}

.test-buttons {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.test-buttons .el-button {
  margin-right: 10px;
}

.output-item {
  margin-bottom: 20px;
}

.result-images {
  margin-bottom: 15px;
}

.image-container {
  margin-bottom: 15px;
  text-align: center;
}

.result-image {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto 10px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-filename {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  margin-bottom: 10px;
}

.result-text {
  white-space: pre-wrap;
  line-height: 1.6;
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #007bff;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.separator {
  height: 1px;
  background-color: #e9ecef;
  margin: 20px 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
}

.clear-button-container {
  text-align: right;
  margin-bottom: 15px;
}
</style>
