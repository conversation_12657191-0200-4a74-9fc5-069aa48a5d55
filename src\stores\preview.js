import { defineStore } from 'pinia';

export const usePreviewStore = defineStore('previewStore', {
  state: () => ({
    channelId: '',
    projectId: '',
    curveData: [],
    tabUrl: '',
    resize: { height: 600 },
    origin: '',
    projectTreeData: '',
    canvasList: '',
    activeTab: '',
    changeFileId: false,
    view: false,
    previewCurve: false,
    nodeInfo: {},
  }),
  actions: {
    setChannelId(type) {
      this.channelId = type;
    },
    setProjectId(data) {
      this.projectId = data;
    },
    onClickTab(tabUrl) {
      this.tabUrl = tabUrl;
    },
    setResize(resize) {
      this.resize = resize;
    },
    setcurveData(data) {
      this.curveData = data;
    },
    setOrigin(origin) {
      this.origin = origin;
    },
    setProjectTreeData(data) {
      this.projectTreeData = data;
    },
    setChangeFileId(data) {
      this.changeFileId = data;
    },
    setView(data) {
      this.view = data;
    },
    setCanvasList(data) {
      this.canvasList = data;
    },
    setActiveTab(data) {
      this.activeTab = data;
    },
    setPreviewCurve(data) {
      this.previewCurve = data;
    },
    setNodeInfo(data) {
      this.nodeInfo = data;
    }
  },
});
