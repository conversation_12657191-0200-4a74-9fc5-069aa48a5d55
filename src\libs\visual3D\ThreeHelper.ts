import * as THREE from 'three';
import { SceneManager, CameraManager, RendererManager, type CameraType } from './core';
import { MeshBuilder, LightBuilder } from './objects';
import { ModelLoader, TextureManager } from './loaders';
import { PointerController, EnhancedOrbitControls } from './controls';
import { ThreeUtils } from './utils';

type ThreeConfig = {
  debugGUI?: boolean;
  antialias?: boolean;
  cameraType?: CameraType;
  shadowEnabled?: boolean;
  defaultLights?: boolean;
  transparentBackground?: boolean;
};

export class ThreeHelper {
  private sceneManager: SceneManager;
  private cameraManager: CameraManager;
  private rendererManager: RendererManager;
  private pointerController?: PointerController;
  private enhancedOrbitControls ?: EnhancedOrbitControls;
  private animationCallbacks: Set<(delta: number) => void> = new Set();
  private debugGUI: any = null;

  constructor(
    private canvas: HTMLCanvasElement,
    config: ThreeConfig = {}
  ) {
    // 初始化核心模块
    this.sceneManager = new SceneManager();
    this.cameraManager = new CameraManager(config.cameraType || 'perspective');
    this.rendererManager = new RendererManager(canvas, {
      antialias: config.antialias,
      shadowMap: config.shadowEnabled,
      alpha: config.transparentBackground
    });

    // 基础配置
    if (config.defaultLights !== false) this.setupDefaultLights();
    if (config.debugGUI) this.initDebugGUI();
  }

  /* 核心功能 */
  start() {
    this.setupRenderLoop();
    return this;
  }

  private setupRenderLoop() {
    this.rendererManager.startRenderLoop(
      this.sceneManager.scene,
      this.cameraManager.camera,
      () => {
        const clock = new THREE.Clock();
        this.animationCallbacks.forEach(cb => cb(clock.getDelta()));
      }
    );
  }

  /* 场景管理 */
  add(...objects: THREE.Object3D[]): this {
    objects.forEach(obj => this.sceneManager.addObject(obj));
    return this;
  }

  remove(...objects: THREE.Object3D[]): this {
    objects.forEach(obj => this.sceneManager.removeObject(obj));
    return this;
  }

  /* 资源加载 */
  async loadModelGLB(url: string): Promise<THREE.Group> {
    return ModelLoader.getInstance().loadGLB(url);
  }

  async loadModelOBJ(url: string): Promise<THREE.Group> {
    return ModelLoader.getInstance().loadOBJ(url);
  }

  /* 资源纹理 */
  async loadTexture(url: string, config?: any): Promise<THREE.Texture> {
    return TextureManager.getInstance().load(url, config);
  }

  /* 快捷创建 */
  create = {
    box: (size = 1, color = 0x00ff00) => MeshBuilder.createBox(size, color),
    sphere: (radius = 1, color = 0xff0000) => MeshBuilder.createSphere(radius, color),
    ambientLight: (color?: THREE.ColorRepresentation, intensity?: number) => LightBuilder.createAmbient(color, intensity),
    directionalLight: (color?: THREE.ColorRepresentation, intensity?: number) => LightBuilder.createDirectional(color, intensity)
  };

  /* 交互系统 */
  enableInteraction(handlers?: {
    onClick?: (obj: THREE.Object3D) => void;
    onDrag?: (delta: THREE.Vector3, obj: THREE.Object3D) => void;
  }): this {
    this.pointerController = new PointerController(
      this.sceneManager.scene,
      this.cameraManager.camera,
      this.rendererManager.renderer,
      handlers // 现在类型匹配了
    );

    this.enhancedOrbitControls = new EnhancedOrbitControls(
      this.cameraManager.camera,
      this.rendererManager.renderer,
      {
        enableDamping: false,
      }
    );
    return this;
  }

  /* 动画系统 */
  animate(callback: (delta: number) => void): () => void {
    this.animationCallbacks.add(callback);
    return () => this.animationCallbacks.delete(callback);
  }

  /* 相机控制 */
  lookAt(target: THREE.Vector3): this {
    this.cameraManager.camera.lookAt(target);
    return this;
  }

  setCameraPosition(x: number, y: number, z: number): this {
    this.cameraManager.setPosition(x, y, z);
    return this;
  }

  /* 调试工具 */
  private async initDebugGUI() {
    try {
      const { GUI } = await import('lil-gui');
      this.debugGUI = new GUI();
      const cameraFolder = this.debugGUI.addFolder('Camera');
      cameraFolder.add(this.camera.position, 'x', -10, 10).name('Position X');
      cameraFolder.add(this.camera.position, 'y', -10, 10).name('Position Y');
      cameraFolder.add(this.camera.position, 'z', -10, 10).name('Position Z');
      cameraFolder.open();
    } catch (error) {
      console.warn('lil-gui 未安装，调试功能不可用');
    }
  }

  /* 工具方法 */
  screenToWorld(position: { x: number; y: number }, distance = 10) {
    return ThreeUtils.screenToWorld(position, this.cameraManager.camera, distance);
  }

  /* 生命周期 */
  dispose() {
    this.rendererManager.dispose();
    this.sceneManager.dispose();
    this.pointerController?.dispose();
    this.enhancedOrbitControls?.dispose();
    this.debugGUI?.destroy();
  }

  /* 获取原始对象 */
  get scene() { return this.sceneManager.scene; }
  get camera() { return this.cameraManager.camera; }
  get renderer() { return this.rendererManager.renderer; }
  get orbitControls() { return this.enhancedOrbitControls; }

  private setupDefaultLights() {
    var ambientLight = this.create.ambientLight(0xffffff, 0.5);
    var directionalLight = this.create.directionalLight(0xffffff, 1);
    directionalLight.position.set(5, 5, 5);
    
    this.add(
      ambientLight,
      directionalLight,
    );
  }

  /**
   * 调整相机位置和朝向，使模型完全显示在视野内
   * @param camera 相机
   * @param model 模型
   */
  adjustCameraToModel(this: ThreeHelper, model: THREE.Group){
    let camera = this.camera;
    model.updateWorldMatrix(true, true);
    // 计算模型的包围盒
    const box = new THREE.Box3().setFromObject(model);
    const center = new THREE.Vector3();
    const size = new THREE.Vector3();
  
    box.getCenter(center); // 获取中心点
    box.getSize(size);     // 获取尺寸
  
    // 计算包围盒对角线长度
    const diagonal = size.length();
  
    // 调整相机位置和朝向
    if (camera instanceof THREE.PerspectiveCamera) {
      // 透视相机调整
      const fov = camera.fov * (Math.PI / 180);
      const distance = Math.abs(diagonal / (2 * Math.tan(fov / 2)));
  
      // 设置相机位置，假设沿z轴方向
      camera.position.copy(center).add(new THREE.Vector3(0, 0, distance));
      camera.lookAt(center);
  
      // 更新近远平面
      camera.near = diagonal / 100;
      camera.far = diagonal * 100;
      camera.updateProjectionMatrix();
    } else if (camera instanceof THREE.OrthographicCamera) {
      // 正交相机调整
    }
  }
}