<template>
  <el-tabs type="border-card">
    <el-tab-pane label="Depth Pairs">
      <depth-pairs ref="depthPairsRef" />
    </el-tab-pane>
    <el-tab-pane label="Output">
      <out-put ref="outputRef" @done="done" />
    </el-tab-pane>
  </el-tabs>
</template>
<script setup>
import { onMounted, ref, nextTick, onUnmounted } from "vue";
import { useProjectStore } from "@/stores/project";
import DepthPairs from "./depthPairs.vue";
import OutPut from "./outPut.vue";
const emit = defineEmits(["update", "admTabClose"]);
const projectStore = useProjectStore();
const openAdm = ref(false);
function handleOpenAdm(channelId) {
  openAdm.value = true;
  nextTick(() => {
    outputRef.value.initProjectTreeData();
    depthPairsRef.value.initDepthPairs();
  });
}
function done() {
  emit("callChildBMethod", projectStore.projectId);
  openAdm.value = false;
}
defineExpose({
  handleOpenAdm,
});
onUnmounted(() => {
  emit("admTabClose");
});
</script>
<style lang="less" scoped>
.el-tabs {
  margin: 10px;
  height: calc(100% - 22px);
}
</style>
