import * as THREE from 'three';

type ShadowConfig = {
  mapSize?: number;
  radius?: number;
  bias?: number;
};

export class LightBuilder {
  static createAmbient(
    color: THREE.ColorRepresentation = 0xffffff,
    intensity: number = 0.5
  ): THREE.AmbientLight {
    return new THREE.AmbientLight(color, intensity);
  }

  static createDirectional(
    color: THREE.ColorRepresentation = 0xffffff,
    intensity: number = 1,
    shadowConfig?: ShadowConfig
  ): THREE.DirectionalLight {
    const light = new THREE.DirectionalLight(color, intensity);
    light.castShadow = true;
    light.shadow.mapSize.width = shadowConfig?.mapSize ?? 2048;
    light.shadow.mapSize.height = shadowConfig?.mapSize ?? 2048;
    light.shadow.radius = shadowConfig?.radius ?? 3;
    light.shadow.bias = shadowConfig?.bias ?? -0.0001;
    return light;
  }

  static createPoint(
    color: THREE.ColorRepresentation = 0xffffff,
    intensity: number = 1,
    distance: number = 10,
    decay: number = 2
  ): THREE.PointLight {
    const light = new THREE.PointLight(color, intensity, distance, decay);
    light.castShadow = true;
    light.shadow.mapSize.width = 1024;
    light.shadow.mapSize.height = 1024;
    return light;
  }

  static createSpot(
    color: THREE.ColorRepresentation = 0xffffff,
    intensity: number = 1,
    distance: number = 10,
    angle: number = Math.PI / 4,
    penumbra: number = 0.1
  ): THREE.SpotLight {
    const light = new THREE.SpotLight(color, intensity, distance, angle, penumbra);
    light.castShadow = true;
    light.shadow.mapSize.width = 1024;
    light.shadow.mapSize.height = 1024;
    return light;
  }
}