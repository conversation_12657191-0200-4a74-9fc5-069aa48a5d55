// import Dexie, { type EntityTable } from 'dexie'
import { loadChannelChunk, loadChannelInfo } from '@/api/curvePreview.js'
import { ref } from "vue";
import { usePreviewStore } from "@/stores/preview.js";
import { curveData } from './channelData.js'
import { CurvePreview } from '@/api/curve.js'

// id, name, unit, alias, startIndex, endIndex, samples
interface Channel {
  id: string;
  name: string;
  unit: string;
  alias: string;
  indexType: number;
  indexUnit: string;
  startIndex: number;
  endIndex: number;
  deltIndex: number;
  samples: number;
  axis: number;
  axisDataPoints: number;
}

interface ChannelData {
  id: string;
  indexData: number[];
  curveData: number[];
}

export const preview: any = usePreviewStore();
const channel = ref()
const channelData = ref()
const loadingFinished = ref(true)
// const dexieChannelFinished = ref(false)
const dexieChannelDataFinished = ref(false)
let chunk = 1000

// 根据channelId 初始化channel和channelData
export async function initialChannelDexie(channelId: bigint) {
  loadingFinished.value = true
  dexieChannelDataFinished.value = false
  // 初始化channelData
  console.log(channel.value, 'channel.value')
  if (channel.value.indexData.length > 0 || channel.value.curveData.length > 0) {
    channelData.value = {
      id: channel.value.id,
      indexData: channel.value.indexData,
      curveData: channel.value.curveData,
    }
  }
  
  // channelData.value = {
  //   id: channelId,
  //   indexData: new Array(channel.value.samples).fill(''),
  //   curveData: new Array(channel.value.samples).fill('')
  // }
}

export async function loadChannel(id: bigint) {
  let index:string = String(id);
  let result
  
  // 获取曲线信息
  const { data } = await CurvePreview({
    wellId: preview.nodeInfo.wellNodeId,
    datasetId: preview.nodeInfo.datasetId,
    id: preview.nodeInfo.nodeDataId,
  })

  result = {
    id: data.id,
    name: data.name,
    unit: data.unit,
    alias: '',
    indexType: data.index.indexType,
    indexUnit: data.index.indexUnit,
    startIndex: data.index.startIndex,
    endIndex: data.index.endIndex,
    deltIndex: data.index.deltIndex,
    samples: data.index.samples,
    axis: data.index.axis,
    axisDataPoints: data.index.axisDataPoints,
    indexData: data.index.indexValue,
    curveData: data.data,
  }
  channel.value = result
  return result
}

// 读取sampleStart到sampleEnd区间的曲线数据
export async function loadChannelData(channelId: bigint, sampleStart: number, sampleEnd: number, axis: number) {
  if (!dexieChannelDataFinished.value) {
      // 首先看Dexie里有没有存储该曲线
      // let result = await getChannelData(channelId)
      let result = null
      if (result) {
          dexieChannelDataFinished.value = true
          channelData.value = result
      } else {
        // 再填充曲线
        await complementCurve(channelId, sampleStart, sampleEnd, axis)
      }
  }
}

export function checkDexieChannelDataFinished() {
  return dexieChannelDataFinished.value
}

async function complementCurve(channelId: bigint, sampleStart: number, sampleEnd: number, axis: number) {
  // 检查channelData缓存是否有连续chunk可供显示
  let startChunk = Math.floor(sampleStart/chunk)
  let endChunk = Math.ceil(sampleEnd/chunk)+1 // 多读一段避免空白
  let complete = true
  let index = 0
  for (let i=startChunk;i<=endChunk;i++) {
      index = Math.min(channel.value.samples-1, i*chunk)-1
      if (channelData.value.curveData[index] == '') {
          complete = false
          break
      }
  }

  // 若没有，则需要调用后端接口填充，这里强制调用
  complete = false
  loadingFinished.value = true
  if (!complete && loadingFinished.value) {
      loadingFinished.value = false
      channelData.value.indexData = channel.value.indexData;
      channelData.value.curveData = channel.value.curveData;

      // 每次填充后都检查是否完整
      // await checkChannelDataComplete(channel.value)
      loadingFinished.value = true
  }
}

export function getChunks() {
  return chunk
}

export function setChunks(chunk: number) {
  // this.chunk = chunk
}

export function getCurrentChunkChannelData() {
  return channelData.value
}


export type { Channel };
export type { ChannelData };
