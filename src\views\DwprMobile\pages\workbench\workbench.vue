<template>
  <div class="workbench-view">
    <div class="workbench-header">
      <Title name="Workbench" :showIcon="false" />
      <div class="content-card">
        <WorkbenchCard />
      </div>
    </div>
    
    <div class="content-list">
      <div class="title">Notification</div>
      <div class="list">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh"
        pulling-text="Pull to refresh..."
        loosing-text="Release to refresh..."
        loading-text="Refreshing..."
        success-text="Refresh successful"
        >
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="No More"
            loading-text="Loading..."
            @load="onLoad"
          >
            <NotifyList :list="list" />
              <!-- <van-cell v-for="item in list" :key="item" :title="item" /> -->
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

import Title from "@/views/DwprMobile/components/common/Title.vue";
import WorkbenchCard from "@/views/DwprMobile/components/workbench/WorkbenchCard.vue";
import NotifyList from "@/views/DwprMobile/components/workbench/NotifyList.vue";
const list = ref<any>([]);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);

const onLoad = () => {
  setTimeout(() => {
    if (refreshing.value) {
      list.value = [];
      refreshing.value = false;
    }

    for (let i = 0; i < 10; i++) {
      list.value.push(list.value.length + 1);
    }
    loading.value = false;

    if (list.value.length >= 40) {
      finished.value = true;
    }
  }, 1000);
};

const onRefresh = () => {
  // 清空列表数据
  finished.value = false;

  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  onLoad();
};
</script>

<style scoped lang="less">
.workbench-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.workbench-header {
  .content-card {
    padding: .75rem;
  }
}
.title {
    font-weight: 500;
    font-size: 1.25rem;
    color: #4472C4;
    background: #fff;
    padding: 0 .375rem;
}
.content-list {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0 .75rem;
  margin-bottom: 1.625rem;
  min-height: 0;
  .list {
    flex: 1;
    padding: 0 .375rem;
    background: #fff;
    overflow-y: auto;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none;
    -ms-overflow-style: none;
    
    &::-webkit-scrollbar {
        display: none;
    }
  }
}
</style>
