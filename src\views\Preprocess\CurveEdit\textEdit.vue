<template>
  <el-dialog
    width="400px"
    v-model="visible"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    append-to-body
    @close="close()"
    title="Curve Data Edit Text"
  >
    <el-input
      ref="textarea"
      type="textarea"
      :autosize="{ minRows: 20, maxRows: 20 }"
      v-model="textarea2"
      @input="(value) => handleInput(value)"
    >
    </el-input>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleSave()">Save</el-button>
        <el-button @click="close()">Cancel</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="textEdit">
import { onMounted, ref, computed, markRaw, onUnmounted } from "vue";
import { useEditCurveStore } from "@/stores/editCurveStore";
const store = useEditCurveStore();
const emit = defineEmits(["update", "done"]);
const textarea2 = ref("");
const visible = ref(false);
let originalData = [];
function openTextEdit(data, type) {
  const deepCopy = JSON.parse(JSON.stringify(data));
  if (type === 1) {
    textarea2.value = deepCopy.join("\n");
    originalData = deepCopy;
  } else {
    const reversedData = deepCopy.reverse();
    textarea2.value = reversedData.join("\n");
    originalData = reversedData;
  }
  visible.value = true;
}
// 新增输入处理函数
function handleInput(currentValue) {
  const newLines = currentValue.split("\n");
  let currentMin = null;
  let currentMax = null;

  newLines.forEach((line, index) => {
    if (line !== originalData[index]) {
      originalData[index] = line;

      // 记录当前修改的index范围
      if (currentMin === null || index < currentMin) currentMin = index;
      if (currentMax === null || index > currentMax) currentMax = index;
    }
  });

  if (currentMin !== null) {
    store.setMinIndex(
      store.minIndex === 0 ? currentMin : Math.min(store.minIndex, currentMin)
    );
    store.setMaxIndex(
      store.maxIndex === 0 ? currentMax : Math.max(store.maxIndex, currentMax)
    );
  }
}
function handleSave() {
  emit("done", textarea2.value.split("\n"));
  visible.value = false;
}

function close() {
  visible.value = false;
}

defineExpose({
  openTextEdit,
});
</script>
